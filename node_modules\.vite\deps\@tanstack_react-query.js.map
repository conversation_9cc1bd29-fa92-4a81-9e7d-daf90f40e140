{"version": 3, "sources": ["../../@tanstack/query-core/src/subscribable.ts", "../../@tanstack/query-core/src/utils.ts", "../../@tanstack/query-core/src/focusManager.ts", "../../@tanstack/query-core/src/onlineManager.ts", "../../@tanstack/query-core/src/thenable.ts", "../../@tanstack/query-core/src/retryer.ts", "../../@tanstack/query-core/src/notifyManager.ts", "../../@tanstack/query-core/src/removable.ts", "../../@tanstack/query-core/src/query.ts", "../../@tanstack/query-core/src/queryCache.ts", "../../@tanstack/query-core/src/mutation.ts", "../../@tanstack/query-core/src/mutationCache.ts", "../../@tanstack/query-core/src/infiniteQueryBehavior.ts", "../../@tanstack/query-core/src/queryClient.ts", "../../@tanstack/query-core/src/queryObserver.ts", "../../@tanstack/query-core/src/queriesObserver.ts", "../../@tanstack/query-core/src/infiniteQueryObserver.ts", "../../@tanstack/query-core/src/mutationObserver.ts", "../../@tanstack/query-core/src/hydration.ts", "../../@tanstack/query-core/src/types.ts", "../../@tanstack/react-query/src/useQueries.ts", "../../@tanstack/react-query/src/QueryClientProvider.tsx", "../../@tanstack/react-query/src/isRestoring.ts", "../../@tanstack/react-query/src/QueryErrorResetBoundary.tsx", "../../@tanstack/react-query/src/errorBoundaryUtils.ts", "../../@tanstack/react-query/src/utils.ts", "../../@tanstack/react-query/src/suspense.ts", "../../@tanstack/react-query/src/useBaseQuery.ts", "../../@tanstack/react-query/src/useQuery.ts", "../../@tanstack/react-query/src/useSuspenseQuery.ts", "../../@tanstack/react-query/src/useSuspenseInfiniteQuery.ts", "../../@tanstack/react-query/src/useSuspenseQueries.ts", "../../@tanstack/react-query/src/usePrefetchQuery.tsx", "../../@tanstack/react-query/src/usePrefetchInfiniteQuery.tsx", "../../@tanstack/react-query/src/queryOptions.ts", "../../@tanstack/react-query/src/infiniteQueryOptions.ts", "../../@tanstack/react-query/src/HydrationBoundary.tsx", "../../@tanstack/react-query/src/useIsFetching.ts", "../../@tanstack/react-query/src/useMutationState.ts", "../../@tanstack/react-query/src/useMutation.ts", "../../@tanstack/react-query/src/useInfiniteQuery.ts"], "sourcesContent": ["export class Subscribable<TListener extends Function> {\n  protected listeners = new Set<TListener>()\n\n  constructor() {\n    this.subscribe = this.subscribe.bind(this)\n  }\n\n  subscribe(listener: TListener): () => void {\n    this.listeners.add(listener)\n\n    this.onSubscribe()\n\n    return () => {\n      this.listeners.delete(listener)\n      this.onUnsubscribe()\n    }\n  }\n\n  hasListeners(): boolean {\n    return this.listeners.size > 0\n  }\n\n  protected onSubscribe(): void {\n    // Do nothing\n  }\n\n  protected onUnsubscribe(): void {\n    // Do nothing\n  }\n}\n", "import type {\n  DefaultError,\n  Enabled,\n  FetchStatus,\n  MutationKey,\n  MutationStatus,\n  QueryFunction,\n  QueryKey,\n  QueryOptions,\n  StaleTime,\n} from './types'\nimport type { Mutation } from './mutation'\nimport type { FetchOptions, Query } from './query'\n\n// TYPES\n\nexport interface QueryFilters<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  /**\n   * Filter to active queries, inactive queries or all queries\n   */\n  type?: QueryTypeFilter\n  /**\n   * Match query key exactly\n   */\n  exact?: boolean\n  /**\n   * Include queries matching this predicate function\n   */\n  predicate?: (query: Query<TQueryFnData, TError, TData, TQueryKey>) => boolean\n  /**\n   * Include queries matching this query key\n   */\n  queryKey?: TQueryKey\n  /**\n   * Include or exclude stale queries\n   */\n  stale?: boolean\n  /**\n   * Include queries matching their fetchStatus\n   */\n  fetchStatus?: FetchStatus\n}\n\nexport interface MutationFilters<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = unknown,\n  TContext = unknown,\n> {\n  /**\n   * Match mutation key exactly\n   */\n  exact?: boolean\n  /**\n   * Include mutations matching this predicate function\n   */\n  predicate?: (\n    mutation: Mutation<TData, TError, TVariables, TContext>,\n  ) => boolean\n  /**\n   * Include mutations matching this mutation key\n   */\n  mutationKey?: MutationKey\n  /**\n   * Filter by mutation status\n   */\n  status?: MutationStatus\n}\n\nexport type Updater<TInput, TOutput> = TOutput | ((input: TInput) => TOutput)\n\nexport type QueryTypeFilter = 'all' | 'active' | 'inactive'\n\n// UTILS\n\nexport const isServer = typeof window === 'undefined' || 'Deno' in globalThis\n\nexport function noop(): void\nexport function noop(): undefined\nexport function noop() {}\n\nexport function functionalUpdate<TInput, TOutput>(\n  updater: Updater<TInput, TOutput>,\n  input: TInput,\n): TOutput {\n  return typeof updater === 'function'\n    ? (updater as (_: TInput) => TOutput)(input)\n    : updater\n}\n\nexport function isValidTimeout(value: unknown): value is number {\n  return typeof value === 'number' && value >= 0 && value !== Infinity\n}\n\nexport function timeUntilStale(updatedAt: number, staleTime?: number): number {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0)\n}\n\nexport function resolveStaleTime<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  staleTime: undefined | StaleTime<TQueryFnData, TError, TData, TQueryKey>,\n  query: Query<TQueryFnData, TError, TData, TQueryKey>,\n): number | undefined {\n  return typeof staleTime === 'function' ? staleTime(query) : staleTime\n}\n\nexport function resolveEnabled<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  enabled: undefined | Enabled<TQueryFnData, TError, TData, TQueryKey>,\n  query: Query<TQueryFnData, TError, TData, TQueryKey>,\n): boolean | undefined {\n  return typeof enabled === 'function' ? enabled(query) : enabled\n}\n\nexport function matchQuery(\n  filters: QueryFilters,\n  query: Query<any, any, any, any>,\n): boolean {\n  const {\n    type = 'all',\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale,\n  } = filters\n\n  if (queryKey) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false\n    }\n  }\n\n  if (type !== 'all') {\n    const isActive = query.isActive()\n    if (type === 'active' && !isActive) {\n      return false\n    }\n    if (type === 'inactive' && isActive) {\n      return false\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false\n  }\n\n  if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n    return false\n  }\n\n  if (predicate && !predicate(query)) {\n    return false\n  }\n\n  return true\n}\n\nexport function matchMutation(\n  filters: MutationFilters,\n  mutation: Mutation<any, any>,\n): boolean {\n  const { exact, status, predicate, mutationKey } = filters\n  if (mutationKey) {\n    if (!mutation.options.mutationKey) {\n      return false\n    }\n    if (exact) {\n      if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n        return false\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false\n    }\n  }\n\n  if (status && mutation.state.status !== status) {\n    return false\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false\n  }\n\n  return true\n}\n\nexport function hashQueryKeyByOptions<TQueryKey extends QueryKey = QueryKey>(\n  queryKey: TQueryKey,\n  options?: Pick<QueryOptions<any, any, any, any>, 'queryKeyHashFn'>,\n): string {\n  const hashFn = options?.queryKeyHashFn || hashKey\n  return hashFn(queryKey)\n}\n\n/**\n * Default query & mutation keys hash function.\n * Hashes the value into a stable hash.\n */\nexport function hashKey(queryKey: QueryKey | MutationKey): string {\n  return JSON.stringify(queryKey, (_, val) =>\n    isPlainObject(val)\n      ? Object.keys(val)\n          .sort()\n          .reduce((result, key) => {\n            result[key] = val[key]\n            return result\n          }, {} as any)\n      : val,\n  )\n}\n\n/**\n * Checks if key `b` partially matches with key `a`.\n */\nexport function partialMatchKey(a: QueryKey, b: QueryKey): boolean\nexport function partialMatchKey(a: any, b: any): boolean {\n  if (a === b) {\n    return true\n  }\n\n  if (typeof a !== typeof b) {\n    return false\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some((key) => !partialMatchKey(a[key], b[key]))\n  }\n\n  return false\n}\n\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\nexport function replaceEqualDeep<T>(a: unknown, b: T): T\nexport function replaceEqualDeep(a: any, b: any): any {\n  if (a === b) {\n    return a\n  }\n\n  const array = isPlainArray(a) && isPlainArray(b)\n\n  if (array || (isPlainObject(a) && isPlainObject(b))) {\n    const aItems = array ? a : Object.keys(a)\n    const aSize = aItems.length\n    const bItems = array ? b : Object.keys(b)\n    const bSize = bItems.length\n    const copy: any = array ? [] : {}\n\n    let equalItems = 0\n\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i]\n      if (\n        ((!array && aItems.includes(key)) || array) &&\n        a[key] === undefined &&\n        b[key] === undefined\n      ) {\n        copy[key] = undefined\n        equalItems++\n      } else {\n        copy[key] = replaceEqualDeep(a[key], b[key])\n        if (copy[key] === a[key] && a[key] !== undefined) {\n          equalItems++\n        }\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy\n  }\n\n  return b\n}\n\n/**\n * Shallow compare objects.\n */\nexport function shallowEqualObjects<T extends Record<string, any>>(\n  a: T,\n  b: T | undefined,\n): boolean {\n  if (!b || Object.keys(a).length !== Object.keys(b).length) {\n    return false\n  }\n\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false\n    }\n  }\n\n  return true\n}\n\nexport function isPlainArray(value: unknown) {\n  return Array.isArray(value) && value.length === Object.keys(value).length\n}\n\n// Copied from: https://github.com/jonschlinkert/is-plain-object\n// eslint-disable-next-line @typescript-eslint/no-wrapper-object-types\nexport function isPlainObject(o: any): o is Object {\n  if (!hasObjectPrototype(o)) {\n    return false\n  }\n\n  // If has no constructor\n  const ctor = o.constructor\n  if (ctor === undefined) {\n    return true\n  }\n\n  // If has modified prototype\n  const prot = ctor.prototype\n  if (!hasObjectPrototype(prot)) {\n    return false\n  }\n\n  // If constructor does not have an Object-specific method\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false\n  }\n\n  // Handles Objects created by Object.create(<arbitrary prototype>)\n  if (Object.getPrototypeOf(o) !== Object.prototype) {\n    return false\n  }\n\n  // Most likely a plain Object\n  return true\n}\n\nfunction hasObjectPrototype(o: any): boolean {\n  return Object.prototype.toString.call(o) === '[object Object]'\n}\n\nexport function sleep(timeout: number): Promise<void> {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeout)\n  })\n}\n\nexport function replaceData<\n  TData,\n  TOptions extends QueryOptions<any, any, any, any>,\n>(prevData: TData | undefined, data: TData, options: TOptions): TData {\n  if (typeof options.structuralSharing === 'function') {\n    return options.structuralSharing(prevData, data) as TData\n  } else if (options.structuralSharing !== false) {\n    if (process.env.NODE_ENV !== 'production') {\n      try {\n        return replaceEqualDeep(prevData, data)\n      } catch (error) {\n        console.error(\n          `Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`,\n        )\n      }\n    }\n    // Structurally share data between prev and new data if needed\n    return replaceEqualDeep(prevData, data)\n  }\n  return data\n}\n\nexport function keepPreviousData<T>(\n  previousData: T | undefined,\n): T | undefined {\n  return previousData\n}\n\nexport function addToEnd<T>(items: Array<T>, item: T, max = 0): Array<T> {\n  const newItems = [...items, item]\n  return max && newItems.length > max ? newItems.slice(1) : newItems\n}\n\nexport function addToStart<T>(items: Array<T>, item: T, max = 0): Array<T> {\n  const newItems = [item, ...items]\n  return max && newItems.length > max ? newItems.slice(0, -1) : newItems\n}\n\nexport const skipToken = Symbol()\nexport type SkipToken = typeof skipToken\n\nexport function ensureQueryFn<\n  TQueryFnData = unknown,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: {\n    queryFn?: QueryFunction<TQueryFnData, TQueryKey> | SkipToken\n    queryHash?: string\n  },\n  fetchOptions?: FetchOptions<TQueryFnData>,\n): QueryFunction<TQueryFnData, TQueryKey> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (options.queryFn === skipToken) {\n      console.error(\n        `Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`,\n      )\n    }\n  }\n\n  // if we attempt to retry a fetch that was triggered from an initialPromise\n  // when we don't have a queryFn yet, we can't retry, so we just return the already rejected initialPromise\n  // if an observer has already mounted, we will be able to retry with that queryFn\n  if (!options.queryFn && fetchOptions?.initialPromise) {\n    return () => fetchOptions.initialPromise!\n  }\n\n  if (!options.queryFn || options.queryFn === skipToken) {\n    return () =>\n      Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`))\n  }\n\n  return options.queryFn\n}\n", "import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype Listener = (focused: boolean) => void\n\ntype SetupFn = (\n  setFocused: (focused?: boolean) => void,\n) => (() => void) | undefined\n\nexport class FocusManager extends Subscribable<Listener> {\n  #focused?: boolean\n  #cleanup?: () => void\n\n  #setup: SetupFn\n\n  constructor() {\n    super()\n    this.#setup = (onFocus) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus()\n        // Listen to visibilitychange\n        window.addEventListener('visibilitychange', listener, false)\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener)\n        }\n      }\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.()\n      this.#cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.#setup = setup\n    this.#cleanup?.()\n    this.#cleanup = setup((focused) => {\n      if (typeof focused === 'boolean') {\n        this.setFocused(focused)\n      } else {\n        this.onFocus()\n      }\n    })\n  }\n\n  setFocused(focused?: boolean): void {\n    const changed = this.#focused !== focused\n    if (changed) {\n      this.#focused = focused\n      this.onFocus()\n    }\n  }\n\n  onFocus(): void {\n    const isFocused = this.isFocused()\n    this.listeners.forEach((listener) => {\n      listener(isFocused)\n    })\n  }\n\n  isFocused(): boolean {\n    if (typeof this.#focused === 'boolean') {\n      return this.#focused\n    }\n\n    // document global can be unavailable in react native\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    return globalThis.document?.visibilityState !== 'hidden'\n  }\n}\n\nexport const focusManager = new FocusManager()\n", "import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype Listener = (online: boolean) => void\ntype SetupFn = (setOnline: Listener) => (() => void) | undefined\n\nexport class OnlineManager extends Subscribable<Listener> {\n  #online = true\n  #cleanup?: () => void\n\n  #setup: SetupFn\n\n  constructor() {\n    super()\n    this.#setup = (onOnline) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const onlineListener = () => onOnline(true)\n        const offlineListener = () => onOnline(false)\n        // Listen to online\n        window.addEventListener('online', onlineListener, false)\n        window.addEventListener('offline', offlineListener, false)\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('online', onlineListener)\n          window.removeEventListener('offline', offlineListener)\n        }\n      }\n\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.()\n      this.#cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.#setup = setup\n    this.#cleanup?.()\n    this.#cleanup = setup(this.setOnline.bind(this))\n  }\n\n  setOnline(online: boolean): void {\n    const changed = this.#online !== online\n\n    if (changed) {\n      this.#online = online\n      this.listeners.forEach((listener) => {\n        listener(online)\n      })\n    }\n  }\n\n  isOnline(): boolean {\n    return this.#online\n  }\n}\n\nexport const onlineManager = new OnlineManager()\n", "/**\n * Thenable types which matches <PERSON><PERSON>'s types for promises\n *\n * <PERSON><PERSON> seemingly uses `.status`, `.value` and `.reason` properties on a promises to optimistically unwrap data from promises\n *\n * @see https://github.com/facebook/react/blob/main/packages/shared/ReactTypes.js#L112-L138\n * @see https://github.com/facebook/react/blob/4f604941569d2e8947ce1460a0b2997e835f37b9/packages/react-debug-tools/src/ReactDebugHooks.js#L224-L227\n */\n\ninterface Fulfilled<T> {\n  status: 'fulfilled'\n  value: T\n}\ninterface Rejected {\n  status: 'rejected'\n  reason: unknown\n}\ninterface Pending<T> {\n  status: 'pending'\n\n  /**\n   * Resolve the promise with a value.\n   * Will remove the `resolve` and `reject` properties from the promise.\n   */\n  resolve: (value: T) => void\n  /**\n   * Reject the promise with a reason.\n   * Will remove the `resolve` and `reject` properties from the promise.\n   */\n  reject: (reason: unknown) => void\n}\n\nexport type FulfilledThenable<T> = Promise<T> & Fulfilled<T>\nexport type RejectedThenable<T> = Promise<T> & Rejected\nexport type PendingThenable<T> = Promise<T> & Pending<T>\n\nexport type Thenable<T> =\n  | FulfilledThenable<T>\n  | RejectedThenable<T>\n  | PendingThenable<T>\n\nexport function pendingThenable<T>(): PendingThenable<T> {\n  let resolve: Pending<T>['resolve']\n  let reject: Pending<T>['reject']\n  // this could use `Promise.withResolvers()` in the future\n  const thenable = new Promise((_resolve, _reject) => {\n    resolve = _resolve\n    reject = _reject\n  }) as PendingThenable<T>\n\n  thenable.status = 'pending'\n  thenable.catch(() => {\n    // prevent unhandled rejection errors\n  })\n\n  function finalize(data: Fulfilled<T> | Rejected) {\n    Object.assign(thenable, data)\n\n    // clear pending props props to avoid calling them twice\n    delete (thenable as Partial<PendingThenable<T>>).resolve\n    delete (thenable as Partial<PendingThenable<T>>).reject\n  }\n\n  thenable.resolve = (value) => {\n    finalize({\n      status: 'fulfilled',\n      value,\n    })\n\n    resolve(value)\n  }\n  thenable.reject = (reason) => {\n    finalize({\n      status: 'rejected',\n      reason,\n    })\n\n    reject(reason)\n  }\n\n  return thenable\n}\n", "import { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { pendingThenable } from './thenable'\nimport { isServer, sleep } from './utils'\nimport type { CancelOptions, DefaultError, NetworkMode } from './types'\n\n// TYPES\n\ninterface RetryerConfig<TData = unknown, TError = DefaultError> {\n  fn: () => TData | Promise<TData>\n  initialPromise?: Promise<TData>\n  abort?: () => void\n  onError?: (error: TError) => void\n  onSuccess?: (data: TData) => void\n  onFail?: (failureCount: number, error: TError) => void\n  onPause?: () => void\n  onContinue?: () => void\n  retry?: RetryValue<TError>\n  retryDelay?: RetryDelayValue<TError>\n  networkMode: NetworkMode | undefined\n  canRun: () => boolean\n}\n\nexport interface Retryer<TData = unknown> {\n  promise: Promise<TData>\n  cancel: (cancelOptions?: CancelOptions) => void\n  continue: () => Promise<unknown>\n  cancelRetry: () => void\n  continueRetry: () => void\n  canStart: () => boolean\n  start: () => Promise<TData>\n}\n\nexport type RetryValue<TError> = boolean | number | ShouldRetryFunction<TError>\n\ntype ShouldRetryFunction<TError = DefaultError> = (\n  failureCount: number,\n  error: TError,\n) => boolean\n\nexport type RetryDelayValue<TError> = number | RetryDelayFunction<TError>\n\ntype RetryDelayFunction<TError = DefaultError> = (\n  failureCount: number,\n  error: TError,\n) => number\n\nfunction defaultRetryDelay(failureCount: number) {\n  return Math.min(1000 * 2 ** failureCount, 30000)\n}\n\nexport function canFetch(networkMode: NetworkMode | undefined): boolean {\n  return (networkMode ?? 'online') === 'online'\n    ? onlineManager.isOnline()\n    : true\n}\n\nexport class CancelledError extends Error {\n  revert?: boolean\n  silent?: boolean\n  constructor(options?: CancelOptions) {\n    super('CancelledError')\n    this.revert = options?.revert\n    this.silent = options?.silent\n  }\n}\n\nexport function isCancelledError(value: any): value is CancelledError {\n  return value instanceof CancelledError\n}\n\nexport function createRetryer<TData = unknown, TError = DefaultError>(\n  config: RetryerConfig<TData, TError>,\n): Retryer<TData> {\n  let isRetryCancelled = false\n  let failureCount = 0\n  let isResolved = false\n  let continueFn: ((value?: unknown) => void) | undefined\n\n  const thenable = pendingThenable<TData>()\n\n  const cancel = (cancelOptions?: CancelOptions): void => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions))\n\n      config.abort?.()\n    }\n  }\n  const cancelRetry = () => {\n    isRetryCancelled = true\n  }\n\n  const continueRetry = () => {\n    isRetryCancelled = false\n  }\n\n  const canContinue = () =>\n    focusManager.isFocused() &&\n    (config.networkMode === 'always' || onlineManager.isOnline()) &&\n    config.canRun()\n\n  const canStart = () => canFetch(config.networkMode) && config.canRun()\n\n  const resolve = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onSuccess?.(value)\n      continueFn?.()\n      thenable.resolve(value)\n    }\n  }\n\n  const reject = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onError?.(value)\n      continueFn?.()\n      thenable.reject(value)\n    }\n  }\n\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        if (isResolved || canContinue()) {\n          continueResolve(value)\n        }\n      }\n      config.onPause?.()\n    }).then(() => {\n      continueFn = undefined\n      if (!isResolved) {\n        config.onContinue?.()\n      }\n    })\n  }\n\n  // Create loop function\n  const run = () => {\n    // Do nothing if already resolved\n    if (isResolved) {\n      return\n    }\n\n    let promiseOrValue: any\n\n    // we can re-use config.initialPromise on the first call of run()\n    const initialPromise =\n      failureCount === 0 ? config.initialPromise : undefined\n\n    // Execute query\n    try {\n      promiseOrValue = initialPromise ?? config.fn()\n    } catch (error) {\n      promiseOrValue = Promise.reject(error)\n    }\n\n    Promise.resolve(promiseOrValue)\n      .then(resolve)\n      .catch((error) => {\n        // Stop if the fetch is already resolved\n        if (isResolved) {\n          return\n        }\n\n        // Do we need to retry the request?\n        const retry = config.retry ?? (isServer ? 0 : 3)\n        const retryDelay = config.retryDelay ?? defaultRetryDelay\n        const delay =\n          typeof retryDelay === 'function'\n            ? retryDelay(failureCount, error)\n            : retryDelay\n        const shouldRetry =\n          retry === true ||\n          (typeof retry === 'number' && failureCount < retry) ||\n          (typeof retry === 'function' && retry(failureCount, error))\n\n        if (isRetryCancelled || !shouldRetry) {\n          // We are done if the query does not need to be retried\n          reject(error)\n          return\n        }\n\n        failureCount++\n\n        // Notify on fail\n        config.onFail?.(failureCount, error)\n\n        // Delay\n        sleep(delay)\n          // Pause if the document is not visible or when the device is offline\n          .then(() => {\n            return canContinue() ? undefined : pause()\n          })\n          .then(() => {\n            if (isRetryCancelled) {\n              reject(error)\n            } else {\n              run()\n            }\n          })\n      })\n  }\n\n  return {\n    promise: thenable,\n    cancel,\n    continue: () => {\n      continueFn?.()\n      return thenable\n    },\n    cancelRetry,\n    continueRetry,\n    canStart,\n    start: () => {\n      // Start loop\n      if (canStart()) {\n        run()\n      } else {\n        pause().then(run)\n      }\n      return thenable\n    },\n  }\n}\n", "// TYPES\n\ntype NotifyCallback = () => void\n\ntype NotifyFunction = (callback: () => void) => void\n\ntype BatchNotifyFunction = (callback: () => void) => void\n\ntype BatchCallsCallback<T extends Array<unknown>> = (...args: T) => void\n\ntype ScheduleFunction = (callback: () => void) => void\n\nexport function createNotifyManager() {\n  let queue: Array<NotifyCallback> = []\n  let transactions = 0\n  let notifyFn: NotifyFunction = (callback) => {\n    callback()\n  }\n  let batchNotifyFn: BatchNotifyFunction = (callback: () => void) => {\n    callback()\n  }\n  let scheduleFn: ScheduleFunction = (cb) => setTimeout(cb, 0)\n\n  const schedule = (callback: NotifyCallback): void => {\n    if (transactions) {\n      queue.push(callback)\n    } else {\n      scheduleFn(() => {\n        notifyFn(callback)\n      })\n    }\n  }\n  const flush = (): void => {\n    const originalQueue = queue\n    queue = []\n    if (originalQueue.length) {\n      scheduleFn(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback)\n          })\n        })\n      })\n    }\n  }\n\n  return {\n    batch: <T>(callback: () => T): T => {\n      let result\n      transactions++\n      try {\n        result = callback()\n      } finally {\n        transactions--\n        if (!transactions) {\n          flush()\n        }\n      }\n      return result\n    },\n    /**\n     * All calls to the wrapped function will be batched.\n     */\n    batchCalls: <T extends Array<unknown>>(\n      callback: BatchCallsCallback<T>,\n    ): BatchCallsCallback<T> => {\n      return (...args) => {\n        schedule(() => {\n          callback(...args)\n        })\n      }\n    },\n    schedule,\n    /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */\n    setNotifyFunction: (fn: NotifyFunction) => {\n      notifyFn = fn\n    },\n    /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */\n    setBatchNotifyFunction: (fn: BatchNotifyFunction) => {\n      batchNotifyFn = fn\n    },\n    setScheduler: (fn: ScheduleFunction) => {\n      scheduleFn = fn\n    },\n  } as const\n}\n\n// SINGLETON\nexport const notifyManager = createNotifyManager()\n", "import { isServer, isValidTimeout } from './utils'\n\nexport abstract class Removable {\n  gcTime!: number\n  #gcTimeout?: ReturnType<typeof setTimeout>\n\n  destroy(): void {\n    this.clearGcTimeout()\n  }\n\n  protected scheduleGc(): void {\n    this.clearGcTimeout()\n\n    if (isValidTimeout(this.gcTime)) {\n      this.#gcTimeout = setTimeout(() => {\n        this.optionalRemove()\n      }, this.gcTime)\n    }\n  }\n\n  protected updateGcTime(newGcTime: number | undefined): void {\n    // Default to 5 minutes (Infinity for server-side) if no gcTime is set\n    this.gcTime = Math.max(\n      this.gcTime || 0,\n      newGcTime ?? (isServer ? Infinity : 5 * 60 * 1000),\n    )\n  }\n\n  protected clearGcTimeout() {\n    if (this.#gcTimeout) {\n      clearTimeout(this.#gcTimeout)\n      this.#gcTimeout = undefined\n    }\n  }\n\n  protected abstract optionalRemove(): void\n}\n", "import {\n  ensureQueryFn,\n  noop,\n  replaceData,\n  resolveEnabled,\n  skipToken,\n  timeUntilStale,\n} from './utils'\nimport { notifyManager } from './notifyManager'\nimport { canFetch, createRetryer, isCancelledError } from './retryer'\nimport { Removable } from './removable'\nimport type {\n  CancelOptions,\n  DefaultError,\n  FetchStatus,\n  InitialDataFunction,\n  OmitKeyof,\n  QueryFunction,\n  QueryFunctionContext,\n  QueryKey,\n  QueryMeta,\n  QueryOptions,\n  QueryStatus,\n  SetDataOptions,\n} from './types'\nimport type { QueryCache } from './queryCache'\nimport type { QueryObserver } from './queryObserver'\nimport type { Retryer } from './retryer'\n\n// TYPES\n\ninterface QueryConfig<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  cache: QueryCache\n  queryKey: TQ<PERSON>y<PERSON>ey\n  queryHash: string\n  options?: QueryOptions<TQueryFnData, TError, <PERSON><PERSON>, TQ<PERSON>yK<PERSON>>\n  defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state?: QueryState<TData, TError>\n}\n\nexport interface QueryState<TData = unknown, TError = DefaultError> {\n  data: TData | undefined\n  dataUpdateCount: number\n  dataUpdatedAt: number\n  error: TError | null\n  errorUpdateCount: number\n  errorUpdatedAt: number\n  fetchFailureCount: number\n  fetchFailureReason: TError | null\n  fetchMeta: FetchMeta | null\n  isInvalidated: boolean\n  status: QueryStatus\n  fetchStatus: FetchStatus\n}\n\nexport interface FetchContext<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  fetchFn: () => unknown | Promise<unknown>\n  fetchOptions?: FetchOptions\n  signal: AbortSignal\n  options: QueryOptions<TQueryFnData, TError, TData, any>\n  queryKey: TQueryKey\n  state: QueryState<TData, TError>\n}\n\nexport interface QueryBehavior<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  onFetch: (\n    context: FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n    query: Query,\n  ) => void\n}\n\nexport type FetchDirection = 'forward' | 'backward'\n\nexport interface FetchMeta {\n  fetchMore?: { direction: FetchDirection }\n}\n\nexport interface FetchOptions<TData = unknown> {\n  cancelRefetch?: boolean\n  meta?: FetchMeta\n  initialPromise?: Promise<TData>\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError\n}\n\ninterface FetchAction {\n  type: 'fetch'\n  meta?: FetchMeta\n}\n\ninterface SuccessAction<TData> {\n  data: TData | undefined\n  type: 'success'\n  dataUpdatedAt?: number\n  manual?: boolean\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface InvalidateAction {\n  type: 'invalidate'\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError> {\n  type: 'setState'\n  state: Partial<QueryState<TData, TError>>\n  setStateOptions?: SetStateOptions\n}\n\nexport type Action<TData, TError> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | FetchAction\n  | InvalidateAction\n  | PauseAction\n  | SetStateAction<TData, TError>\n  | SuccessAction<TData>\n\nexport interface SetStateOptions {\n  meta?: any\n}\n\n// CLASS\n\nexport class Query<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Removable {\n  queryKey: TQueryKey\n  queryHash: string\n  options!: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state: QueryState<TData, TError>\n\n  #initialState: QueryState<TData, TError>\n  #revertState?: QueryState<TData, TError>\n  #cache: QueryCache\n  #retryer?: Retryer<TData>\n  observers: Array<QueryObserver<any, any, any, any, any>>\n  #defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  #abortSignalConsumed: boolean\n\n  constructor(config: QueryConfig<TQueryFnData, TError, TData, TQueryKey>) {\n    super()\n\n    this.#abortSignalConsumed = false\n    this.#defaultOptions = config.defaultOptions\n    this.setOptions(config.options)\n    this.observers = []\n    this.#cache = config.cache\n    this.queryKey = config.queryKey\n    this.queryHash = config.queryHash\n    this.#initialState = getDefaultState(this.options)\n    this.state = config.state ?? this.#initialState\n    this.scheduleGc()\n  }\n  get meta(): QueryMeta | undefined {\n    return this.options.meta\n  }\n\n  get promise(): Promise<TData> | undefined {\n    return this.#retryer?.promise\n  }\n\n  setOptions(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): void {\n    this.options = { ...this.#defaultOptions, ...options }\n\n    this.updateGcTime(this.options.gcTime)\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === 'idle') {\n      this.#cache.remove(this)\n    }\n  }\n\n  setData(\n    newData: TData,\n    options?: SetDataOptions & { manual: boolean },\n  ): TData {\n    const data = replaceData(this.state.data, newData, this.options)\n\n    // Set data and mark it as cached\n    this.#dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual,\n    })\n\n    return data\n  }\n\n  setState(\n    state: Partial<QueryState<TData, TError>>,\n    setStateOptions?: SetStateOptions,\n  ): void {\n    this.#dispatch({ type: 'setState', state, setStateOptions })\n  }\n\n  cancel(options?: CancelOptions): Promise<void> {\n    const promise = this.#retryer?.promise\n    this.#retryer?.cancel(options)\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve()\n  }\n\n  destroy(): void {\n    super.destroy()\n\n    this.cancel({ silent: true })\n  }\n\n  reset(): void {\n    this.destroy()\n    this.setState(this.#initialState)\n  }\n\n  isActive(): boolean {\n    return this.observers.some(\n      (observer) => resolveEnabled(observer.options.enabled, this) !== false,\n    )\n  }\n\n  isDisabled(): boolean {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive()\n    }\n    // if a query has no observers, it should still be considered disabled if it never attempted a fetch\n    return (\n      this.options.queryFn === skipToken ||\n      this.state.dataUpdateCount + this.state.errorUpdateCount === 0\n    )\n  }\n\n  isStale(): boolean {\n    if (this.state.isInvalidated) {\n      return true\n    }\n\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => observer.getCurrentResult().isStale,\n      )\n    }\n\n    return this.state.data === undefined\n  }\n\n  isStaleByTime(staleTime = 0): boolean {\n    return (\n      this.state.isInvalidated ||\n      this.state.data === undefined ||\n      !timeUntilStale(this.state.dataUpdatedAt, staleTime)\n    )\n  }\n\n  onFocus(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus())\n\n    observer?.refetch({ cancelRefetch: false })\n\n    // Continue fetch if currently paused\n    this.#retryer?.continue()\n  }\n\n  onOnline(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect())\n\n    observer?.refetch({ cancelRefetch: false })\n\n    // Continue fetch if currently paused\n    this.#retryer?.continue()\n  }\n\n  addObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the query from being garbage collected\n      this.clearGcTimeout()\n\n      this.#cache.notify({ type: 'observerAdded', query: this, observer })\n    }\n  }\n\n  removeObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer)\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({ revert: true })\n          } else {\n            this.#retryer.cancelRetry()\n          }\n        }\n\n        this.scheduleGc()\n      }\n\n      this.#cache.notify({ type: 'observerRemoved', query: this, observer })\n    }\n  }\n\n  getObserversCount(): number {\n    return this.observers.length\n  }\n\n  invalidate(): void {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({ type: 'invalidate' })\n    }\n  }\n\n  fetch(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    fetchOptions?: FetchOptions<TQueryFnData>,\n  ): Promise<TData> {\n    if (this.state.fetchStatus !== 'idle') {\n      if (this.state.data !== undefined && fetchOptions?.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetch\n        this.cancel({ silent: true })\n      } else if (this.#retryer) {\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        this.#retryer.continueRetry()\n        // Return current promise if we are already fetching\n        return this.#retryer.promise\n      }\n    }\n\n    // Update config if passed, otherwise the config from the last execution is used\n    if (options) {\n      this.setOptions(options)\n    }\n\n    // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn)\n      if (observer) {\n        this.setOptions(observer.options)\n      }\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`,\n        )\n      }\n    }\n\n    const abortController = new AbortController()\n\n    // Adds an enumerable signal property to the object that\n    // which sets abortSignalConsumed to true when the signal\n    // is read.\n    const addSignalProperty = (object: unknown) => {\n      Object.defineProperty(object, 'signal', {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true\n          return abortController.signal\n        },\n      })\n    }\n\n    // Create fetch function\n    const fetchFn = () => {\n      const queryFn = ensureQueryFn(this.options, fetchOptions)\n\n      // Create query function context\n      const queryFnContext: OmitKeyof<\n        QueryFunctionContext<TQueryKey>,\n        'signal'\n      > = {\n        queryKey: this.queryKey,\n        meta: this.meta,\n      }\n\n      addSignalProperty(queryFnContext)\n\n      this.#abortSignalConsumed = false\n      if (this.options.persister) {\n        return this.options.persister(\n          queryFn as QueryFunction<any>,\n          queryFnContext as QueryFunctionContext<TQueryKey>,\n          this as unknown as Query,\n        )\n      }\n\n      return queryFn(queryFnContext as QueryFunctionContext<TQueryKey>)\n    }\n\n    // Trigger behavior hook\n    const context: OmitKeyof<\n      FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n      'signal'\n    > = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      state: this.state,\n      fetchFn,\n    }\n\n    addSignalProperty(context)\n\n    this.options.behavior?.onFetch(\n      context as FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n      this as unknown as Query,\n    )\n\n    // Store state in case the current fetch needs to be reverted\n    this.#revertState = this.state\n\n    // Set to fetching state if not already in it\n    if (\n      this.state.fetchStatus === 'idle' ||\n      this.state.fetchMeta !== context.fetchOptions?.meta\n    ) {\n      this.#dispatch({ type: 'fetch', meta: context.fetchOptions?.meta })\n    }\n\n    const onError = (error: TError | { silent?: boolean }) => {\n      // Optimistically update state if needed\n      if (!(isCancelledError(error) && error.silent)) {\n        this.#dispatch({\n          type: 'error',\n          error: error as TError,\n        })\n      }\n\n      if (!isCancelledError(error)) {\n        // Notify cache callback\n        this.#cache.config.onError?.(\n          error as any,\n          this as Query<any, any, any, any>,\n        )\n        this.#cache.config.onSettled?.(\n          this.state.data,\n          error as any,\n          this as Query<any, any, any, any>,\n        )\n      }\n\n      // Schedule query gc after fetching\n      this.scheduleGc()\n    }\n\n    // Try to fetch the data\n    this.#retryer = createRetryer({\n      initialPromise: fetchOptions?.initialPromise as\n        | Promise<TData>\n        | undefined,\n      fn: context.fetchFn as () => Promise<TData>,\n      abort: abortController.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (data === undefined) {\n          if (process.env.NODE_ENV !== 'production') {\n            console.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`,\n            )\n          }\n          onError(new Error(`${this.queryHash} data is undefined`) as any)\n          return\n        }\n\n        try {\n          this.setData(data)\n        } catch (error) {\n          onError(error as TError)\n          return\n        }\n\n        // Notify cache callback\n        this.#cache.config.onSuccess?.(data, this as Query<any, any, any, any>)\n        this.#cache.config.onSettled?.(\n          data,\n          this.state.error as any,\n          this as Query<any, any, any, any>,\n        )\n\n        // Schedule query gc after fetching\n        this.scheduleGc()\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: 'failed', failureCount, error })\n      },\n      onPause: () => {\n        this.#dispatch({ type: 'pause' })\n      },\n      onContinue: () => {\n        this.#dispatch({ type: 'continue' })\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true,\n    })\n\n    return this.#retryer.start()\n  }\n\n  #dispatch(action: Action<TData, TError>): void {\n    const reducer = (\n      state: QueryState<TData, TError>,\n    ): QueryState<TData, TError> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            fetchStatus: 'paused',\n          }\n        case 'continue':\n          return {\n            ...state,\n            fetchStatus: 'fetching',\n          }\n        case 'fetch':\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null,\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: 'success',\n            ...(!action.manual && {\n              fetchStatus: 'idle',\n              fetchFailureCount: 0,\n              fetchFailureReason: null,\n            }),\n          }\n        case 'error':\n          const error = action.error\n\n          if (isCancelledError(error) && error.revert && this.#revertState) {\n            return { ...this.#revertState, fetchStatus: 'idle' }\n          }\n\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: 'idle',\n            status: 'error',\n          }\n        case 'invalidate':\n          return {\n            ...state,\n            isInvalidated: true,\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate()\n      })\n\n      this.#cache.notify({ query: this, type: 'updated', action })\n    })\n  }\n}\n\nexport function fetchState<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  data: TData | undefined,\n  options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: canFetch(options.networkMode) ? 'fetching' : 'paused',\n    ...(data === undefined &&\n      ({\n        error: null,\n        status: 'pending',\n      } as const)),\n  } as const\n}\n\nfunction getDefaultState<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): QueryState<TData, TError> {\n  const data =\n    typeof options.initialData === 'function'\n      ? (options.initialData as InitialDataFunction<TData>)()\n      : options.initialData\n\n  const hasData = data !== undefined\n\n  const initialDataUpdatedAt = hasData\n    ? typeof options.initialDataUpdatedAt === 'function'\n      ? (options.initialDataUpdatedAt as () => number | undefined)()\n      : options.initialDataUpdatedAt\n    : 0\n\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? (initialDataUpdatedAt ?? Date.now()) : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? 'success' : 'pending',\n    fetchStatus: 'idle',\n  }\n}\n", "import { hashQueryKeyByOptions, matchQuery } from './utils'\nimport { Query } from './query'\nimport { notifyManager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport type { QueryFilters } from './utils'\nimport type { Action, QueryState } from './query'\nimport type {\n  DefaultError,\n  NotifyEvent,\n  QueryKey,\n  QueryOptions,\n  WithRequired,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { QueryObserver } from './queryObserver'\n\n// TYPES\n\ninterface QueryCacheConfig {\n  onError?: (\n    error: DefaultError,\n    query: Query<unknown, unknown, unknown>,\n  ) => void\n  onSuccess?: (data: unknown, query: Query<unknown, unknown, unknown>) => void\n  onSettled?: (\n    data: unknown | undefined,\n    error: DefaultError | null,\n    query: Query<unknown, unknown, unknown>,\n  ) => void\n}\n\ninterface NotifyEventQueryAdded extends NotifyEvent {\n  type: 'added'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryRemoved extends NotifyEvent {\n  type: 'removed'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryUpdated extends NotifyEvent {\n  type: 'updated'\n  query: Query<any, any, any, any>\n  action: Action<any, any>\n}\n\ninterface NotifyEventQueryObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverResultsUpdated extends NotifyEvent {\n  type: 'observerResultsUpdated'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\nexport type QueryCacheNotifyEvent =\n  | NotifyEventQueryAdded\n  | NotifyEventQueryRemoved\n  | NotifyEventQueryUpdated\n  | NotifyEventQueryObserverAdded\n  | NotifyEventQueryObserverRemoved\n  | NotifyEventQueryObserverResultsUpdated\n  | NotifyEventQueryObserverOptionsUpdated\n\ntype QueryCacheListener = (event: QueryCacheNotifyEvent) => void\n\nexport interface QueryStore {\n  has: (queryHash: string) => boolean\n  set: (queryHash: string, query: Query) => void\n  get: (queryHash: string) => Query | undefined\n  delete: (queryHash: string) => void\n  values: () => IterableIterator<Query>\n}\n\n// CLASS\n\nexport class QueryCache extends Subscribable<QueryCacheListener> {\n  #queries: QueryStore\n\n  constructor(public config: QueryCacheConfig = {}) {\n    super()\n    this.#queries = new Map<string, Query>()\n  }\n\n  build<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    client: QueryClient,\n    options: WithRequired<\n      QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n    state?: QueryState<TData, TError>,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> {\n    const queryKey = options.queryKey\n    const queryHash =\n      options.queryHash ?? hashQueryKeyByOptions(queryKey, options)\n    let query = this.get<TQueryFnData, TError, TData, TQueryKey>(queryHash)\n\n    if (!query) {\n      query = new Query({\n        cache: this,\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey),\n      })\n      this.add(query)\n    }\n\n    return query\n  }\n\n  add(query: Query<any, any, any, any>): void {\n    if (!this.#queries.has(query.queryHash)) {\n      this.#queries.set(query.queryHash, query)\n\n      this.notify({\n        type: 'added',\n        query,\n      })\n    }\n  }\n\n  remove(query: Query<any, any, any, any>): void {\n    const queryInMap = this.#queries.get(query.queryHash)\n\n    if (queryInMap) {\n      query.destroy()\n\n      if (queryInMap === query) {\n        this.#queries.delete(query.queryHash)\n      }\n\n      this.notify({ type: 'removed', query })\n    }\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        this.remove(query)\n      })\n    })\n  }\n\n  get<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryHash: string,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> | undefined {\n    return this.#queries.get(queryHash) as\n      | Query<TQueryFnData, TError, TData, TQueryKey>\n      | undefined\n  }\n\n  getAll(): Array<Query> {\n    return [...this.#queries.values()]\n  }\n\n  find<TQueryFnData = unknown, TError = DefaultError, TData = TQueryFnData>(\n    filters: WithRequired<QueryFilters, 'queryKey'>,\n  ): Query<TQueryFnData, TError, TData> | undefined {\n    const defaultedFilters = { exact: true, ...filters }\n\n    return this.getAll().find((query) =>\n      matchQuery(defaultedFilters, query),\n    ) as Query<TQueryFnData, TError, TData> | undefined\n  }\n\n  findAll(filters: QueryFilters = {}): Array<Query> {\n    const queries = this.getAll()\n    return Object.keys(filters).length > 0\n      ? queries.filter((query) => matchQuery(filters, query))\n      : queries\n  }\n\n  notify(event: QueryCacheNotifyEvent): void {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event)\n      })\n    })\n  }\n\n  onFocus(): void {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onFocus()\n      })\n    })\n  }\n\n  onOnline(): void {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onOnline()\n      })\n    })\n  }\n}\n", "import { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Removable } from './removable'\nimport { createR<PERSON>ry<PERSON> } from './retryer'\nimport type {\n  DefaultError,\n  MutationMeta,\n  MutationOptions,\n  MutationStatus,\n} from './types'\nimport type { MutationCache } from './mutationCache'\nimport type { MutationObserver } from './mutationObserver'\nimport type { <PERSON><PERSON><PERSON> } from './retryer'\n\n// TYPES\n\ninterface MutationConfig<TData, TError, TVariables, TContext> {\n  mutationId: number\n  mutationCache: MutationCache\n  options: MutationOptions<TData, TError, TVariables, TContext>\n  state?: MutationState<TData, TError, TVariables, TContext>\n}\n\nexport interface MutationState<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = unknown,\n  TContext = unknown,\n> {\n  context: TContext | undefined\n  data: TData | undefined\n  error: TError | null\n  failureCount: number\n  failureReason: TError | null\n  isPaused: boolean\n  status: MutationStatus\n  variables: TVariables | undefined\n  submittedAt: number\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError | null\n}\n\ninterface PendingAction<TVariables, TContext> {\n  type: 'pending'\n  isPaused: boolean\n  variables?: TVariables\n  context?: TContext\n}\n\ninterface SuccessAction<TData> {\n  type: 'success'\n  data: TData\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\nexport type Action<TData, TError, TVariables, TContext> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | PendingAction<TVariables, TContext>\n  | PauseAction\n  | SuccessAction<TData>\n\n// CLASS\n\nexport class Mutation<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = unknown,\n  TContext = unknown,\n> extends Removable {\n  state: MutationState<TData, TError, TVariables, TContext>\n  options!: MutationOptions<TData, TError, TVariables, TContext>\n  readonly mutationId: number\n\n  #observers: Array<MutationObserver<TData, TError, TVariables, TContext>>\n  #mutationCache: MutationCache\n  #retryer?: Retryer<TData>\n\n  constructor(config: MutationConfig<TData, TError, TVariables, TContext>) {\n    super()\n\n    this.mutationId = config.mutationId\n    this.#mutationCache = config.mutationCache\n    this.#observers = []\n    this.state = config.state || getDefaultState()\n\n    this.setOptions(config.options)\n    this.scheduleGc()\n  }\n\n  setOptions(\n    options: MutationOptions<TData, TError, TVariables, TContext>,\n  ): void {\n    this.options = options\n\n    this.updateGcTime(this.options.gcTime)\n  }\n\n  get meta(): MutationMeta | undefined {\n    return this.options.meta\n  }\n\n  addObserver(observer: MutationObserver<any, any, any, any>): void {\n    if (!this.#observers.includes(observer)) {\n      this.#observers.push(observer)\n\n      // Stop the mutation from being garbage collected\n      this.clearGcTimeout()\n\n      this.#mutationCache.notify({\n        type: 'observerAdded',\n        mutation: this,\n        observer,\n      })\n    }\n  }\n\n  removeObserver(observer: MutationObserver<any, any, any, any>): void {\n    this.#observers = this.#observers.filter((x) => x !== observer)\n\n    this.scheduleGc()\n\n    this.#mutationCache.notify({\n      type: 'observerRemoved',\n      mutation: this,\n      observer,\n    })\n  }\n\n  protected optionalRemove() {\n    if (!this.#observers.length) {\n      if (this.state.status === 'pending') {\n        this.scheduleGc()\n      } else {\n        this.#mutationCache.remove(this)\n      }\n    }\n  }\n\n  continue(): Promise<unknown> {\n    return (\n      this.#retryer?.continue() ??\n      // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n      this.execute(this.state.variables!)\n    )\n  }\n\n  async execute(variables: TVariables): Promise<TData> {\n    this.#retryer = createRetryer({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject(new Error('No mutationFn found'))\n        }\n        return this.options.mutationFn(variables)\n      },\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: 'failed', failureCount, error })\n      },\n      onPause: () => {\n        this.#dispatch({ type: 'pause' })\n      },\n      onContinue: () => {\n        this.#dispatch({ type: 'continue' })\n      },\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n      networkMode: this.options.networkMode,\n      canRun: () => this.#mutationCache.canRun(this),\n    })\n\n    const restored = this.state.status === 'pending'\n    const isPaused = !this.#retryer.canStart()\n\n    try {\n      if (!restored) {\n        this.#dispatch({ type: 'pending', variables, isPaused })\n        // Notify cache callback\n        await this.#mutationCache.config.onMutate?.(\n          variables,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n        const context = await this.options.onMutate?.(variables)\n        if (context !== this.state.context) {\n          this.#dispatch({\n            type: 'pending',\n            context,\n            variables,\n            isPaused,\n          })\n        }\n      }\n      const data = await this.#retryer.start()\n\n      // Notify cache callback\n      await this.#mutationCache.config.onSuccess?.(\n        data,\n        variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSuccess?.(data, variables, this.state.context!)\n\n      // Notify cache callback\n      await this.#mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSettled?.(data, null, variables, this.state.context)\n\n      this.#dispatch({ type: 'success', data })\n      return data\n    } catch (error) {\n      try {\n        // Notify cache callback\n        await this.#mutationCache.config.onError?.(\n          error as any,\n          variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        await this.options.onError?.(\n          error as TError,\n          variables,\n          this.state.context,\n        )\n\n        // Notify cache callback\n        await this.#mutationCache.config.onSettled?.(\n          undefined,\n          error as any,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        await this.options.onSettled?.(\n          undefined,\n          error as TError,\n          variables,\n          this.state.context,\n        )\n        throw error\n      } finally {\n        this.#dispatch({ type: 'error', error: error as TError })\n      }\n    } finally {\n      this.#mutationCache.runNext(this)\n    }\n  }\n\n  #dispatch(action: Action<TData, TError, TVariables, TContext>): void {\n    const reducer = (\n      state: MutationState<TData, TError, TVariables, TContext>,\n    ): MutationState<TData, TError, TVariables, TContext> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            isPaused: true,\n          }\n        case 'continue':\n          return {\n            ...state,\n            isPaused: false,\n          }\n        case 'pending':\n          return {\n            ...state,\n            context: action.context,\n            data: undefined,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: action.isPaused,\n            status: 'pending',\n            variables: action.variables,\n            submittedAt: Date.now(),\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: 'success',\n            isPaused: false,\n          }\n        case 'error':\n          return {\n            ...state,\n            data: undefined,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: 'error',\n          }\n      }\n    }\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.#observers.forEach((observer) => {\n        observer.onMutationUpdate(action)\n      })\n      this.#mutationCache.notify({\n        mutation: this,\n        type: 'updated',\n        action,\n      })\n    })\n  }\n}\n\nexport function getDefaultState<\n  TData,\n  TError,\n  TVariables,\n  TContext,\n>(): MutationState<TData, TError, TVariables, TContext> {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined,\n    submittedAt: 0,\n  }\n}\n", "import { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Mutation } from './mutation'\nimport { matchMutation, noop } from './utils'\nimport { Subscribable } from './subscribable'\nimport type { MutationObserver } from './mutationObserver'\nimport type { DefaultError, MutationOptions, NotifyEvent } from './types'\nimport type { QueryClient } from './queryClient'\nimport type { Action, MutationState } from './mutation'\nimport type { MutationFilters } from './utils'\n\n// TYPES\n\ninterface MutationCacheConfig {\n  onError?: (\n    error: DefaultError,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSuccess?: (\n    data: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onMutate?: (\n    variables: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSettled?: (\n    data: unknown | undefined,\n    error: DefaultError | null,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n}\n\ninterface NotifyEventMutationAdded extends NotifyEvent {\n  type: 'added'\n  mutation: Mutation<any, any, any, any>\n}\ninterface NotifyEventMutationRemoved extends NotifyEvent {\n  type: 'removed'\n  mutation: Mutation<any, any, any, any>\n}\n\ninterface NotifyEventMutationObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  mutation?: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any, any>\n}\n\ninterface NotifyEventMutationUpdated extends NotifyEvent {\n  type: 'updated'\n  mutation: Mutation<any, any, any, any>\n  action: Action<any, any, any, any>\n}\n\nexport type MutationCacheNotifyEvent =\n  | NotifyEventMutationAdded\n  | NotifyEventMutationRemoved\n  | NotifyEventMutationObserverAdded\n  | NotifyEventMutationObserverRemoved\n  | NotifyEventMutationObserverOptionsUpdated\n  | NotifyEventMutationUpdated\n\ntype MutationCacheListener = (event: MutationCacheNotifyEvent) => void\n\n// CLASS\n\nexport class MutationCache extends Subscribable<MutationCacheListener> {\n  #mutations: Set<Mutation<any, any, any, any>>\n  #scopes: Map<string, Array<Mutation<any, any, any, any>>>\n  #mutationId: number\n\n  constructor(public config: MutationCacheConfig = {}) {\n    super()\n    this.#mutations = new Set()\n    this.#scopes = new Map()\n    this.#mutationId = 0\n  }\n\n  build<TData, TError, TVariables, TContext>(\n    client: QueryClient,\n    options: MutationOptions<TData, TError, TVariables, TContext>,\n    state?: MutationState<TData, TError, TVariables, TContext>,\n  ): Mutation<TData, TError, TVariables, TContext> {\n    const mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.#mutationId,\n      options: client.defaultMutationOptions(options),\n      state,\n    })\n\n    this.add(mutation)\n\n    return mutation\n  }\n\n  add(mutation: Mutation<any, any, any, any>): void {\n    this.#mutations.add(mutation)\n    const scope = scopeFor(mutation)\n    if (typeof scope === 'string') {\n      const scopedMutations = this.#scopes.get(scope)\n      if (scopedMutations) {\n        scopedMutations.push(mutation)\n      } else {\n        this.#scopes.set(scope, [mutation])\n      }\n    }\n    this.notify({ type: 'added', mutation })\n  }\n\n  remove(mutation: Mutation<any, any, any, any>): void {\n    if (this.#mutations.delete(mutation)) {\n      const scope = scopeFor(mutation)\n      if (typeof scope === 'string') {\n        const scopedMutations = this.#scopes.get(scope)\n        if (scopedMutations) {\n          if (scopedMutations.length > 1) {\n            const index = scopedMutations.indexOf(mutation)\n            if (index !== -1) {\n              scopedMutations.splice(index, 1)\n            }\n          } else if (scopedMutations[0] === mutation) {\n            this.#scopes.delete(scope)\n          }\n        }\n      }\n    }\n\n    // Currently we notify the removal even if the mutation was already removed.\n    // Consider making this an error or not notifying of the removal depending on the desired semantics.\n    this.notify({ type: 'removed', mutation })\n  }\n\n  canRun(mutation: Mutation<any, any, any, any>): boolean {\n    const scope = scopeFor(mutation)\n    if (typeof scope === 'string') {\n      const mutationsWithSameScope = this.#scopes.get(scope)\n      const firstPendingMutation = mutationsWithSameScope?.find(\n        (m) => m.state.status === 'pending',\n      )\n      // we can run if there is no current pending mutation (start use-case)\n      // or if WE are the first pending mutation (continue use-case)\n      return !firstPendingMutation || firstPendingMutation === mutation\n    } else {\n      // For unscoped mutations there are never any pending mutations in front of the\n      // current mutation\n      return true\n    }\n  }\n\n  runNext(mutation: Mutation<any, any, any, any>): Promise<unknown> {\n    const scope = scopeFor(mutation)\n    if (typeof scope === 'string') {\n      const foundMutation = this.#scopes\n        .get(scope)\n        ?.find((m) => m !== mutation && m.state.isPaused)\n\n      return foundMutation?.continue() ?? Promise.resolve()\n    } else {\n      return Promise.resolve()\n    }\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.#mutations.forEach((mutation) => {\n        this.notify({ type: 'removed', mutation })\n      })\n      this.#mutations.clear()\n      this.#scopes.clear()\n    })\n  }\n\n  getAll(): Array<Mutation> {\n    return Array.from(this.#mutations)\n  }\n\n  find<\n    TData = unknown,\n    TError = DefaultError,\n    TVariables = any,\n    TContext = unknown,\n  >(\n    filters: MutationFilters,\n  ): Mutation<TData, TError, TVariables, TContext> | undefined {\n    const defaultedFilters = { exact: true, ...filters }\n\n    return this.getAll().find((mutation) =>\n      matchMutation(defaultedFilters, mutation),\n    ) as Mutation<TData, TError, TVariables, TContext> | undefined\n  }\n\n  findAll(filters: MutationFilters = {}): Array<Mutation> {\n    return this.getAll().filter((mutation) => matchMutation(filters, mutation))\n  }\n\n  notify(event: MutationCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event)\n      })\n    })\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    const pausedMutations = this.getAll().filter((x) => x.state.isPaused)\n\n    return notifyManager.batch(() =>\n      Promise.all(\n        pausedMutations.map((mutation) => mutation.continue().catch(noop)),\n      ),\n    )\n  }\n}\n\nfunction scopeFor(mutation: Mutation<any, any, any, any>) {\n  return mutation.options.scope?.id\n}\n", "import { addToEnd, addToStart, ensureQueryFn } from './utils'\nimport type { QueryBehavior } from './query'\nimport type {\n  InfiniteData,\n  InfiniteQueryPageParamsOptions,\n  OmitKeyof,\n  QueryFunctionContext,\n  QueryKey,\n} from './types'\n\nexport function infiniteQueryBehavior<TQueryFnData, TError, TData, TPageParam>(\n  pages?: number,\n): QueryBehavior<TQueryFnData, TError, InfiniteData<TData, TPageParam>> {\n  return {\n    onFetch: (context, query) => {\n      const options = context.options as InfiniteQueryPageParamsOptions<TData>\n      const direction = context.fetchOptions?.meta?.fetchMore?.direction\n      const oldPages = context.state.data?.pages || []\n      const oldPageParams = context.state.data?.pageParams || []\n      let result: InfiniteData<unknown> = { pages: [], pageParams: [] }\n      let currentPage = 0\n\n      const fetchFn = async () => {\n        let cancelled = false\n        const addSignalProperty = (object: unknown) => {\n          Object.defineProperty(object, 'signal', {\n            enumerable: true,\n            get: () => {\n              if (context.signal.aborted) {\n                cancelled = true\n              } else {\n                context.signal.addEventListener('abort', () => {\n                  cancelled = true\n                })\n              }\n              return context.signal\n            },\n          })\n        }\n\n        const queryFn = ensureQueryFn(context.options, context.fetchOptions)\n\n        // Create function to fetch a page\n        const fetchPage = async (\n          data: InfiniteData<unknown>,\n          param: unknown,\n          previous?: boolean,\n        ): Promise<InfiniteData<unknown>> => {\n          if (cancelled) {\n            return Promise.reject()\n          }\n\n          if (param == null && data.pages.length) {\n            return Promise.resolve(data)\n          }\n\n          const queryFnContext: OmitKeyof<\n            QueryFunctionContext<QueryKey, unknown>,\n            'signal'\n          > = {\n            queryKey: context.queryKey,\n            pageParam: param,\n            direction: previous ? 'backward' : 'forward',\n            meta: context.options.meta,\n          }\n\n          addSignalProperty(queryFnContext)\n\n          const page = await queryFn(\n            queryFnContext as QueryFunctionContext<QueryKey, unknown>,\n          )\n\n          const { maxPages } = context.options\n          const addTo = previous ? addToStart : addToEnd\n\n          return {\n            pages: addTo(data.pages, page, maxPages),\n            pageParams: addTo(data.pageParams, param, maxPages),\n          }\n        }\n\n        // fetch next / previous page?\n        if (direction && oldPages.length) {\n          const previous = direction === 'backward'\n          const pageParamFn = previous ? getPreviousPageParam : getNextPageParam\n          const oldData = {\n            pages: oldPages,\n            pageParams: oldPageParams,\n          }\n          const param = pageParamFn(options, oldData)\n\n          result = await fetchPage(oldData, param, previous)\n        } else {\n          const remainingPages = pages ?? oldPages.length\n\n          // Fetch all pages\n          do {\n            const param =\n              currentPage === 0\n                ? (oldPageParams[0] ?? options.initialPageParam)\n                : getNextPageParam(options, result)\n            if (currentPage > 0 && param == null) {\n              break\n            }\n            result = await fetchPage(result, param)\n            currentPage++\n          } while (currentPage < remainingPages)\n        }\n\n        return result\n      }\n      if (context.options.persister) {\n        context.fetchFn = () => {\n          return context.options.persister?.(\n            fetchFn as any,\n            {\n              queryKey: context.queryKey,\n              meta: context.options.meta,\n              signal: context.signal,\n            },\n            query,\n          )\n        }\n      } else {\n        context.fetchFn = fetchFn\n      }\n    },\n  }\n}\n\nfunction getNextPageParam(\n  options: InfiniteQueryPageParamsOptions<any>,\n  { pages, pageParams }: InfiniteData<unknown>,\n): unknown | undefined {\n  const lastIndex = pages.length - 1\n  return pages.length > 0\n    ? options.getNextPageParam(\n        pages[lastIndex],\n        pages,\n        pageParams[lastIndex],\n        pageParams,\n      )\n    : undefined\n}\n\nfunction getPreviousPageParam(\n  options: InfiniteQueryPageParamsOptions<any>,\n  { pages, pageParams }: InfiniteData<unknown>,\n): unknown | undefined {\n  return pages.length > 0\n    ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams)\n    : undefined\n}\n\n/**\n * Checks if there is a next page.\n */\nexport function hasNextPage(\n  options: InfiniteQueryPageParamsOptions<any, any>,\n  data?: InfiniteData<unknown>,\n): boolean {\n  if (!data) return false\n  return getNextPageParam(options, data) != null\n}\n\n/**\n * Checks if there is a previous page.\n */\nexport function hasPreviousPage(\n  options: InfiniteQueryPageParamsOptions<any, any>,\n  data?: InfiniteData<unknown>,\n): boolean {\n  if (!data || !options.getPreviousPageParam) return false\n  return getPreviousPageParam(options, data) != null\n}\n", "import {\n  functionalUpdate,\n  hashKey,\n  hashQueryKeyByOptions,\n  noop,\n  partialMatchKey,\n  resolveStaleTime,\n  skipToken,\n} from './utils'\nimport { QueryCache } from './queryCache'\nimport { MutationCache } from './mutationCache'\nimport { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { notifyManager } from './notifyManager'\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior'\nimport type {\n  CancelOptions,\n  DataTag,\n  DefaultError,\n  DefaultOptions,\n  DefaultedQueryObserverOptions,\n  EnsureInfiniteQueryDataOptions,\n  EnsureQueryDataOptions,\n  FetchInfiniteQueryOptions,\n  FetchQueryOptions,\n  InfiniteData,\n  InvalidateOptions,\n  InvalidateQueryFilters,\n  MutationKey,\n  MutationObserverOptions,\n  MutationOptions,\n  NoInfer,\n  OmitKeyof,\n  QueryClientConfig,\n  QueryKey,\n  QueryObserverOptions,\n  QueryOptions,\n  RefetchOptions,\n  RefetchQueryFilters,\n  ResetOptions,\n  SetDataOptions,\n  UnsetMarker,\n} from './types'\nimport type { QueryState } from './query'\nimport type { MutationFilters, QueryFilters, Updater } from './utils'\n\n// TYPES\n\ninterface QueryDefaults {\n  queryKey: QueryKey\n  defaultOptions: OmitKeyof<QueryOptions<any, any, any>, 'queryKey'>\n}\n\ninterface MutationDefaults {\n  mutationKey: MutationKey\n  defaultOptions: MutationOptions<any, any, any, any>\n}\n\n// CLASS\n\nexport class QueryClient {\n  #queryCache: QueryCache\n  #mutationCache: MutationCache\n  #defaultOptions: DefaultOptions\n  #queryDefaults: Map<string, QueryDefaults>\n  #mutationDefaults: Map<string, MutationDefaults>\n  #mountCount: number\n  #unsubscribeFocus?: () => void\n  #unsubscribeOnline?: () => void\n\n  constructor(config: QueryClientConfig = {}) {\n    this.#queryCache = config.queryCache || new QueryCache()\n    this.#mutationCache = config.mutationCache || new MutationCache()\n    this.#defaultOptions = config.defaultOptions || {}\n    this.#queryDefaults = new Map()\n    this.#mutationDefaults = new Map()\n    this.#mountCount = 0\n  }\n\n  mount(): void {\n    this.#mountCount++\n    if (this.#mountCount !== 1) return\n\n    this.#unsubscribeFocus = focusManager.subscribe(async (focused) => {\n      if (focused) {\n        await this.resumePausedMutations()\n        this.#queryCache.onFocus()\n      }\n    })\n    this.#unsubscribeOnline = onlineManager.subscribe(async (online) => {\n      if (online) {\n        await this.resumePausedMutations()\n        this.#queryCache.onOnline()\n      }\n    })\n  }\n\n  unmount(): void {\n    this.#mountCount--\n    if (this.#mountCount !== 0) return\n\n    this.#unsubscribeFocus?.()\n    this.#unsubscribeFocus = undefined\n\n    this.#unsubscribeOnline?.()\n    this.#unsubscribeOnline = undefined\n  }\n\n  isFetching<\n    TQueryFilters extends QueryFilters<any, any, any, any> = QueryFilters,\n  >(filters?: TQueryFilters): number {\n    return this.#queryCache.findAll({ ...filters, fetchStatus: 'fetching' })\n      .length\n  }\n\n  isMutating<\n    TMutationFilters extends MutationFilters<any, any> = MutationFilters,\n  >(filters?: TMutationFilters): number {\n    return this.#mutationCache.findAll({ ...filters, status: 'pending' }).length\n  }\n\n  getQueryData<\n    TQueryFnData = unknown,\n    TTaggedQueryKey extends QueryKey = QueryKey,\n    TInferredQueryFnData = TTaggedQueryKey extends DataTag<\n      unknown,\n      infer TaggedValue,\n      unknown\n    >\n      ? TaggedValue\n      : TQueryFnData,\n  >(queryKey: TTaggedQueryKey): TInferredQueryFnData | undefined {\n    const options = this.defaultQueryOptions({ queryKey })\n\n    return this.#queryCache.get(options.queryHash)?.state.data as\n      | TInferredQueryFnData\n      | undefined\n  }\n\n  ensureQueryData<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: EnsureQueryDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData> {\n    const defaultedOptions = this.defaultQueryOptions(options)\n    const query = this.#queryCache.build(this, defaultedOptions)\n    const cachedData = query.state.data\n\n    if (cachedData === undefined) {\n      return this.fetchQuery(options)\n    }\n\n    if (\n      options.revalidateIfStale &&\n      query.isStaleByTime(resolveStaleTime(defaultedOptions.staleTime, query))\n    ) {\n      void this.prefetchQuery(defaultedOptions)\n    }\n\n    return Promise.resolve(cachedData)\n  }\n\n  getQueriesData<\n    TQueryFnData = unknown,\n    TQueryFilters extends QueryFilters<\n      any,\n      any,\n      any,\n      any\n    > = QueryFilters<TQueryFnData>,\n    TInferredQueryFnData = TQueryFilters extends QueryFilters<\n      infer TData,\n      any,\n      any,\n      any\n    >\n      ? TData\n      : TQueryFnData,\n  >(\n    filters: TQueryFilters,\n  ): Array<[QueryKey, TInferredQueryFnData | undefined]> {\n    return this.#queryCache.findAll(filters).map(({ queryKey, state }) => {\n      const data = state.data as TInferredQueryFnData | undefined\n      return [queryKey, data]\n    })\n  }\n\n  setQueryData<\n    TQueryFnData = unknown,\n    TTaggedQueryKey extends QueryKey = QueryKey,\n    TInferredQueryFnData = TTaggedQueryKey extends DataTag<\n      unknown,\n      infer TaggedValue,\n      unknown\n    >\n      ? TaggedValue\n      : TQueryFnData,\n  >(\n    queryKey: TTaggedQueryKey,\n    updater: Updater<\n      NoInfer<TInferredQueryFnData> | undefined,\n      NoInfer<TInferredQueryFnData> | undefined\n    >,\n    options?: SetDataOptions,\n  ): TInferredQueryFnData | undefined {\n    const defaultedOptions = this.defaultQueryOptions<\n      any,\n      any,\n      unknown,\n      any,\n      QueryKey\n    >({ queryKey })\n\n    const query = this.#queryCache.get<TInferredQueryFnData>(\n      defaultedOptions.queryHash,\n    )\n    const prevData = query?.state.data\n    const data = functionalUpdate(updater, prevData)\n\n    if (data === undefined) {\n      return undefined\n    }\n\n    return this.#queryCache\n      .build(this, defaultedOptions)\n      .setData(data, { ...options, manual: true })\n  }\n\n  setQueriesData<\n    TQueryFnData,\n    TQueryFilters extends QueryFilters<\n      any,\n      any,\n      any,\n      any\n    > = QueryFilters<TQueryFnData>,\n    TInferredQueryFnData = TQueryFilters extends QueryFilters<\n      infer TData,\n      any,\n      any,\n      any\n    >\n      ? TData\n      : TQueryFnData,\n  >(\n    filters: TQueryFilters,\n    updater: Updater<\n      NoInfer<TInferredQueryFnData> | undefined,\n      NoInfer<TInferredQueryFnData> | undefined\n    >,\n    options?: SetDataOptions,\n  ): Array<[QueryKey, TInferredQueryFnData | undefined]> {\n    return notifyManager.batch(() =>\n      this.#queryCache\n        .findAll(filters)\n        .map(({ queryKey }) => [\n          queryKey,\n          this.setQueryData<TInferredQueryFnData>(queryKey, updater, options),\n        ]),\n    )\n  }\n\n  getQueryState<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TTaggedQueryKey extends QueryKey = QueryKey,\n    TInferredQueryFnData = TTaggedQueryKey extends DataTag<\n      unknown,\n      infer TaggedValue,\n      unknown\n    >\n      ? TaggedValue\n      : TQueryFnData,\n    TInferredError = TTaggedQueryKey extends DataTag<\n      unknown,\n      unknown,\n      infer TaggedError\n    >\n      ? TaggedError extends UnsetMarker\n        ? TError\n        : TaggedError\n      : TError,\n  >(\n    queryKey: TTaggedQueryKey,\n  ): QueryState<TInferredQueryFnData, TInferredError> | undefined {\n    const options = this.defaultQueryOptions({ queryKey })\n    return this.#queryCache.get<TInferredQueryFnData, TInferredError>(\n      options.queryHash,\n    )?.state\n  }\n\n  removeQueries<\n    TQueryFilters extends QueryFilters<any, any, any, any> = QueryFilters,\n  >(filters?: TQueryFilters): void {\n    const queryCache = this.#queryCache\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query)\n      })\n    })\n  }\n\n  resetQueries<\n    TQueryFilters extends QueryFilters<any, any, any, any> = QueryFilters,\n  >(filters?: TQueryFilters, options?: ResetOptions): Promise<void> {\n    const queryCache = this.#queryCache\n\n    const refetchFilters: RefetchQueryFilters = {\n      type: 'active',\n      ...filters,\n    }\n\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset()\n      })\n      return this.refetchQueries(refetchFilters, options)\n    })\n  }\n\n  cancelQueries<\n    TQueryFilters extends QueryFilters<any, any, any, any> = QueryFilters,\n  >(filters?: TQueryFilters, cancelOptions: CancelOptions = {}): Promise<void> {\n    const defaultedCancelOptions = { revert: true, ...cancelOptions }\n\n    const promises = notifyManager.batch(() =>\n      this.#queryCache\n        .findAll(filters)\n        .map((query) => query.cancel(defaultedCancelOptions)),\n    )\n\n    return Promise.all(promises).then(noop).catch(noop)\n  }\n\n  invalidateQueries<\n    TInvalidateQueryFilters extends InvalidateQueryFilters<\n      any,\n      any,\n      any,\n      any\n    > = InvalidateQueryFilters,\n  >(\n    filters?: TInvalidateQueryFilters,\n    options: InvalidateOptions = {},\n  ): Promise<void> {\n    return notifyManager.batch(() => {\n      this.#queryCache.findAll(filters).forEach((query) => {\n        query.invalidate()\n      })\n\n      if (filters?.refetchType === 'none') {\n        return Promise.resolve()\n      }\n      const refetchFilters: RefetchQueryFilters = {\n        ...filters,\n        type: filters?.refetchType ?? filters?.type ?? 'active',\n      }\n      return this.refetchQueries(refetchFilters, options)\n    })\n  }\n\n  refetchQueries<\n    TRefetchQueryFilters extends RefetchQueryFilters<\n      any,\n      any,\n      any,\n      any\n    > = RefetchQueryFilters,\n  >(\n    filters?: TRefetchQueryFilters,\n    options: RefetchOptions = {},\n  ): Promise<void> {\n    const fetchOptions = {\n      ...options,\n      cancelRefetch: options.cancelRefetch ?? true,\n    }\n    const promises = notifyManager.batch(() =>\n      this.#queryCache\n        .findAll(filters)\n        .filter((query) => !query.isDisabled())\n        .map((query) => {\n          let promise = query.fetch(undefined, fetchOptions)\n          if (!fetchOptions.throwOnError) {\n            promise = promise.catch(noop)\n          }\n          return query.state.fetchStatus === 'paused'\n            ? Promise.resolve()\n            : promise\n        }),\n    )\n\n    return Promise.all(promises).then(noop)\n  }\n\n  fetchQuery<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = never,\n  >(\n    options: FetchQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): Promise<TData> {\n    const defaultedOptions = this.defaultQueryOptions(options)\n\n    // https://github.com/tannerlinsley/react-query/issues/652\n    if (defaultedOptions.retry === undefined) {\n      defaultedOptions.retry = false\n    }\n\n    const query = this.#queryCache.build(this, defaultedOptions)\n\n    return query.isStaleByTime(\n      resolveStaleTime(defaultedOptions.staleTime, query),\n    )\n      ? query.fetch(defaultedOptions)\n      : Promise.resolve(query.state.data as TData)\n  }\n\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void> {\n    return this.fetchQuery(options).then(noop).catch(noop)\n  }\n\n  fetchInfiniteQuery<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = unknown,\n  >(\n    options: FetchInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): Promise<InfiniteData<TData, TPageParam>> {\n    options.behavior = infiniteQueryBehavior<\n      TQueryFnData,\n      TError,\n      TData,\n      TPageParam\n    >(options.pages)\n    return this.fetchQuery(options as any)\n  }\n\n  prefetchInfiniteQuery<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = unknown,\n  >(\n    options: FetchInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): Promise<void> {\n    return this.fetchInfiniteQuery(options).then(noop).catch(noop)\n  }\n\n  ensureInfiniteQueryData<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = unknown,\n  >(\n    options: EnsureInfiniteQueryDataOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): Promise<InfiniteData<TData, TPageParam>> {\n    options.behavior = infiniteQueryBehavior<\n      TQueryFnData,\n      TError,\n      TData,\n      TPageParam\n    >(options.pages)\n\n    return this.ensureQueryData(options as any)\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    if (onlineManager.isOnline()) {\n      return this.#mutationCache.resumePausedMutations()\n    }\n    return Promise.resolve()\n  }\n\n  getQueryCache(): QueryCache {\n    return this.#queryCache\n  }\n\n  getMutationCache(): MutationCache {\n    return this.#mutationCache\n  }\n\n  getDefaultOptions(): DefaultOptions {\n    return this.#defaultOptions\n  }\n\n  setDefaultOptions(options: DefaultOptions): void {\n    this.#defaultOptions = options\n  }\n\n  setQueryDefaults<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryData = TQueryFnData,\n  >(\n    queryKey: QueryKey,\n    options: Partial<\n      OmitKeyof<\n        QueryObserverOptions<TQueryFnData, TError, TData, TQueryData>,\n        'queryKey'\n      >\n    >,\n  ): void {\n    this.#queryDefaults.set(hashKey(queryKey), {\n      queryKey,\n      defaultOptions: options,\n    })\n  }\n\n  getQueryDefaults(\n    queryKey: QueryKey,\n  ): OmitKeyof<QueryObserverOptions<any, any, any, any, any>, 'queryKey'> {\n    const defaults = [...this.#queryDefaults.values()]\n\n    const result: OmitKeyof<\n      QueryObserverOptions<any, any, any, any, any>,\n      'queryKey'\n    > = {}\n\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(queryKey, queryDefault.queryKey)) {\n        Object.assign(result, queryDefault.defaultOptions)\n      }\n    })\n    return result\n  }\n\n  setMutationDefaults<\n    TData = unknown,\n    TError = DefaultError,\n    TVariables = void,\n    TContext = unknown,\n  >(\n    mutationKey: MutationKey,\n    options: OmitKeyof<\n      MutationObserverOptions<TData, TError, TVariables, TContext>,\n      'mutationKey'\n    >,\n  ): void {\n    this.#mutationDefaults.set(hashKey(mutationKey), {\n      mutationKey,\n      defaultOptions: options,\n    })\n  }\n\n  getMutationDefaults(\n    mutationKey: MutationKey,\n  ): MutationObserverOptions<any, any, any, any> {\n    const defaults = [...this.#mutationDefaults.values()]\n\n    let result: MutationObserverOptions<any, any, any, any> = {}\n\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(mutationKey, queryDefault.mutationKey)) {\n        result = { ...result, ...queryDefault.defaultOptions }\n      }\n    })\n\n    return result\n  }\n\n  defaultQueryOptions<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = never,\n  >(\n    options:\n      | QueryObserverOptions<\n          TQueryFnData,\n          TError,\n          TData,\n          TQueryData,\n          TQueryKey,\n          TPageParam\n        >\n      | DefaultedQueryObserverOptions<\n          TQueryFnData,\n          TError,\n          TData,\n          TQueryData,\n          TQueryKey\n        >,\n  ): DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  > {\n    if (options._defaulted) {\n      return options as DefaultedQueryObserverOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryData,\n        TQueryKey\n      >\n    }\n\n    const defaultedOptions = {\n      ...this.#defaultOptions.queries,\n      ...this.getQueryDefaults(options.queryKey),\n      ...options,\n      _defaulted: true,\n    }\n\n    if (!defaultedOptions.queryHash) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions,\n      )\n    }\n\n    // dependent default values\n    if (defaultedOptions.refetchOnReconnect === undefined) {\n      defaultedOptions.refetchOnReconnect =\n        defaultedOptions.networkMode !== 'always'\n    }\n    if (defaultedOptions.throwOnError === undefined) {\n      defaultedOptions.throwOnError = !!defaultedOptions.suspense\n    }\n\n    if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n      defaultedOptions.networkMode = 'offlineFirst'\n    }\n\n    if (defaultedOptions.queryFn === skipToken) {\n      defaultedOptions.enabled = false\n    }\n\n    return defaultedOptions as DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  }\n\n  defaultMutationOptions<T extends MutationOptions<any, any, any, any>>(\n    options?: T,\n  ): T {\n    if (options?._defaulted) {\n      return options\n    }\n    return {\n      ...this.#defaultOptions.mutations,\n      ...(options?.mutationKey &&\n        this.getMutationDefaults(options.mutationKey)),\n      ...options,\n      _defaulted: true,\n    } as T\n  }\n\n  clear(): void {\n    this.#queryCache.clear()\n    this.#mutationCache.clear()\n  }\n}\n", "import { focusManager } from './focusManager'\nimport { notifyManager } from './notifyManager'\nimport { fetchState } from './query'\nimport { Subscribable } from './subscribable'\nimport { pendingThenable } from './thenable'\nimport {\n  isServer,\n  isValidTimeout,\n  noop,\n  replaceData,\n  resolveEnabled,\n  resolveStaleTime,\n  shallowEqualObjects,\n  timeUntilStale,\n} from './utils'\nimport type { FetchOptions, Query, QueryState } from './query'\nimport type { QueryClient } from './queryClient'\nimport type { PendingThenable, Thenable } from './thenable'\nimport type {\n  DefaultError,\n  DefaultedQueryObserverOptions,\n  PlaceholderDataFunction,\n  QueryKey,\n  QueryObserverBaseResult,\n  QueryObserverOptions,\n  QueryObserverResult,\n  QueryOptions,\n  RefetchOptions,\n} from './types'\n\ntype QueryObserverListener<TData, TError> = (\n  result: QueryObserverResult<TData, TError>,\n) => void\n\nexport interface NotifyOptions {\n  listeners?: boolean\n}\n\ninterface ObserverFetchOptions extends FetchOptions {\n  throwOnError?: boolean\n}\n\nexport class QueryObserver<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Subscribable<QueryObserverListener<TData, TError>> {\n  #client: QueryClient\n  #currentQuery: Query<TQueryFnData, TError, TQueryData, TQueryKey> = undefined!\n  #currentQueryInitialState: QueryState<TQueryData, TError> = undefined!\n  #currentResult: QueryObserverResult<TData, TError> = undefined!\n  #currentResultState?: QueryState<TQueryData, TError>\n  #currentResultOptions?: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n  #currentThenable: Thenable<TData>\n  #selectError: TError | null\n  #selectFn?: (data: TQueryData) => TData\n  #selectResult?: TData\n  // This property keeps track of the last query with defined data.\n  // It will be used to pass the previous data and query to the placeholder function between renders.\n  #lastQueryWithDefinedData?: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n  #staleTimeoutId?: ReturnType<typeof setTimeout>\n  #refetchIntervalId?: ReturnType<typeof setInterval>\n  #currentRefetchInterval?: number | false\n  #trackedProps = new Set<keyof QueryObserverResult>()\n\n  constructor(\n    client: QueryClient,\n    public options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super()\n\n    this.#client = client\n    this.#selectError = null\n    this.#currentThenable = pendingThenable()\n    if (!this.options.experimental_prefetchInRender) {\n      this.#currentThenable.reject(\n        new Error('experimental_prefetchInRender feature flag is not enabled'),\n      )\n    }\n\n    this.bindMethods()\n    this.setOptions(options)\n  }\n\n  protected bindMethods(): void {\n    this.refetch = this.refetch.bind(this)\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.#currentQuery.addObserver(this)\n\n      if (shouldFetchOnMount(this.#currentQuery, this.options)) {\n        this.#executeFetch()\n      } else {\n        this.updateResult()\n      }\n\n      this.#updateTimers()\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.destroy()\n    }\n  }\n\n  shouldFetchOnReconnect(): boolean {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnReconnect,\n    )\n  }\n\n  shouldFetchOnWindowFocus(): boolean {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus,\n    )\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.#clearStaleTimeout()\n    this.#clearRefetchInterval()\n    this.#currentQuery.removeObserver(this)\n  }\n\n  setOptions(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n    notifyOptions?: NotifyOptions,\n  ): void {\n    const prevOptions = this.options\n    const prevQuery = this.#currentQuery\n\n    this.options = this.#client.defaultQueryOptions(options)\n\n    if (\n      this.options.enabled !== undefined &&\n      typeof this.options.enabled !== 'boolean' &&\n      typeof this.options.enabled !== 'function' &&\n      typeof resolveEnabled(this.options.enabled, this.#currentQuery) !==\n        'boolean'\n    ) {\n      throw new Error(\n        'Expected enabled to be a boolean or a callback that returns a boolean',\n      )\n    }\n\n    this.#updateQuery()\n    this.#currentQuery.setOptions(this.options)\n\n    if (\n      prevOptions._defaulted &&\n      !shallowEqualObjects(this.options, prevOptions)\n    ) {\n      this.#client.getQueryCache().notify({\n        type: 'observerOptionsUpdated',\n        query: this.#currentQuery,\n        observer: this,\n      })\n    }\n\n    const mounted = this.hasListeners()\n\n    // Fetch if there are subscribers\n    if (\n      mounted &&\n      shouldFetchOptionally(\n        this.#currentQuery,\n        prevQuery,\n        this.options,\n        prevOptions,\n      )\n    ) {\n      this.#executeFetch()\n    }\n\n    // Update result\n    this.updateResult(notifyOptions)\n\n    // Update stale interval if needed\n    if (\n      mounted &&\n      (this.#currentQuery !== prevQuery ||\n        resolveEnabled(this.options.enabled, this.#currentQuery) !==\n          resolveEnabled(prevOptions.enabled, this.#currentQuery) ||\n        resolveStaleTime(this.options.staleTime, this.#currentQuery) !==\n          resolveStaleTime(prevOptions.staleTime, this.#currentQuery))\n    ) {\n      this.#updateStaleTimeout()\n    }\n\n    const nextRefetchInterval = this.#computeRefetchInterval()\n\n    // Update refetch interval if needed\n    if (\n      mounted &&\n      (this.#currentQuery !== prevQuery ||\n        resolveEnabled(this.options.enabled, this.#currentQuery) !==\n          resolveEnabled(prevOptions.enabled, this.#currentQuery) ||\n        nextRefetchInterval !== this.#currentRefetchInterval)\n    ) {\n      this.#updateRefetchInterval(nextRefetchInterval)\n    }\n  }\n\n  getOptimisticResult(\n    options: DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const query = this.#client.getQueryCache().build(this.#client, options)\n\n    const result = this.createResult(query, options)\n\n    if (shouldAssignObserverCurrentProperties(this, result)) {\n      // this assigns the optimistic result to the current Observer\n      // because if the query function changes, useQuery will be performing\n      // an effect where it would fetch again.\n      // When the fetch finishes, we perform a deep data cloning in order\n      // to reuse objects references. This deep data clone is performed against\n      // the `observer.currentResult.data` property\n      // When QueryKey changes, we refresh the query and get new `optimistic`\n      // result, while we leave the `observer.currentResult`, so when new data\n      // arrives, it finds the old `observer.currentResult` which is related\n      // to the old QueryKey. Which means that currentResult and selectData are\n      // out of sync already.\n      // To solve this, we move the cursor of the currentResult every time\n      // an observer reads an optimistic value.\n\n      // When keeping the previous data, the result doesn't change until new\n      // data arrives.\n      this.#currentResult = result\n      this.#currentResultOptions = this.options\n      this.#currentResultState = this.#currentQuery.state\n    }\n    return result\n  }\n\n  getCurrentResult(): QueryObserverResult<TData, TError> {\n    return this.#currentResult\n  }\n\n  trackResult(\n    result: QueryObserverResult<TData, TError>,\n    onPropTracked?: (key: keyof QueryObserverResult) => void,\n  ): QueryObserverResult<TData, TError> {\n    const trackedResult = {} as QueryObserverResult<TData, TError>\n\n    Object.keys(result).forEach((key) => {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: () => {\n          this.trackProp(key as keyof QueryObserverResult)\n          onPropTracked?.(key as keyof QueryObserverResult)\n          return result[key as keyof QueryObserverResult]\n        },\n      })\n    })\n\n    return trackedResult\n  }\n\n  trackProp(key: keyof QueryObserverResult) {\n    this.#trackedProps.add(key)\n  }\n\n  getCurrentQuery(): Query<TQueryFnData, TError, TQueryData, TQueryKey> {\n    return this.#currentQuery\n  }\n\n  refetch({ ...options }: RefetchOptions = {}): Promise<\n    QueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n    })\n  }\n\n  fetchOptimistic(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    const defaultedOptions = this.#client.defaultQueryOptions(options)\n\n    const query = this.#client\n      .getQueryCache()\n      .build(this.#client, defaultedOptions)\n\n    return query.fetch().then(() => this.createResult(query, defaultedOptions))\n  }\n\n  protected fetch(\n    fetchOptions: ObserverFetchOptions,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    return this.#executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true,\n    }).then(() => {\n      this.updateResult()\n      return this.#currentResult\n    })\n  }\n\n  #executeFetch(\n    fetchOptions?: Omit<ObserverFetchOptions, 'initialPromise'>,\n  ): Promise<TQueryData | undefined> {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.#updateQuery()\n\n    // Fetch\n    let promise: Promise<TQueryData | undefined> = this.#currentQuery.fetch(\n      this.options as QueryOptions<TQueryFnData, TError, TQueryData, TQueryKey>,\n      fetchOptions,\n    )\n\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  #updateStaleTimeout(): void {\n    this.#clearStaleTimeout()\n    const staleTime = resolveStaleTime(\n      this.options.staleTime,\n      this.#currentQuery,\n    )\n\n    if (isServer || this.#currentResult.isStale || !isValidTimeout(staleTime)) {\n      return\n    }\n\n    const time = timeUntilStale(this.#currentResult.dataUpdatedAt, staleTime)\n\n    // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n    const timeout = time + 1\n\n    this.#staleTimeoutId = setTimeout(() => {\n      if (!this.#currentResult.isStale) {\n        this.updateResult()\n      }\n    }, timeout)\n  }\n\n  #computeRefetchInterval() {\n    return (\n      (typeof this.options.refetchInterval === 'function'\n        ? this.options.refetchInterval(this.#currentQuery)\n        : this.options.refetchInterval) ?? false\n    )\n  }\n\n  #updateRefetchInterval(nextInterval: number | false): void {\n    this.#clearRefetchInterval()\n\n    this.#currentRefetchInterval = nextInterval\n\n    if (\n      isServer ||\n      resolveEnabled(this.options.enabled, this.#currentQuery) === false ||\n      !isValidTimeout(this.#currentRefetchInterval) ||\n      this.#currentRefetchInterval === 0\n    ) {\n      return\n    }\n\n    this.#refetchIntervalId = setInterval(() => {\n      if (\n        this.options.refetchIntervalInBackground ||\n        focusManager.isFocused()\n      ) {\n        this.#executeFetch()\n      }\n    }, this.#currentRefetchInterval)\n  }\n\n  #updateTimers(): void {\n    this.#updateStaleTimeout()\n    this.#updateRefetchInterval(this.#computeRefetchInterval())\n  }\n\n  #clearStaleTimeout(): void {\n    if (this.#staleTimeoutId) {\n      clearTimeout(this.#staleTimeoutId)\n      this.#staleTimeoutId = undefined\n    }\n  }\n\n  #clearRefetchInterval(): void {\n    if (this.#refetchIntervalId) {\n      clearInterval(this.#refetchIntervalId)\n      this.#refetchIntervalId = undefined\n    }\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const prevQuery = this.#currentQuery\n    const prevOptions = this.options\n    const prevResult = this.#currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n    const prevResultState = this.#currentResultState\n    const prevResultOptions = this.#currentResultOptions\n    const queryChange = query !== prevQuery\n    const queryInitialState = queryChange\n      ? query.state\n      : this.#currentQueryInitialState\n\n    const { state } = query\n    let newState = { ...state }\n    let isPlaceholderData = false\n    let data: TData | undefined\n\n    // Optimistically set result in fetching state if needed\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners()\n\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options)\n\n      const fetchOptionally =\n        mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions)\n\n      if (fetchOnMount || fetchOptionally) {\n        newState = {\n          ...newState,\n          ...fetchState(state.data, query.options),\n        }\n      }\n      if (options._optimisticResults === 'isRestoring') {\n        newState.fetchStatus = 'idle'\n      }\n    }\n\n    let { error, errorUpdatedAt, status } = newState\n\n    // Select data if needed\n    if (options.select && newState.data !== undefined) {\n      // Memoize select result\n      if (\n        prevResult &&\n        newState.data === prevResultState?.data &&\n        options.select === this.#selectFn\n      ) {\n        data = this.#selectResult\n      } else {\n        try {\n          this.#selectFn = options.select\n          data = options.select(newState.data)\n          data = replaceData(prevResult?.data, data, options)\n          this.#selectResult = data\n          this.#selectError = null\n        } catch (selectError) {\n          this.#selectError = selectError as TError\n        }\n      }\n    }\n    // Use query data\n    else {\n      data = newState.data as unknown as TData\n    }\n\n    // Show placeholder data if needed\n    if (\n      options.placeholderData !== undefined &&\n      data === undefined &&\n      status === 'pending'\n    ) {\n      let placeholderData\n\n      // Memoize placeholder data\n      if (\n        prevResult?.isPlaceholderData &&\n        options.placeholderData === prevResultOptions?.placeholderData\n      ) {\n        placeholderData = prevResult.data\n      } else {\n        placeholderData =\n          typeof options.placeholderData === 'function'\n            ? (\n                options.placeholderData as unknown as PlaceholderDataFunction<TQueryData>\n              )(\n                this.#lastQueryWithDefinedData?.state.data,\n                this.#lastQueryWithDefinedData as any,\n              )\n            : options.placeholderData\n        if (options.select && placeholderData !== undefined) {\n          try {\n            placeholderData = options.select(placeholderData)\n            this.#selectError = null\n          } catch (selectError) {\n            this.#selectError = selectError as TError\n          }\n        }\n      }\n\n      if (placeholderData !== undefined) {\n        status = 'success'\n        data = replaceData(\n          prevResult?.data,\n          placeholderData as unknown,\n          options,\n        ) as TData\n        isPlaceholderData = true\n      }\n    }\n\n    if (this.#selectError) {\n      error = this.#selectError as any\n      data = this.#selectResult\n      errorUpdatedAt = Date.now()\n      status = 'error'\n    }\n\n    const isFetching = newState.fetchStatus === 'fetching'\n    const isPending = status === 'pending'\n    const isError = status === 'error'\n\n    const isLoading = isPending && isFetching\n    const hasData = data !== undefined\n\n    const result: QueryObserverBaseResult<TData, TError> = {\n      status,\n      fetchStatus: newState.fetchStatus,\n      isPending,\n      isSuccess: status === 'success',\n      isError,\n      isInitialLoading: isLoading,\n      isLoading,\n      data,\n      dataUpdatedAt: newState.dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: newState.fetchFailureCount,\n      failureReason: newState.fetchFailureReason,\n      errorUpdateCount: newState.errorUpdateCount,\n      isFetched: newState.dataUpdateCount > 0 || newState.errorUpdateCount > 0,\n      isFetchedAfterMount:\n        newState.dataUpdateCount > queryInitialState.dataUpdateCount ||\n        newState.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isPending,\n      isLoadingError: isError && !hasData,\n      isPaused: newState.fetchStatus === 'paused',\n      isPlaceholderData,\n      isRefetchError: isError && hasData,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      promise: this.#currentThenable,\n    }\n\n    const nextResult = result as QueryObserverResult<TData, TError>\n\n    if (this.options.experimental_prefetchInRender) {\n      const finalizeThenableIfPossible = (thenable: PendingThenable<TData>) => {\n        if (nextResult.status === 'error') {\n          thenable.reject(nextResult.error)\n        } else if (nextResult.data !== undefined) {\n          thenable.resolve(nextResult.data)\n        }\n      }\n\n      /**\n       * Create a new thenable and result promise when the results have changed\n       */\n      const recreateThenable = () => {\n        const pending =\n          (this.#currentThenable =\n          nextResult.promise =\n            pendingThenable())\n\n        finalizeThenableIfPossible(pending)\n      }\n\n      const prevThenable = this.#currentThenable\n      switch (prevThenable.status) {\n        case 'pending':\n          // Finalize the previous thenable if it was pending\n          // and we are still observing the same query\n          if (query.queryHash === prevQuery.queryHash) {\n            finalizeThenableIfPossible(prevThenable)\n          }\n          break\n        case 'fulfilled':\n          if (\n            nextResult.status === 'error' ||\n            nextResult.data !== prevThenable.value\n          ) {\n            recreateThenable()\n          }\n          break\n        case 'rejected':\n          if (\n            nextResult.status !== 'error' ||\n            nextResult.error !== prevThenable.reason\n          ) {\n            recreateThenable()\n          }\n          break\n      }\n    }\n\n    return nextResult\n  }\n\n  updateResult(notifyOptions?: NotifyOptions): void {\n    const prevResult = this.#currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n\n    const nextResult = this.createResult(this.#currentQuery, this.options)\n\n    this.#currentResultState = this.#currentQuery.state\n    this.#currentResultOptions = this.options\n\n    if (this.#currentResultState.data !== undefined) {\n      this.#lastQueryWithDefinedData = this.#currentQuery\n    }\n\n    // Only notify and update result if something has changed\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return\n    }\n\n    this.#currentResult = nextResult\n\n    // Determine which callbacks to trigger\n    const defaultNotifyOptions: NotifyOptions = {}\n\n    const shouldNotifyListeners = (): boolean => {\n      if (!prevResult) {\n        return true\n      }\n\n      const { notifyOnChangeProps } = this.options\n      const notifyOnChangePropsValue =\n        typeof notifyOnChangeProps === 'function'\n          ? notifyOnChangeProps()\n          : notifyOnChangeProps\n\n      if (\n        notifyOnChangePropsValue === 'all' ||\n        (!notifyOnChangePropsValue && !this.#trackedProps.size)\n      ) {\n        return true\n      }\n\n      const includedProps = new Set(\n        notifyOnChangePropsValue ?? this.#trackedProps,\n      )\n\n      if (this.options.throwOnError) {\n        includedProps.add('error')\n      }\n\n      return Object.keys(this.#currentResult).some((key) => {\n        const typedKey = key as keyof QueryObserverResult\n        const changed = this.#currentResult[typedKey] !== prevResult[typedKey]\n\n        return changed && includedProps.has(typedKey)\n      })\n    }\n\n    if (notifyOptions?.listeners !== false && shouldNotifyListeners()) {\n      defaultNotifyOptions.listeners = true\n    }\n\n    this.#notify({ ...defaultNotifyOptions, ...notifyOptions })\n  }\n\n  #updateQuery(): void {\n    const query = this.#client.getQueryCache().build(this.#client, this.options)\n\n    if (query === this.#currentQuery) {\n      return\n    }\n\n    const prevQuery = this.#currentQuery as\n      | Query<TQueryFnData, TError, TQueryData, TQueryKey>\n      | undefined\n    this.#currentQuery = query\n    this.#currentQueryInitialState = query.state\n\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this)\n      query.addObserver(this)\n    }\n  }\n\n  onQueryUpdate(): void {\n    this.updateResult()\n\n    if (this.hasListeners()) {\n      this.#updateTimers()\n    }\n  }\n\n  #notify(notifyOptions: NotifyOptions): void {\n    notifyManager.batch(() => {\n      // First, trigger the listeners\n      if (notifyOptions.listeners) {\n        this.listeners.forEach((listener) => {\n          listener(this.#currentResult)\n        })\n      }\n\n      // Then the cache listeners\n      this.#client.getQueryCache().notify({\n        query: this.#currentQuery,\n        type: 'observerResultsUpdated',\n      })\n    })\n  }\n}\n\nfunction shouldLoadOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any>,\n): boolean {\n  return (\n    resolveEnabled(options.enabled, query) !== false &&\n    query.state.data === undefined &&\n    !(query.state.status === 'error' && options.retryOnMount === false)\n  )\n}\n\nfunction shouldFetchOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    shouldLoadOnMount(query, options) ||\n    (query.state.data !== undefined &&\n      shouldFetchOn(query, options, options.refetchOnMount))\n  )\n}\n\nfunction shouldFetchOn(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  field: (typeof options)['refetchOnMount'] &\n    (typeof options)['refetchOnWindowFocus'] &\n    (typeof options)['refetchOnReconnect'],\n) {\n  if (resolveEnabled(options.enabled, query) !== false) {\n    const value = typeof field === 'function' ? field(query) : field\n\n    return value === 'always' || (value !== false && isStale(query, options))\n  }\n  return false\n}\n\nfunction shouldFetchOptionally(\n  query: Query<any, any, any, any>,\n  prevQuery: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  prevOptions: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    (query !== prevQuery ||\n      resolveEnabled(prevOptions.enabled, query) === false) &&\n    (!options.suspense || query.state.status !== 'error') &&\n    isStale(query, options)\n  )\n}\n\nfunction isStale(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    resolveEnabled(options.enabled, query) !== false &&\n    query.isStaleByTime(resolveStaleTime(options.staleTime, query))\n  )\n}\n\n// this function would decide if we will update the observer's 'current'\n// properties after an optimistic reading via getOptimisticResult\nfunction shouldAssignObserverCurrentProperties<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  optimisticResult: QueryObserverResult<TData, TError>,\n) {\n  // if the newly created result isn't what the observer is holding as current,\n  // then we'll need to update the properties as well\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true\n  }\n\n  // basically, just keep previous properties if nothing changed\n  return false\n}\n", "import { notify<PERSON>ana<PERSON> } from './notifyManager'\nimport { QueryObserver } from './queryObserver'\nimport { Subscribable } from './subscribable'\nimport { replaceEqualDeep } from './utils'\nimport type {\n  DefaultedQueryObserverOptions,\n  QueryObserverOptions,\n  QueryObserverResult,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { NotifyOptions } from './queryObserver'\n\nfunction difference<T>(array1: Array<T>, array2: Array<T>): Array<T> {\n  return array1.filter((x) => !array2.includes(x))\n}\n\nfunction replaceAt<T>(array: Array<T>, index: number, value: T): Array<T> {\n  const copy = array.slice(0)\n  copy[index] = value\n  return copy\n}\n\ntype QueriesObserverListener = (result: Array<QueryObserverResult>) => void\n\ntype CombineFn<TCombinedResult> = (\n  result: Array<QueryObserverResult>,\n) => TCombinedResult\n\nexport interface QueriesObserverOptions<\n  TCombinedResult = Array<QueryObserverResult>,\n> {\n  combine?: CombineFn<TCombinedResult>\n}\n\nexport class QueriesObserver<\n  TCombinedResult = Array<QueryObserverResult>,\n> extends Subscribable<QueriesObserverListener> {\n  #client: QueryClient\n  #result!: Array<QueryObserverResult>\n  #queries: Array<QueryObserverOptions>\n  #options?: QueriesObserverOptions<TCombinedResult>\n  #observers: Array<QueryObserver>\n  #combinedResult?: TCombinedResult\n  #lastCombine?: CombineFn<TCombinedResult>\n  #lastResult?: Array<QueryObserverResult>\n\n  constructor(\n    client: QueryClient,\n    queries: Array<QueryObserverOptions<any, any, any, any, any>>,\n    options?: QueriesObserverOptions<TCombinedResult>,\n  ) {\n    super()\n\n    this.#client = client\n    this.#options = options\n    this.#queries = []\n    this.#observers = []\n    this.#result = []\n\n    this.setQueries(queries)\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.#observers.forEach((observer) => {\n        observer.subscribe((result) => {\n          this.#onUpdate(observer, result)\n        })\n      })\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.listeners.size) {\n      this.destroy()\n    }\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.#observers.forEach((observer) => {\n      observer.destroy()\n    })\n  }\n\n  setQueries(\n    queries: Array<QueryObserverOptions>,\n    options?: QueriesObserverOptions<TCombinedResult>,\n    notifyOptions?: NotifyOptions,\n  ): void {\n    this.#queries = queries\n    this.#options = options\n\n    if (process.env.NODE_ENV !== 'production') {\n      const queryHashes = queries.map(\n        (query) => this.#client.defaultQueryOptions(query).queryHash,\n      )\n      if (new Set(queryHashes).size !== queryHashes.length) {\n        console.warn(\n          '[QueriesObserver]: Duplicate Queries found. This might result in unexpected behavior.',\n        )\n      }\n    }\n\n    notifyManager.batch(() => {\n      const prevObservers = this.#observers\n\n      const newObserverMatches = this.#findMatchingObservers(this.#queries)\n\n      // set options for the new observers to notify of changes\n      newObserverMatches.forEach((match) =>\n        match.observer.setOptions(match.defaultedQueryOptions, notifyOptions),\n      )\n\n      const newObservers = newObserverMatches.map((match) => match.observer)\n      const newResult = newObservers.map((observer) =>\n        observer.getCurrentResult(),\n      )\n\n      const hasIndexChange = newObservers.some(\n        (observer, index) => observer !== prevObservers[index],\n      )\n\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return\n      }\n\n      this.#observers = newObservers\n      this.#result = newResult\n\n      if (!this.hasListeners()) {\n        return\n      }\n\n      difference(prevObservers, newObservers).forEach((observer) => {\n        observer.destroy()\n      })\n\n      difference(newObservers, prevObservers).forEach((observer) => {\n        observer.subscribe((result) => {\n          this.#onUpdate(observer, result)\n        })\n      })\n\n      this.#notify()\n    })\n  }\n\n  getCurrentResult(): Array<QueryObserverResult> {\n    return this.#result\n  }\n\n  getQueries() {\n    return this.#observers.map((observer) => observer.getCurrentQuery())\n  }\n\n  getObservers() {\n    return this.#observers\n  }\n\n  getOptimisticResult(\n    queries: Array<QueryObserverOptions>,\n    combine: CombineFn<TCombinedResult> | undefined,\n  ): [\n    rawResult: Array<QueryObserverResult>,\n    combineResult: (r?: Array<QueryObserverResult>) => TCombinedResult,\n    trackResult: () => Array<QueryObserverResult>,\n  ] {\n    const matches = this.#findMatchingObservers(queries)\n    const result = matches.map((match) =>\n      match.observer.getOptimisticResult(match.defaultedQueryOptions),\n    )\n\n    return [\n      result,\n      (r?: Array<QueryObserverResult>) => {\n        return this.#combineResult(r ?? result, combine)\n      },\n      () => {\n        return this.#trackResult(result, queries)\n      },\n    ]\n  }\n\n  #trackResult(\n    result: Array<QueryObserverResult>,\n    queries: Array<QueryObserverOptions>,\n  ) {\n    const matches = this.#findMatchingObservers(queries)\n\n    return matches.map((match, index) => {\n      const observerResult = result[index]!\n      return !match.defaultedQueryOptions.notifyOnChangeProps\n        ? match.observer.trackResult(observerResult, (accessedProp) => {\n            // track property on all observers to ensure proper (synchronized) tracking (#7000)\n            matches.forEach((m) => {\n              m.observer.trackProp(accessedProp)\n            })\n          })\n        : observerResult\n    })\n  }\n\n  #combineResult(\n    input: Array<QueryObserverResult>,\n    combine: CombineFn<TCombinedResult> | undefined,\n  ): TCombinedResult {\n    if (combine) {\n      if (\n        !this.#combinedResult ||\n        this.#result !== this.#lastResult ||\n        combine !== this.#lastCombine\n      ) {\n        this.#lastCombine = combine\n        this.#lastResult = this.#result\n        this.#combinedResult = replaceEqualDeep(\n          this.#combinedResult,\n          combine(input),\n        )\n      }\n\n      return this.#combinedResult\n    }\n    return input as any\n  }\n\n  #findMatchingObservers(\n    queries: Array<QueryObserverOptions>,\n  ): Array<QueryObserverMatch> {\n    const prevObserversMap = new Map(\n      this.#observers.map((observer) => [observer.options.queryHash, observer]),\n    )\n\n    const observers: Array<QueryObserverMatch> = []\n\n    queries.forEach((options) => {\n      const defaultedOptions = this.#client.defaultQueryOptions(options)\n      const match = prevObserversMap.get(defaultedOptions.queryHash)\n      if (match) {\n        observers.push({\n          defaultedQueryOptions: defaultedOptions,\n          observer: match,\n        })\n      } else {\n        observers.push({\n          defaultedQueryOptions: defaultedOptions,\n          observer: new QueryObserver(this.#client, defaultedOptions),\n        })\n      }\n    })\n\n    return observers\n  }\n\n  #onUpdate(observer: QueryObserver, result: QueryObserverResult): void {\n    const index = this.#observers.indexOf(observer)\n    if (index !== -1) {\n      this.#result = replaceAt(this.#result, index, result)\n      this.#notify()\n    }\n  }\n\n  #notify(): void {\n    if (this.hasListeners()) {\n      const previousResult = this.#combinedResult\n      const newResult = this.#combineResult(\n        this.#trackResult(this.#result, this.#queries),\n        this.#options?.combine,\n      )\n\n      if (previousResult !== newResult) {\n        notifyManager.batch(() => {\n          this.listeners.forEach((listener) => {\n            listener(this.#result)\n          })\n        })\n      }\n    }\n  }\n}\n\ntype QueryObserverMatch = {\n  defaultedQueryOptions: DefaultedQueryObserverOptions\n  observer: QueryObserver\n}\n", "import { QueryObserver } from './queryObserver'\nimport {\n  hasNextPage,\n  hasPreviousPage,\n  infiniteQueryBehavior,\n} from './infiniteQueryBehavior'\nimport type { Subscribable } from './subscribable'\nimport type {\n  DefaultError,\n  DefaultedInfiniteQueryObserverOptions,\n  FetchNextPageOptions,\n  FetchPreviousPageOptions,\n  InfiniteData,\n  InfiniteQueryObserverBaseResult,\n  InfiniteQueryObserverOptions,\n  InfiniteQueryObserverResult,\n  QueryKey,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { NotifyOptions } from './queryObserver'\nimport type { Query } from './query'\n\ntype InfiniteQueryObserverListener<TData, TError> = (\n  result: InfiniteQueryObserverResult<TData, TError>,\n) => void\n\nexport class InfiniteQueryObserver<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> extends QueryObserver<\n  TQueryFnData,\n  TError,\n  TData,\n  InfiniteData<TQueryData, TPageParam>,\n  TQueryKey\n> {\n  // Type override\n  subscribe!: Subscribable<\n    InfiniteQueryObserverListener<TData, TError>\n  >['subscribe']\n\n  // Type override\n  getCurrentResult!: ReplaceReturnType<\n    QueryObserver<\n      TQueryFnData,\n      TError,\n      TData,\n      InfiniteData<TQueryData, TPageParam>,\n      TQueryKey\n    >['getCurrentResult'],\n    InfiniteQueryObserverResult<TData, TError>\n  >\n\n  // Type override\n  protected fetch!: ReplaceReturnType<\n    QueryObserver<\n      TQueryFnData,\n      TError,\n      TData,\n      InfiniteData<TQueryData, TPageParam>,\n      TQueryKey\n    >['fetch'],\n    Promise<InfiniteQueryObserverResult<TData, TError>>\n  >\n\n  constructor(\n    client: QueryClient,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey,\n      TPageParam\n    >,\n  ) {\n    super(client, options)\n  }\n\n  protected bindMethods(): void {\n    super.bindMethods()\n    this.fetchNextPage = this.fetchNextPage.bind(this)\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this)\n  }\n\n  setOptions(\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey,\n      TPageParam\n    >,\n    notifyOptions?: NotifyOptions,\n  ): void {\n    super.setOptions(\n      {\n        ...options,\n        behavior: infiniteQueryBehavior(),\n      },\n      notifyOptions,\n    )\n  }\n\n  getOptimisticResult(\n    options: DefaultedInfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): InfiniteQueryObserverResult<TData, TError> {\n    options.behavior = infiniteQueryBehavior()\n    return super.getOptimisticResult(options) as InfiniteQueryObserverResult<\n      TData,\n      TError\n    >\n  }\n\n  fetchNextPage(\n    options?: FetchNextPageOptions,\n  ): Promise<InfiniteQueryObserverResult<TData, TError>> {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: 'forward' },\n      },\n    })\n  }\n\n  fetchPreviousPage(\n    options?: FetchPreviousPageOptions,\n  ): Promise<InfiniteQueryObserverResult<TData, TError>> {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: 'backward' },\n      },\n    })\n  }\n\n  protected createResult(\n    query: Query<\n      TQueryFnData,\n      TError,\n      InfiniteData<TQueryData, TPageParam>,\n      TQueryKey\n    >,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): InfiniteQueryObserverResult<TData, TError> {\n    const { state } = query\n    const parentResult = super.createResult(query, options)\n\n    const { isFetching, isRefetching, isError, isRefetchError } = parentResult\n    const fetchDirection = state.fetchMeta?.fetchMore?.direction\n\n    const isFetchNextPageError = isError && fetchDirection === 'forward'\n    const isFetchingNextPage = isFetching && fetchDirection === 'forward'\n\n    const isFetchPreviousPageError = isError && fetchDirection === 'backward'\n    const isFetchingPreviousPage = isFetching && fetchDirection === 'backward'\n\n    const result: InfiniteQueryObserverBaseResult<TData, TError> = {\n      ...parentResult,\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: hasNextPage(options, state.data),\n      hasPreviousPage: hasPreviousPage(options, state.data),\n      isFetchNextPageError,\n      isFetchingNextPage,\n      isFetchPreviousPageError,\n      isFetchingPreviousPage,\n      isRefetchError:\n        isRefetchError && !isFetchNextPageError && !isFetchPreviousPageError,\n      isRefetching:\n        isRefetching && !isFetchingNextPage && !isFetchingPreviousPage,\n    }\n\n    return result as InfiniteQueryObserverResult<TData, TError>\n  }\n}\n\ntype ReplaceReturnType<\n  TFunction extends (...args: Array<any>) => unknown,\n  TReturn,\n> = (...args: Parameters<TFunction>) => TReturn\n", "import { getDefaultState } from './mutation'\nimport { notify<PERSON>anager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport { hashKey, shallowEqualObjects } from './utils'\nimport type { QueryClient } from './queryClient'\nimport type {\n  DefaultError,\n  MutateOptions,\n  MutationObserverOptions,\n  MutationObserverResult,\n} from './types'\nimport type { Action, Mutation } from './mutation'\n\n// TYPES\n\ntype MutationObserverListener<TData, TError, TVariables, TContext> = (\n  result: MutationObserverResult<TData, TError, TVariables, TContext>,\n) => void\n\n// CLASS\n\nexport class MutationObserver<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n> extends Subscribable<\n  MutationObserverListener<TData, TError, TVariables, TContext>\n> {\n  options!: MutationObserverOptions<TData, TError, TVariables, TContext>\n\n  #client: QueryClient\n  #currentResult: MutationObserverResult<TData, TError, TVariables, TContext> =\n    undefined!\n  #currentMutation?: Mutation<TData, TError, TVariables, TContext>\n  #mutateOptions?: MutateOptions<TData, TError, TVariables, TContext>\n\n  constructor(\n    client: QueryClient,\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    super()\n\n    this.#client = client\n    this.setOptions(options)\n    this.bindMethods()\n    this.#updateResult()\n  }\n\n  protected bindMethods(): void {\n    this.mutate = this.mutate.bind(this)\n    this.reset = this.reset.bind(this)\n  }\n\n  setOptions(\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    const prevOptions = this.options as\n      | MutationObserverOptions<TData, TError, TVariables, TContext>\n      | undefined\n    this.options = this.#client.defaultMutationOptions(options)\n    if (!shallowEqualObjects(this.options, prevOptions)) {\n      this.#client.getMutationCache().notify({\n        type: 'observerOptionsUpdated',\n        mutation: this.#currentMutation,\n        observer: this,\n      })\n    }\n\n    if (\n      prevOptions?.mutationKey &&\n      this.options.mutationKey &&\n      hashKey(prevOptions.mutationKey) !== hashKey(this.options.mutationKey)\n    ) {\n      this.reset()\n    } else if (this.#currentMutation?.state.status === 'pending') {\n      this.#currentMutation.setOptions(this.options)\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.#currentMutation?.removeObserver(this)\n    }\n  }\n\n  onMutationUpdate(action: Action<TData, TError, TVariables, TContext>): void {\n    this.#updateResult()\n\n    this.#notify(action)\n  }\n\n  getCurrentResult(): MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  > {\n    return this.#currentResult\n  }\n\n  reset(): void {\n    // reset needs to remove the observer from the mutation because there is no way to \"get it back\"\n    // another mutate call will yield a new mutation!\n    this.#currentMutation?.removeObserver(this)\n    this.#currentMutation = undefined\n    this.#updateResult()\n    this.#notify()\n  }\n\n  mutate(\n    variables: TVariables,\n    options?: MutateOptions<TData, TError, TVariables, TContext>,\n  ): Promise<TData> {\n    this.#mutateOptions = options\n\n    this.#currentMutation?.removeObserver(this)\n\n    this.#currentMutation = this.#client\n      .getMutationCache()\n      .build(this.#client, this.options)\n\n    this.#currentMutation.addObserver(this)\n\n    return this.#currentMutation.execute(variables)\n  }\n\n  #updateResult(): void {\n    const state =\n      this.#currentMutation?.state ??\n      getDefaultState<TData, TError, TVariables, TContext>()\n\n    this.#currentResult = {\n      ...state,\n      isPending: state.status === 'pending',\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset,\n    } as MutationObserverResult<TData, TError, TVariables, TContext>\n  }\n\n  #notify(action?: Action<TData, TError, TVariables, TContext>): void {\n    notifyManager.batch(() => {\n      // First trigger the mutate callbacks\n      if (this.#mutateOptions && this.hasListeners()) {\n        const variables = this.#currentResult.variables!\n        const context = this.#currentResult.context\n\n        if (action?.type === 'success') {\n          this.#mutateOptions.onSuccess?.(action.data, variables, context!)\n          this.#mutateOptions.onSettled?.(action.data, null, variables, context)\n        } else if (action?.type === 'error') {\n          this.#mutateOptions.onError?.(action.error, variables, context)\n          this.#mutateOptions.onSettled?.(\n            undefined,\n            action.error,\n            variables,\n            context,\n          )\n        }\n      }\n\n      // Then trigger the listeners\n      this.listeners.forEach((listener) => {\n        listener(this.#currentResult)\n      })\n    })\n  }\n}\n", "import type {\n  DefaultError,\n  <PERSON><PERSON><PERSON><PERSON>,\n  MutationMeta,\n  MutationOptions,\n  MutationScope,\n  QueryKey,\n  QueryMeta,\n  QueryOptions,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { Query, QueryState } from './query'\nimport type { Mutation, MutationState } from './mutation'\n\n// TYPES\ntype TransformerFn = (data: any) => any\nfunction defaultTransformerFn(data: any): any {\n  return data\n}\n\nexport interface DehydrateOptions {\n  serializeData?: TransformerFn\n  shouldDehydrateMutation?: (mutation: Mutation) => boolean\n  shouldDehydrateQuery?: (query: Query) => boolean\n}\n\nexport interface HydrateOptions {\n  defaultOptions?: {\n    deserializeData?: TransformerFn\n    queries?: QueryOptions\n    mutations?: MutationOptions<unknown, DefaultError, unknown, unknown>\n  }\n}\n\ninterface DehydratedMutation {\n  mutationKey?: MutationKey\n  state: MutationState\n  meta?: MutationMeta\n  scope?: MutationScope\n}\n\ninterface DehydratedQuery {\n  queryHash: string\n  queryKey: QueryKey\n  state: QueryState\n  promise?: Promise<unknown>\n  meta?: QueryMeta\n}\n\nexport interface DehydratedState {\n  mutations: Array<DehydratedMutation>\n  queries: Array<DehydratedQuery>\n}\n\n// FUNCTIONS\n\nfunction dehydrateMutation(mutation: Mutation): DehydratedMutation {\n  return {\n    mutationKey: mutation.options.mutationKey,\n    state: mutation.state,\n    ...(mutation.options.scope && { scope: mutation.options.scope }),\n    ...(mutation.meta && { meta: mutation.meta }),\n  }\n}\n\n// Most config is not dehydrated but instead meant to configure again when\n// consuming the de/rehydrated data, typically with useQuery on the client.\n// Sometimes it might make sense to prefetch data on the server and include\n// in the html-payload, but not consume it on the initial render.\nfunction dehydrateQuery(\n  query: Query,\n  serializeData: TransformerFn,\n): DehydratedQuery {\n  return {\n    state: {\n      ...query.state,\n      ...(query.state.data !== undefined && {\n        data: serializeData(query.state.data),\n      }),\n    },\n    queryKey: query.queryKey,\n    queryHash: query.queryHash,\n    ...(query.state.status === 'pending' && {\n      promise: query.promise?.then(serializeData).catch((error) => {\n        if (process.env.NODE_ENV !== 'production') {\n          console.error(\n            `A query that was dehydrated as pending ended up rejecting. [${query.queryHash}]: ${error}; The error will be redacted in production builds`,\n          )\n        }\n        return Promise.reject(new Error('redacted'))\n      }),\n    }),\n    ...(query.meta && { meta: query.meta }),\n  }\n}\n\nexport function defaultShouldDehydrateMutation(mutation: Mutation) {\n  return mutation.state.isPaused\n}\n\nexport function defaultShouldDehydrateQuery(query: Query) {\n  return query.state.status === 'success'\n}\n\nexport function dehydrate(\n  client: QueryClient,\n  options: DehydrateOptions = {},\n): DehydratedState {\n  const filterMutation =\n    options.shouldDehydrateMutation ??\n    client.getDefaultOptions().dehydrate?.shouldDehydrateMutation ??\n    defaultShouldDehydrateMutation\n\n  const mutations = client\n    .getMutationCache()\n    .getAll()\n    .flatMap((mutation) =>\n      filterMutation(mutation) ? [dehydrateMutation(mutation)] : [],\n    )\n\n  const filterQuery =\n    options.shouldDehydrateQuery ??\n    client.getDefaultOptions().dehydrate?.shouldDehydrateQuery ??\n    defaultShouldDehydrateQuery\n\n  const serializeData =\n    options.serializeData ??\n    client.getDefaultOptions().dehydrate?.serializeData ??\n    defaultTransformerFn\n\n  const queries = client\n    .getQueryCache()\n    .getAll()\n    .flatMap((query) =>\n      filterQuery(query) ? [dehydrateQuery(query, serializeData)] : [],\n    )\n\n  return { mutations, queries }\n}\n\nexport function hydrate(\n  client: QueryClient,\n  dehydratedState: unknown,\n  options?: HydrateOptions,\n): void {\n  if (typeof dehydratedState !== 'object' || dehydratedState === null) {\n    return\n  }\n\n  const mutationCache = client.getMutationCache()\n  const queryCache = client.getQueryCache()\n  const deserializeData =\n    options?.defaultOptions?.deserializeData ??\n    client.getDefaultOptions().hydrate?.deserializeData ??\n    defaultTransformerFn\n\n  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n  const mutations = (dehydratedState as DehydratedState).mutations || []\n  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n  const queries = (dehydratedState as DehydratedState).queries || []\n\n  mutations.forEach(({ state, ...mutationOptions }) => {\n    mutationCache.build(\n      client,\n      {\n        ...client.getDefaultOptions().hydrate?.mutations,\n        ...options?.defaultOptions?.mutations,\n        ...mutationOptions,\n      },\n      state,\n    )\n  })\n\n  queries.forEach(({ queryKey, state, queryHash, meta, promise }) => {\n    let query = queryCache.get(queryHash)\n\n    const data =\n      state.data === undefined ? state.data : deserializeData(state.data)\n\n    // Do not hydrate if an existing query exists with newer data\n    if (query) {\n      if (query.state.dataUpdatedAt < state.dataUpdatedAt) {\n        // omit fetchStatus from dehydrated state\n        // so that query stays in its current fetchStatus\n        const { fetchStatus: _ignored, ...serializedState } = state\n        query.setState({\n          ...serializedState,\n          data,\n        })\n      }\n    } else {\n      // Restore query\n      query = queryCache.build(\n        client,\n        {\n          ...client.getDefaultOptions().hydrate?.queries,\n          ...options?.defaultOptions?.queries,\n          queryKey,\n          queryHash,\n          meta,\n        },\n        // Reset fetch status to idle to avoid\n        // query being stuck in fetching state upon hydration\n        {\n          ...state,\n          data,\n          fetchStatus: 'idle',\n        },\n      )\n    }\n\n    if (promise) {\n      // Note: `Promise.resolve` required cause\n      // RSC transformed promises are not thenable\n      const initialPromise = Promise.resolve(promise).then(deserializeData)\n\n      // this doesn't actually fetch - it just creates a retryer\n      // which will re-use the passed `initialPromise`\n      void query.fetch(undefined, { initialPromise })\n    }\n  })\n}\n", "/* istanbul ignore file */\n\nimport type { DehydrateOptions, HydrateOptions } from './hydration'\nimport type { MutationState } from './mutation'\nimport type { FetchDirection, Query, QueryBehavior } from './query'\nimport type { RetryDelayValue, RetryValue } from './retryer'\nimport type { QueryFilters, QueryTypeFilter, SkipToken } from './utils'\nimport type { QueryCache } from './queryCache'\nimport type { MutationCache } from './mutationCache'\n\nexport type OmitKeyof<\n  TObject,\n  T<PERSON><PERSON> extends TStrictly extends 'safely'\n    ?\n        | keyof TObject\n        | (string & Record<never, never>)\n        | (number & Record<never, never>)\n        | (symbol & Record<never, never>)\n    : keyof TObject,\n  TStrictly extends 'strictly' | 'safely' = 'strictly',\n> = Omit<TObject, TKey>\n\nexport type Override<TTargetA, TTargetB> = {\n  [AKey in keyof TTargetA]: <PERSON><PERSON> extends keyof TTargetB\n    ? TTargetB[AKey]\n    : TTargetA[<PERSON>ey]\n}\n\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\n\nexport interface Register {\n  // defaultError: Error\n  // queryMeta: Record<string, unknown>\n  // mutationMeta: Record<string, unknown>\n}\n\nexport type DefaultError = Register extends {\n  defaultError: infer TError\n}\n  ? TError\n  : Error\n\nexport type QueryKey = ReadonlyArray<unknown>\n\nexport const dataTagSymbol = Symbol('dataTagSymbol')\nexport type dataTagSymbol = typeof dataTagSymbol\nexport const dataTagErrorSymbol = Symbol('dataTagErrorSymbol')\nexport type dataTagErrorSymbol = typeof dataTagErrorSymbol\nexport const unsetMarker = Symbol('unsetMarker')\nexport type UnsetMarker = typeof unsetMarker\nexport type AnyDataTag = {\n  [dataTagSymbol]: any\n  [dataTagErrorSymbol]: any\n}\nexport type DataTag<\n  TType,\n  TValue,\n  TError = UnsetMarker,\n> = TType extends AnyDataTag\n  ? TType\n  : TType & {\n      [dataTagSymbol]: TValue\n      [dataTagErrorSymbol]: TError\n    }\n\nexport type QueryFunction<\n  T = unknown,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = never,\n> = (context: QueryFunctionContext<TQueryKey, TPageParam>) => T | Promise<T>\n\nexport type StaleTime<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = number | ((query: Query<TQueryFnData, TError, TData, TQueryKey>) => number)\n\nexport type Enabled<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> =\n  | boolean\n  | ((query: Query<TQueryFnData, TError, TData, TQueryKey>) => boolean)\n\nexport type QueryPersister<\n  T = unknown,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = never,\n> = [TPageParam] extends [never]\n  ? (\n      queryFn: QueryFunction<T, TQueryKey, never>,\n      context: QueryFunctionContext<TQueryKey>,\n      query: Query,\n    ) => T | Promise<T>\n  : (\n      queryFn: QueryFunction<T, TQueryKey, TPageParam>,\n      context: QueryFunctionContext<TQueryKey>,\n      query: Query,\n    ) => T | Promise<T>\n\nexport type QueryFunctionContext<\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = never,\n> = [TPageParam] extends [never]\n  ? {\n      queryKey: TQueryKey\n      signal: AbortSignal\n      meta: QueryMeta | undefined\n      pageParam?: unknown\n      /**\n       * @deprecated\n       * if you want access to the direction, you can add it to the pageParam\n       */\n      direction?: unknown\n    }\n  : {\n      queryKey: TQueryKey\n      signal: AbortSignal\n      pageParam: TPageParam\n      /**\n       * @deprecated\n       * if you want access to the direction, you can add it to the pageParam\n       */\n      direction: FetchDirection\n      meta: QueryMeta | undefined\n    }\n\nexport type InitialDataFunction<T> = () => T | undefined\n\ntype NonFunctionGuard<T> = T extends Function ? never : T\n\nexport type PlaceholderDataFunction<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = (\n  previousData: TQueryData | undefined,\n  previousQuery: Query<TQueryFnData, TError, TQueryData, TQueryKey> | undefined,\n) => TQueryData | undefined\n\nexport type QueriesPlaceholderDataFunction<TQueryData> = (\n  previousData: undefined,\n  previousQuery: undefined,\n) => TQueryData | undefined\n\nexport type QueryKeyHashFunction<TQueryKey extends QueryKey> = (\n  queryKey: TQueryKey,\n) => string\n\nexport type GetPreviousPageParamFunction<TPageParam, TQueryFnData = unknown> = (\n  firstPage: TQueryFnData,\n  allPages: Array<TQueryFnData>,\n  firstPageParam: TPageParam,\n  allPageParams: Array<TPageParam>,\n) => TPageParam | undefined | null\n\nexport type GetNextPageParamFunction<TPageParam, TQueryFnData = unknown> = (\n  lastPage: TQueryFnData,\n  allPages: Array<TQueryFnData>,\n  lastPageParam: TPageParam,\n  allPageParams: Array<TPageParam>,\n) => TPageParam | undefined | null\n\nexport interface InfiniteData<TData, TPageParam = unknown> {\n  pages: Array<TData>\n  pageParams: Array<TPageParam>\n}\n\nexport type QueryMeta = Register extends {\n  queryMeta: infer TQueryMeta\n}\n  ? TQueryMeta extends Record<string, unknown>\n    ? TQueryMeta\n    : Record<string, unknown>\n  : Record<string, unknown>\n\nexport type NetworkMode = 'online' | 'always' | 'offlineFirst'\n\nexport type NotifyOnChangeProps =\n  | Array<keyof InfiniteQueryObserverResult>\n  | 'all'\n  | undefined\n  | (() => Array<keyof InfiniteQueryObserverResult> | 'all' | undefined)\n\nexport interface QueryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = never,\n> {\n  /**\n   * If `false`, failed queries will not retry by default.\n   * If `true`, failed queries will retry infinitely., failureCount: num\n   * If set to an integer number, e.g. 3, failed queries will retry until the failed query count meets that number.\n   * If set to a function `(failureCount, error) => boolean` failed queries will retry until the function returns false.\n   */\n  retry?: RetryValue<TError>\n  retryDelay?: RetryDelayValue<TError>\n  networkMode?: NetworkMode\n  /**\n   * The time in milliseconds that unused/inactive cache data remains in memory.\n   * When a query's cache becomes unused or inactive, that cache data will be garbage collected after this duration.\n   * When different garbage collection times are specified, the longest one will be used.\n   * Setting it to `Infinity` will disable garbage collection.\n   */\n  gcTime?: number\n  queryFn?: QueryFunction<TQueryFnData, TQueryKey, TPageParam> | SkipToken\n  persister?: QueryPersister<\n    NoInfer<TQueryFnData>,\n    NoInfer<TQueryKey>,\n    NoInfer<TPageParam>\n  >\n  queryHash?: string\n  queryKey?: TQueryKey\n  queryKeyHashFn?: QueryKeyHashFunction<TQueryKey>\n  initialData?: TData | InitialDataFunction<TData>\n  initialDataUpdatedAt?: number | (() => number | undefined)\n  behavior?: QueryBehavior<TQueryFnData, TError, TData, TQueryKey>\n  /**\n   * Set this to `false` to disable structural sharing between query results.\n   * Set this to a function which accepts the old and new data and returns resolved data of the same type to implement custom structural sharing logic.\n   * Defaults to `true`.\n   */\n  structuralSharing?:\n    | boolean\n    | ((oldData: unknown | undefined, newData: unknown) => unknown)\n  _defaulted?: boolean\n  /**\n   * Additional payload to be stored on each query.\n   * Use this property to pass information that can be used in other places.\n   */\n  meta?: QueryMeta\n  /**\n   * Maximum number of pages to store in the data of an infinite query.\n   */\n  maxPages?: number\n}\n\nexport interface InitialPageParam<TPageParam = unknown> {\n  initialPageParam: TPageParam\n}\n\nexport interface InfiniteQueryPageParamsOptions<\n  TQueryFnData = unknown,\n  TPageParam = unknown,\n> extends InitialPageParam<TPageParam> {\n  /**\n   * This function can be set to automatically get the previous cursor for infinite queries.\n   * The result will also be used to determine the value of `hasPreviousPage`.\n   */\n  getPreviousPageParam?: GetPreviousPageParamFunction<TPageParam, TQueryFnData>\n  /**\n   * This function can be set to automatically get the next cursor for infinite queries.\n   * The result will also be used to determine the value of `hasNextPage`.\n   */\n  getNextPageParam: GetNextPageParamFunction<TPageParam, TQueryFnData>\n}\n\nexport type ThrowOnError<\n  TQueryFnData,\n  TError,\n  TQueryData,\n  TQueryKey extends QueryKey,\n> =\n  | boolean\n  | ((\n      error: TError,\n      query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n    ) => boolean)\n\nexport interface QueryObserverOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = never,\n> extends WithRequired<\n    QueryOptions<TQueryFnData, TError, TQueryData, TQueryKey, TPageParam>,\n    'queryKey'\n  > {\n  /**\n   * Set this to `false` or a function that returns `false` to disable automatic refetching when the query mounts or changes query keys.\n   * To refetch the query, use the `refetch` method returned from the `useQuery` instance.\n   * Accepts a boolean or function that returns a boolean.\n   * Defaults to `true`.\n   */\n  enabled?: Enabled<TQueryFnData, TError, TQueryData, TQueryKey>\n  /**\n   * The time in milliseconds after data is considered stale.\n   * If set to `Infinity`, the data will never be considered stale.\n   * If set to a function, the function will be executed with the query to compute a `staleTime`.\n   */\n  staleTime?: StaleTime<TQueryFnData, TError, TQueryData, TQueryKey>\n  /**\n   * If set to a number, the query will continuously refetch at this frequency in milliseconds.\n   * If set to a function, the function will be executed with the latest data and query to compute a frequency\n   * Defaults to `false`.\n   */\n  refetchInterval?:\n    | number\n    | false\n    | ((\n        query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n      ) => number | false | undefined)\n  /**\n   * If set to `true`, the query will continue to refetch while their tab/window is in the background.\n   * Defaults to `false`.\n   */\n  refetchIntervalInBackground?: boolean\n  /**\n   * If set to `true`, the query will refetch on window focus if the data is stale.\n   * If set to `false`, the query will not refetch on window focus.\n   * If set to `'always'`, the query will always refetch on window focus.\n   * If set to a function, the function will be executed with the latest data and query to compute the value.\n   * Defaults to `true`.\n   */\n  refetchOnWindowFocus?:\n    | boolean\n    | 'always'\n    | ((\n        query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n      ) => boolean | 'always')\n  /**\n   * If set to `true`, the query will refetch on reconnect if the data is stale.\n   * If set to `false`, the query will not refetch on reconnect.\n   * If set to `'always'`, the query will always refetch on reconnect.\n   * If set to a function, the function will be executed with the latest data and query to compute the value.\n   * Defaults to the value of `networkOnline` (`true`)\n   */\n  refetchOnReconnect?:\n    | boolean\n    | 'always'\n    | ((\n        query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n      ) => boolean | 'always')\n  /**\n   * If set to `true`, the query will refetch on mount if the data is stale.\n   * If set to `false`, will disable additional instances of a query to trigger background refetch.\n   * If set to `'always'`, the query will always refetch on mount.\n   * If set to a function, the function will be executed with the latest data and query to compute the value\n   * Defaults to `true`.\n   */\n  refetchOnMount?:\n    | boolean\n    | 'always'\n    | ((\n        query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n      ) => boolean | 'always')\n  /**\n   * If set to `false`, the query will not be retried on mount if it contains an error.\n   * Defaults to `true`.\n   */\n  retryOnMount?: boolean\n  /**\n   * If set, the component will only re-render if any of the listed properties change.\n   * When set to `['data', 'error']`, the component will only re-render when the `data` or `error` properties change.\n   * When set to `'all'`, the component will re-render whenever a query is updated.\n   * When set to a function, the function will be executed to compute the list of properties.\n   * By default, access to properties will be tracked, and the component will only re-render when one of the tracked properties change.\n   */\n  notifyOnChangeProps?: NotifyOnChangeProps\n  /**\n   * Whether errors should be thrown instead of setting the `error` property.\n   * If set to `true` or `suspense` is `true`, all errors will be thrown to the error boundary.\n   * If set to `false` and `suspense` is `false`, errors are returned as state.\n   * If set to a function, it will be passed the error and the query, and it should return a boolean indicating whether to show the error in an error boundary (`true`) or return the error as state (`false`).\n   * Defaults to `false`.\n   */\n  throwOnError?: ThrowOnError<TQueryFnData, TError, TQueryData, TQueryKey>\n  /**\n   * This option can be used to transform or select a part of the data returned by the query function.\n   */\n  select?: (data: TQueryData) => TData\n  /**\n   * If set to `true`, the query will suspend when `status === 'pending'`\n   * and throw errors when `status === 'error'`.\n   * Defaults to `false`.\n   */\n  suspense?: boolean\n  /**\n   * If set, this value will be used as the placeholder data for this particular query observer while the query is still in the `loading` data and no initialData has been provided.\n   */\n  placeholderData?:\n    | NonFunctionGuard<TQueryData>\n    | PlaceholderDataFunction<\n        NonFunctionGuard<TQueryData>,\n        TError,\n        NonFunctionGuard<TQueryData>,\n        TQueryKey\n      >\n\n  _optimisticResults?: 'optimistic' | 'isRestoring'\n\n  /**\n   * Enable prefetching during rendering\n   */\n  experimental_prefetchInRender?: boolean\n}\n\nexport type WithRequired<TTarget, TKey extends keyof TTarget> = TTarget & {\n  [_ in TKey]: {}\n}\nexport type Optional<TTarget, TKey extends keyof TTarget> = Pick<\n  Partial<TTarget>,\n  TKey\n> &\n  OmitKeyof<TTarget, TKey>\n\nexport type DefaultedQueryObserverOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = WithRequired<\n  QueryObserverOptions<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  'throwOnError' | 'refetchOnReconnect' | 'queryHash'\n>\n\nexport interface InfiniteQueryObserverOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> extends QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      InfiniteData<TQueryData, TPageParam>,\n      TQueryKey,\n      TPageParam\n    >,\n    InfiniteQueryPageParamsOptions<TQueryFnData, TPageParam> {}\n\nexport type DefaultedInfiniteQueryObserverOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> = WithRequired<\n  InfiniteQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey,\n    TPageParam\n  >,\n  'throwOnError' | 'refetchOnReconnect' | 'queryHash'\n>\n\nexport interface FetchQueryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = never,\n> extends WithRequired<\n    QueryOptions<TQueryFnData, TError, TData, TQueryKey, TPageParam>,\n    'queryKey'\n  > {\n  initialPageParam?: never\n  /**\n   * The time in milliseconds after data is considered stale.\n   * If the data is fresh it will be returned from the cache.\n   */\n  staleTime?: StaleTime<TQueryFnData, TError, TData, TQueryKey>\n}\n\nexport interface EnsureQueryDataOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = never,\n> extends FetchQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  > {\n  revalidateIfStale?: boolean\n}\n\nexport type EnsureInfiniteQueryDataOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> = FetchInfiniteQueryOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey,\n  TPageParam\n> & {\n  revalidateIfStale?: boolean\n}\n\ntype FetchInfiniteQueryPages<TQueryFnData = unknown, TPageParam = unknown> =\n  | { pages?: never }\n  | {\n      pages: number\n      getNextPageParam: GetNextPageParamFunction<TPageParam, TQueryFnData>\n    }\n\nexport type FetchInfiniteQueryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> = Omit<\n  FetchQueryOptions<\n    TQueryFnData,\n    TError,\n    InfiniteData<TData, TPageParam>,\n    TQueryKey,\n    TPageParam\n  >,\n  'initialPageParam'\n> &\n  InitialPageParam<TPageParam> &\n  FetchInfiniteQueryPages<TQueryFnData, TPageParam>\n\nexport interface ResultOptions {\n  throwOnError?: boolean\n}\n\nexport interface RefetchOptions extends ResultOptions {\n  /**\n   * If set to `true`, a currently running request will be cancelled before a new request is made\n   *\n   * If set to `false`, no refetch will be made if there is already a request running.\n   *\n   * Defaults to `true`.\n   */\n  cancelRefetch?: boolean\n}\n\nexport interface InvalidateQueryFilters<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends QueryFilters<TQueryFnData, TError, TData, TQueryKey> {\n  refetchType?: QueryTypeFilter | 'none'\n}\n\nexport interface RefetchQueryFilters<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends QueryFilters<TQueryFnData, TError, TData, TQueryKey> {}\n\nexport interface InvalidateOptions extends RefetchOptions {}\nexport interface ResetOptions extends RefetchOptions {}\n\nexport interface FetchNextPageOptions extends ResultOptions {\n  /**\n   * If set to `true`, calling `fetchNextPage` repeatedly will invoke `queryFn` every time,\n   * whether the previous invocation has resolved or not. Also, the result from previous invocations will be ignored.\n   *\n   * If set to `false`, calling `fetchNextPage` repeatedly won't have any effect until the first invocation has resolved.\n   *\n   * Defaults to `true`.\n   */\n  cancelRefetch?: boolean\n}\n\nexport interface FetchPreviousPageOptions extends ResultOptions {\n  /**\n   * If set to `true`, calling `fetchPreviousPage` repeatedly will invoke `queryFn` every time,\n   * whether the previous invocation has resolved or not. Also, the result from previous invocations will be ignored.\n   *\n   * If set to `false`, calling `fetchPreviousPage` repeatedly won't have any effect until the first invocation has resolved.\n   *\n   * Defaults to `true`.\n   */\n  cancelRefetch?: boolean\n}\n\nexport type QueryStatus = 'pending' | 'error' | 'success'\nexport type FetchStatus = 'fetching' | 'paused' | 'idle'\n\nexport interface QueryObserverBaseResult<\n  TData = unknown,\n  TError = DefaultError,\n> {\n  /**\n   * The last successfully resolved data for the query.\n   */\n  data: TData | undefined\n  /**\n   * The timestamp for when the query most recently returned the `status` as `\"success\"`.\n   */\n  dataUpdatedAt: number\n  /**\n   * The error object for the query, if an error was thrown.\n   * - Defaults to `null`.\n   */\n  error: TError | null\n  /**\n   * The timestamp for when the query most recently returned the `status` as `\"error\"`.\n   */\n  errorUpdatedAt: number\n  /**\n   * The failure count for the query.\n   * - Incremented every time the query fails.\n   * - Reset to `0` when the query succeeds.\n   */\n  failureCount: number\n  /**\n   * The failure reason for the query retry.\n   * - Reset to `null` when the query succeeds.\n   */\n  failureReason: TError | null\n  /**\n   * The sum of all errors.\n   */\n  errorUpdateCount: number\n  /**\n   * A derived boolean from the `status` variable, provided for convenience.\n   * - `true` if the query attempt resulted in an error.\n   */\n  isError: boolean\n  /**\n   * Will be `true` if the query has been fetched.\n   */\n  isFetched: boolean\n  /**\n   * Will be `true` if the query has been fetched after the component mounted.\n   * - This property can be used to not show any previously cached data.\n   */\n  isFetchedAfterMount: boolean\n  /**\n   * A derived boolean from the `fetchStatus` variable, provided for convenience.\n   * - `true` whenever the `queryFn` is executing, which includes initial `pending` as well as background refetch.\n   */\n  isFetching: boolean\n  /**\n   * Is `true` whenever the first fetch for a query is in-flight.\n   * - Is the same as `isFetching && isPending`.\n   */\n  isLoading: boolean\n  /**\n   * Will be `pending` if there's no cached data and no query attempt was finished yet.\n   */\n  isPending: boolean\n  /**\n   * Will be `true` if the query failed while fetching for the first time.\n   */\n  isLoadingError: boolean\n  /**\n   * @deprecated `isInitialLoading` is being deprecated in favor of `isLoading`\n   * and will be removed in the next major version.\n   */\n  isInitialLoading: boolean\n  /**\n   * A derived boolean from the `fetchStatus` variable, provided for convenience.\n   * - The query wanted to fetch, but has been `paused`.\n   */\n  isPaused: boolean\n  /**\n   * Will be `true` if the data shown is the placeholder data.\n   */\n  isPlaceholderData: boolean\n  /**\n   * Will be `true` if the query failed while refetching.\n   */\n  isRefetchError: boolean\n  /**\n   * Is `true` whenever a background refetch is in-flight, which _does not_ include initial `pending`.\n   * - Is the same as `isFetching && !isPending`.\n   */\n  isRefetching: boolean\n  /**\n   * Will be `true` if the data in the cache is invalidated or if the data is older than the given `staleTime`.\n   */\n  isStale: boolean\n  /**\n   * A derived boolean from the `status` variable, provided for convenience.\n   * - `true` if the query has received a response with no errors and is ready to display its data.\n   */\n  isSuccess: boolean\n  /**\n   * A function to manually refetch the query.\n   */\n  refetch: (\n    options?: RefetchOptions,\n  ) => Promise<QueryObserverResult<TData, TError>>\n  /**\n   * The status of the query.\n   * - Will be:\n   *   - `pending` if there's no cached data and no query attempt was finished yet.\n   *   - `error` if the query attempt resulted in an error.\n   *   - `success` if the query has received a response with no errors and is ready to display its data.\n   */\n  status: QueryStatus\n  /**\n   * The fetch status of the query.\n   * - `fetching`: Is `true` whenever the queryFn is executing, which includes initial `pending` as well as background refetch.\n   * - `paused`: The query wanted to fetch, but has been `paused`.\n   * - `idle`: The query is not fetching.\n   * - See [Network Mode](https://tanstack.com/query/latest/docs/framework/react/guides/network-mode) for more information.\n   */\n  fetchStatus: FetchStatus\n  /**\n   * A stable promise that will be resolved with the data of the query.\n   * Requires the `experimental_prefetchInRender` feature flag to be enabled.\n   * @example\n   *\n   * ### Enabling the feature flag\n   * ```ts\n   * const client = new QueryClient({\n   *   defaultOptions: {\n   *     queries: {\n   *       experimental_prefetchInRender: true,\n   *     },\n   *   },\n   * })\n   * ```\n   *\n   * ### Usage\n   * ```tsx\n   * import { useQuery } from '@tanstack/react-query'\n   * import React from 'react'\n   * import { fetchTodos, type Todo } from './api'\n   *\n   * function TodoList({ query }: { query: UseQueryResult<Todo[], Error> }) {\n   *   const data = React.use(query.promise)\n   *\n   *   return (\n   *     <ul>\n   *       {data.map(todo => (\n   *         <li key={todo.id}>{todo.title}</li>\n   *       ))}\n   *     </ul>\n   *   )\n   * }\n   *\n   * export function App() {\n   *   const query = useQuery({ queryKey: ['todos'], queryFn: fetchTodos })\n   *\n   *   return (\n   *     <>\n   *       <h1>Todos</h1>\n   *       <React.Suspense fallback={<div>Loading...</div>}>\n   *         <TodoList query={query} />\n   *       </React.Suspense>\n   *     </>\n   *   )\n   * }\n   * ```\n   */\n  promise: Promise<TData>\n}\n\nexport interface QueryObserverPendingResult<\n  TData = unknown,\n  TError = DefaultError,\n> extends QueryObserverBaseResult<TData, TError> {\n  data: undefined\n  error: null\n  isError: false\n  isPending: true\n  isLoadingError: false\n  isRefetchError: false\n  isSuccess: false\n  status: 'pending'\n}\n\nexport interface QueryObserverLoadingResult<\n  TData = unknown,\n  TError = DefaultError,\n> extends QueryObserverBaseResult<TData, TError> {\n  data: undefined\n  error: null\n  isError: false\n  isPending: true\n  isLoading: true\n  isLoadingError: false\n  isRefetchError: false\n  isSuccess: false\n  status: 'pending'\n}\n\nexport interface QueryObserverLoadingErrorResult<\n  TData = unknown,\n  TError = DefaultError,\n> extends QueryObserverBaseResult<TData, TError> {\n  data: undefined\n  error: TError\n  isError: true\n  isPending: false\n  isLoading: false\n  isLoadingError: true\n  isRefetchError: false\n  isSuccess: false\n  status: 'error'\n}\n\nexport interface QueryObserverRefetchErrorResult<\n  TData = unknown,\n  TError = DefaultError,\n> extends QueryObserverBaseResult<TData, TError> {\n  data: TData\n  error: TError\n  isError: true\n  isPending: false\n  isLoading: false\n  isLoadingError: false\n  isRefetchError: true\n  isSuccess: false\n  status: 'error'\n}\n\nexport interface QueryObserverSuccessResult<\n  TData = unknown,\n  TError = DefaultError,\n> extends QueryObserverBaseResult<TData, TError> {\n  data: TData\n  error: null\n  isError: false\n  isPending: false\n  isLoading: false\n  isLoadingError: false\n  isRefetchError: false\n  isSuccess: true\n  status: 'success'\n}\n\nexport type DefinedQueryObserverResult<\n  TData = unknown,\n  TError = DefaultError,\n> =\n  | QueryObserverRefetchErrorResult<TData, TError>\n  | QueryObserverSuccessResult<TData, TError>\n\nexport type QueryObserverResult<TData = unknown, TError = DefaultError> =\n  | DefinedQueryObserverResult<TData, TError>\n  | QueryObserverLoadingErrorResult<TData, TError>\n  | QueryObserverLoadingResult<TData, TError>\n  | QueryObserverPendingResult<TData, TError>\n\nexport interface InfiniteQueryObserverBaseResult<\n  TData = unknown,\n  TError = DefaultError,\n> extends QueryObserverBaseResult<TData, TError> {\n  /**\n   * This function allows you to fetch the next \"page\" of results.\n   */\n  fetchNextPage: (\n    options?: FetchNextPageOptions,\n  ) => Promise<InfiniteQueryObserverResult<TData, TError>>\n  /**\n   * This function allows you to fetch the previous \"page\" of results.\n   */\n  fetchPreviousPage: (\n    options?: FetchPreviousPageOptions,\n  ) => Promise<InfiniteQueryObserverResult<TData, TError>>\n  /**\n   * Will be `true` if there is a next page to be fetched (known via the `getNextPageParam` option).\n   */\n  hasNextPage: boolean\n  /**\n   * Will be `true` if there is a previous page to be fetched (known via the `getPreviousPageParam` option).\n   */\n  hasPreviousPage: boolean\n  /**\n   * Will be `true` if the query failed while fetching the next page.\n   */\n  isFetchNextPageError: boolean\n  /**\n   * Will be `true` while fetching the next page with `fetchNextPage`.\n   */\n  isFetchingNextPage: boolean\n  /**\n   * Will be `true` if the query failed while fetching the previous page.\n   */\n  isFetchPreviousPageError: boolean\n  /**\n   * Will be `true` while fetching the previous page with `fetchPreviousPage`.\n   */\n  isFetchingPreviousPage: boolean\n}\n\nexport interface InfiniteQueryObserverPendingResult<\n  TData = unknown,\n  TError = DefaultError,\n> extends InfiniteQueryObserverBaseResult<TData, TError> {\n  data: undefined\n  error: null\n  isError: false\n  isPending: true\n  isLoadingError: false\n  isRefetchError: false\n  isFetchNextPageError: false\n  isFetchPreviousPageError: false\n  isSuccess: false\n  status: 'pending'\n}\n\nexport interface InfiniteQueryObserverLoadingResult<\n  TData = unknown,\n  TError = DefaultError,\n> extends InfiniteQueryObserverBaseResult<TData, TError> {\n  data: undefined\n  error: null\n  isError: false\n  isPending: true\n  isLoading: true\n  isLoadingError: false\n  isRefetchError: false\n  isFetchNextPageError: false\n  isFetchPreviousPageError: false\n  isSuccess: false\n  status: 'pending'\n}\n\nexport interface InfiniteQueryObserverLoadingErrorResult<\n  TData = unknown,\n  TError = DefaultError,\n> extends InfiniteQueryObserverBaseResult<TData, TError> {\n  data: undefined\n  error: TError\n  isError: true\n  isPending: false\n  isLoading: false\n  isLoadingError: true\n  isRefetchError: false\n  isFetchNextPageError: false\n  isFetchPreviousPageError: false\n  isSuccess: false\n  status: 'error'\n}\n\nexport interface InfiniteQueryObserverRefetchErrorResult<\n  TData = unknown,\n  TError = DefaultError,\n> extends InfiniteQueryObserverBaseResult<TData, TError> {\n  data: TData\n  error: TError\n  isError: true\n  isPending: false\n  isLoading: false\n  isLoadingError: false\n  isRefetchError: true\n  isSuccess: false\n  status: 'error'\n}\n\nexport interface InfiniteQueryObserverSuccessResult<\n  TData = unknown,\n  TError = DefaultError,\n> extends InfiniteQueryObserverBaseResult<TData, TError> {\n  data: TData\n  error: null\n  isError: false\n  isPending: false\n  isLoading: false\n  isLoadingError: false\n  isRefetchError: false\n  isFetchNextPageError: false\n  isFetchPreviousPageError: false\n  isSuccess: true\n  status: 'success'\n}\n\nexport type DefinedInfiniteQueryObserverResult<\n  TData = unknown,\n  TError = DefaultError,\n> =\n  | InfiniteQueryObserverRefetchErrorResult<TData, TError>\n  | InfiniteQueryObserverSuccessResult<TData, TError>\n\nexport type InfiniteQueryObserverResult<\n  TData = unknown,\n  TError = DefaultError,\n> =\n  | DefinedInfiniteQueryObserverResult<TData, TError>\n  | InfiniteQueryObserverLoadingErrorResult<TData, TError>\n  | InfiniteQueryObserverLoadingResult<TData, TError>\n  | InfiniteQueryObserverPendingResult<TData, TError>\n\nexport type MutationKey = ReadonlyArray<unknown>\n\nexport type MutationStatus = 'idle' | 'pending' | 'success' | 'error'\n\nexport type MutationScope = {\n  id: string\n}\n\nexport type MutationMeta = Register extends {\n  mutationMeta: infer TMutationMeta\n}\n  ? TMutationMeta extends Record<string, unknown>\n    ? TMutationMeta\n    : Record<string, unknown>\n  : Record<string, unknown>\n\nexport type MutationFunction<TData = unknown, TVariables = unknown> = (\n  variables: TVariables,\n) => Promise<TData>\n\nexport interface MutationOptions<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n> {\n  mutationFn?: MutationFunction<TData, TVariables>\n  mutationKey?: MutationKey\n  onMutate?: (\n    variables: TVariables,\n  ) => Promise<TContext | undefined> | TContext | undefined\n  onSuccess?: (\n    data: TData,\n    variables: TVariables,\n    context: TContext,\n  ) => Promise<unknown> | unknown\n  onError?: (\n    error: TError,\n    variables: TVariables,\n    context: TContext | undefined,\n  ) => Promise<unknown> | unknown\n  onSettled?: (\n    data: TData | undefined,\n    error: TError | null,\n    variables: TVariables,\n    context: TContext | undefined,\n  ) => Promise<unknown> | unknown\n  retry?: RetryValue<TError>\n  retryDelay?: RetryDelayValue<TError>\n  networkMode?: NetworkMode\n  gcTime?: number\n  _defaulted?: boolean\n  meta?: MutationMeta\n  scope?: MutationScope\n}\n\nexport interface MutationObserverOptions<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n> extends MutationOptions<TData, TError, TVariables, TContext> {\n  throwOnError?: boolean | ((error: TError) => boolean)\n}\n\nexport interface MutateOptions<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n> {\n  onSuccess?: (data: TData, variables: TVariables, context: TContext) => void\n  onError?: (\n    error: TError,\n    variables: TVariables,\n    context: TContext | undefined,\n  ) => void\n  onSettled?: (\n    data: TData | undefined,\n    error: TError | null,\n    variables: TVariables,\n    context: TContext | undefined,\n  ) => void\n}\n\nexport type MutateFunction<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n> = (\n  variables: TVariables,\n  options?: MutateOptions<TData, TError, TVariables, TContext>,\n) => Promise<TData>\n\nexport interface MutationObserverBaseResult<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n> extends MutationState<TData, TError, TVariables, TContext> {\n  /**\n   * The last successfully resolved data for the mutation.\n   */\n  data: TData | undefined\n  /**\n   * The variables object passed to the `mutationFn`.\n   */\n  variables: TVariables | undefined\n  /**\n   * The error object for the mutation, if an error was encountered.\n   * - Defaults to `null`.\n   */\n  error: TError | null\n  /**\n   * A boolean variable derived from `status`.\n   * - `true` if the last mutation attempt resulted in an error.\n   */\n  isError: boolean\n  /**\n   * A boolean variable derived from `status`.\n   * - `true` if the mutation is in its initial state prior to executing.\n   */\n  isIdle: boolean\n  /**\n   * A boolean variable derived from `status`.\n   * - `true` if the mutation is currently executing.\n   */\n  isPending: boolean\n  /**\n   * A boolean variable derived from `status`.\n   * - `true` if the last mutation attempt was successful.\n   */\n  isSuccess: boolean\n  /**\n   * The status of the mutation.\n   * - Will be:\n   *   - `idle` initial status prior to the mutation function executing.\n   *   - `pending` if the mutation is currently executing.\n   *   - `error` if the last mutation attempt resulted in an error.\n   *   - `success` if the last mutation attempt was successful.\n   */\n  status: MutationStatus\n  /**\n   * The mutation function you can call with variables to trigger the mutation and optionally hooks on additional callback options.\n   * @param variables - The variables object to pass to the `mutationFn`.\n   * @param options.onSuccess - This function will fire when the mutation is successful and will be passed the mutation's result.\n   * @param options.onError - This function will fire if the mutation encounters an error and will be passed the error.\n   * @param options.onSettled - This function will fire when the mutation is either successfully fetched or encounters an error and be passed either the data or error.\n   * @remarks\n   * - If you make multiple requests, `onSuccess` will fire only after the latest call you've made.\n   * - All the callback functions (`onSuccess`, `onError`, `onSettled`) are void functions, and the returned value will be ignored.\n   */\n  mutate: MutateFunction<TData, TError, TVariables, TContext>\n  /**\n   * A function to clean the mutation internal state (i.e., it resets the mutation to its initial state).\n   */\n  reset: () => void\n}\n\nexport interface MutationObserverIdleResult<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n> extends MutationObserverBaseResult<TData, TError, TVariables, TContext> {\n  data: undefined\n  variables: undefined\n  error: null\n  isError: false\n  isIdle: true\n  isPending: false\n  isSuccess: false\n  status: 'idle'\n}\n\nexport interface MutationObserverLoadingResult<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n> extends MutationObserverBaseResult<TData, TError, TVariables, TContext> {\n  data: undefined\n  variables: TVariables\n  error: null\n  isError: false\n  isIdle: false\n  isPending: true\n  isSuccess: false\n  status: 'pending'\n}\n\nexport interface MutationObserverErrorResult<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n> extends MutationObserverBaseResult<TData, TError, TVariables, TContext> {\n  data: undefined\n  error: TError\n  variables: TVariables\n  isError: true\n  isIdle: false\n  isPending: false\n  isSuccess: false\n  status: 'error'\n}\n\nexport interface MutationObserverSuccessResult<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n> extends MutationObserverBaseResult<TData, TError, TVariables, TContext> {\n  data: TData\n  error: null\n  variables: TVariables\n  isError: false\n  isIdle: false\n  isPending: false\n  isSuccess: true\n  status: 'success'\n}\n\nexport type MutationObserverResult<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n> =\n  | MutationObserverIdleResult<TData, TError, TVariables, TContext>\n  | MutationObserverLoadingResult<TData, TError, TVariables, TContext>\n  | MutationObserverErrorResult<TData, TError, TVariables, TContext>\n  | MutationObserverSuccessResult<TData, TError, TVariables, TContext>\n\nexport interface QueryClientConfig {\n  queryCache?: QueryCache\n  mutationCache?: MutationCache\n  defaultOptions?: DefaultOptions\n}\n\nexport interface DefaultOptions<TError = DefaultError> {\n  queries?: OmitKeyof<\n    QueryObserverOptions<unknown, TError>,\n    'suspense' | 'queryKey'\n  >\n  mutations?: MutationObserverOptions<unknown, TError, unknown, unknown>\n  hydrate?: HydrateOptions['defaultOptions']\n  dehydrate?: DehydrateOptions\n}\n\nexport interface CancelOptions {\n  revert?: boolean\n  silent?: boolean\n}\n\nexport interface SetDataOptions {\n  updatedAt?: number\n}\n\nexport type NotifyEventType =\n  | 'added'\n  | 'removed'\n  | 'updated'\n  | 'observerAdded'\n  | 'observerRemoved'\n  | 'observerResultsUpdated'\n  | 'observerOptionsUpdated'\n\nexport interface NotifyEvent {\n  type: NotifyEventType\n}\n", "'use client'\nimport * as React from 'react'\n\nimport {\n  QueriesObserver,\n  QueryObserver,\n  notifyManager,\n} from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport { useIsRestoring } from './isRestoring'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary,\n} from './errorBoundaryUtils'\nimport {\n  ensureSuspenseTimers,\n  fetchOptimistic,\n  shouldSuspend,\n  willFetch,\n} from './suspense'\nimport { noop } from './utils'\nimport type {\n  DefinedUseQueryResult,\n  UseQueryOptions,\n  UseQueryResult,\n} from './types'\nimport type {\n  DefaultError,\n  OmitKeyof,\n  QueriesObserverOptions,\n  QueriesPlaceholderDataFunction,\n  QueryClient,\n  QueryFunction,\n  QueryKey,\n  QueryObserverOptions,\n  ThrowOnError,\n} from '@tanstack/query-core'\n\n// This defines the `UseQueryOptions` that are accepted in `QueriesOptions` & `GetOptions`.\n// `placeholderData` function always gets undefined passed\ntype UseQueryOptionsForUseQueries<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = OmitKeyof<\n  UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  'placeholderData' | 'subscribed'\n> & {\n  placeholderData?: TQueryFnData | QueriesPlaceholderDataFunction<TQueryFnData>\n}\n\n// Avoid TS depth-limit error in case of large array literal\ntype MAXIMUM_DEPTH = 20\n\n// Widen the type of the symbol to enable type inference even if skipToken is not immutable.\ntype SkipTokenForUseQueries = symbol\n\ntype GetUseQueryOptionsForUseQueries<T> =\n  // Part 1: responsible for applying explicit type parameter to function arguments, if object { queryFnData: TQueryFnData, error: TError, data: TData }\n  T extends {\n    queryFnData: infer TQueryFnData\n    error?: infer TError\n    data: infer TData\n  }\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n      ? UseQueryOptionsForUseQueries<TQueryFnData, TError>\n      : T extends { data: infer TData; error?: infer TError }\n        ? UseQueryOptionsForUseQueries<unknown, TError, TData>\n        : // Part 2: responsible for applying explicit type parameter to function arguments, if tuple [TQueryFnData, TError, TData]\n          T extends [infer TQueryFnData, infer TError, infer TData]\n          ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData>\n          : T extends [infer TQueryFnData, infer TError]\n            ? UseQueryOptionsForUseQueries<TQueryFnData, TError>\n            : T extends [infer TQueryFnData]\n              ? UseQueryOptionsForUseQueries<TQueryFnData>\n              : // Part 3: responsible for inferring and enforcing type if no explicit parameter was provided\n                T extends {\n                    queryFn?:\n                      | QueryFunction<infer TQueryFnData, infer TQueryKey>\n                      | SkipTokenForUseQueries\n                    select?: (data: any) => infer TData\n                    throwOnError?: ThrowOnError<any, infer TError, any, any>\n                  }\n                ? UseQueryOptionsForUseQueries<\n                    TQueryFnData,\n                    unknown extends TError ? DefaultError : TError,\n                    unknown extends TData ? TQueryFnData : TData,\n                    TQueryKey\n                  >\n                : // Fallback\n                  UseQueryOptionsForUseQueries\n\n// A defined initialData setting should return a DefinedUseQueryResult rather than UseQueryResult\ntype GetDefinedOrUndefinedQueryResult<T, TData, TError = unknown> = T extends {\n  initialData?: infer TInitialData\n}\n  ? unknown extends TInitialData\n    ? UseQueryResult<TData, TError>\n    : TInitialData extends TData\n      ? DefinedUseQueryResult<TData, TError>\n      : TInitialData extends () => infer TInitialDataResult\n        ? unknown extends TInitialDataResult\n          ? UseQueryResult<TData, TError>\n          : TInitialDataResult extends TData\n            ? DefinedUseQueryResult<TData, TError>\n            : UseQueryResult<TData, TError>\n        : UseQueryResult<TData, TError>\n  : UseQueryResult<TData, TError>\n\ntype GetUseQueryResult<T> =\n  // Part 1: responsible for mapping explicit type parameter to function result, if object\n  T extends { queryFnData: any; error?: infer TError; data: infer TData }\n    ? GetDefinedOrUndefinedQueryResult<T, TData, TError>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n      ? GetDefinedOrUndefinedQueryResult<T, TQueryFnData, TError>\n      : T extends { data: infer TData; error?: infer TError }\n        ? GetDefinedOrUndefinedQueryResult<T, TData, TError>\n        : // Part 2: responsible for mapping explicit type parameter to function result, if tuple\n          T extends [any, infer TError, infer TData]\n          ? GetDefinedOrUndefinedQueryResult<T, TData, TError>\n          : T extends [infer TQueryFnData, infer TError]\n            ? GetDefinedOrUndefinedQueryResult<T, TQueryFnData, TError>\n            : T extends [infer TQueryFnData]\n              ? GetDefinedOrUndefinedQueryResult<T, TQueryFnData>\n              : // Part 3: responsible for mapping inferred type to results, if no explicit parameter was provided\n                T extends {\n                    queryFn?:\n                      | QueryFunction<infer TQueryFnData, any>\n                      | SkipTokenForUseQueries\n                    select?: (data: any) => infer TData\n                    throwOnError?: ThrowOnError<any, infer TError, any, any>\n                  }\n                ? GetDefinedOrUndefinedQueryResult<\n                    T,\n                    unknown extends TData ? TQueryFnData : TData,\n                    unknown extends TError ? DefaultError : TError\n                  >\n                : // Fallback\n                  UseQueryResult\n\n/**\n * QueriesOptions reducer recursively unwraps function arguments to infer/enforce type param\n */\nexport type QueriesOptions<\n  T extends Array<any>,\n  TResults extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseQueryOptionsForUseQueries>\n  : T extends []\n    ? []\n    : T extends [infer Head]\n      ? [...TResults, GetUseQueryOptionsForUseQueries<Head>]\n      : T extends [infer Head, ...infer Tails]\n        ? QueriesOptions<\n            [...Tails],\n            [...TResults, GetUseQueryOptionsForUseQueries<Head>],\n            [...TDepth, 1]\n          >\n        : ReadonlyArray<unknown> extends T\n          ? T\n          : // If T is *some* array but we couldn't assign unknown[] to it, then it must hold some known/homogenous type!\n            // use this to infer the param types in the case of Array.map() argument\n            T extends Array<\n                UseQueryOptionsForUseQueries<\n                  infer TQueryFnData,\n                  infer TError,\n                  infer TData,\n                  infer TQueryKey\n                >\n              >\n            ? Array<\n                UseQueryOptionsForUseQueries<\n                  TQueryFnData,\n                  TError,\n                  TData,\n                  TQueryKey\n                >\n              >\n            : // Fallback\n              Array<UseQueryOptionsForUseQueries>\n\n/**\n * QueriesResults reducer recursively maps type param to results\n */\nexport type QueriesResults<\n  T extends Array<any>,\n  TResults extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseQueryResult>\n  : T extends []\n    ? []\n    : T extends [infer Head]\n      ? [...TResults, GetUseQueryResult<Head>]\n      : T extends [infer Head, ...infer Tails]\n        ? QueriesResults<\n            [...Tails],\n            [...TResults, GetUseQueryResult<Head>],\n            [...TDepth, 1]\n          >\n        : T extends Array<\n              UseQueryOptionsForUseQueries<\n                infer TQueryFnData,\n                infer TError,\n                infer TData,\n                any\n              >\n            >\n          ? // Dynamic-size (homogenous) UseQueryOptions array: map directly to array of results\n            Array<\n              UseQueryResult<\n                unknown extends TData ? TQueryFnData : TData,\n                unknown extends TError ? DefaultError : TError\n              >\n            >\n          : // Fallback\n            Array<UseQueryResult>\n\nexport function useQueries<\n  T extends Array<any>,\n  TCombinedResult = QueriesResults<T>,\n>(\n  {\n    queries,\n    ...options\n  }: {\n    queries: readonly [...QueriesOptions<T>]\n    combine?: (result: QueriesResults<T>) => TCombinedResult\n    subscribed?: boolean\n  },\n  queryClient?: QueryClient,\n): TCombinedResult {\n  const client = useQueryClient(queryClient)\n  const isRestoring = useIsRestoring()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n\n  const defaultedQueries = React.useMemo(\n    () =>\n      queries.map((opts) => {\n        const defaultedOptions = client.defaultQueryOptions(\n          opts as QueryObserverOptions,\n        )\n\n        // Make sure the results are already in fetching state before subscribing or updating options\n        defaultedOptions._optimisticResults = isRestoring\n          ? 'isRestoring'\n          : 'optimistic'\n\n        return defaultedOptions\n      }),\n    [queries, client, isRestoring],\n  )\n\n  defaultedQueries.forEach((query) => {\n    ensureSuspenseTimers(query)\n    ensurePreventErrorBoundaryRetry(query, errorResetBoundary)\n  })\n\n  useClearResetErrorBoundary(errorResetBoundary)\n\n  const [observer] = React.useState(\n    () =>\n      new QueriesObserver<TCombinedResult>(\n        client,\n        defaultedQueries,\n        options as QueriesObserverOptions<TCombinedResult>,\n      ),\n  )\n\n  // note: this must be called before useSyncExternalStore\n  const [optimisticResult, getCombinedResult, trackResult] =\n    observer.getOptimisticResult(\n      defaultedQueries,\n      (options as QueriesObserverOptions<TCombinedResult>).combine,\n    )\n\n  const shouldSubscribe = !isRestoring && options.subscribed !== false\n  React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        shouldSubscribe\n          ? observer.subscribe(notifyManager.batchCalls(onStoreChange))\n          : noop,\n      [observer, shouldSubscribe],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  React.useEffect(() => {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setQueries(\n      defaultedQueries,\n      options as QueriesObserverOptions<TCombinedResult>,\n      {\n        listeners: false,\n      },\n    )\n  }, [defaultedQueries, options, observer])\n\n  const shouldAtLeastOneSuspend = optimisticResult.some((result, index) =>\n    shouldSuspend(defaultedQueries[index], result),\n  )\n\n  const suspensePromises = shouldAtLeastOneSuspend\n    ? optimisticResult.flatMap((result, index) => {\n        const opts = defaultedQueries[index]\n\n        if (opts) {\n          const queryObserver = new QueryObserver(client, opts)\n          if (shouldSuspend(opts, result)) {\n            return fetchOptimistic(opts, queryObserver, errorResetBoundary)\n          } else if (willFetch(result, isRestoring)) {\n            void fetchOptimistic(opts, queryObserver, errorResetBoundary)\n          }\n        }\n        return []\n      })\n    : []\n\n  if (suspensePromises.length > 0) {\n    throw Promise.all(suspensePromises)\n  }\n  const firstSingleResultWhichShouldThrow = optimisticResult.find(\n    (result, index) => {\n      const query = defaultedQueries[index]\n      return (\n        query &&\n        getHasError({\n          result,\n          errorResetBoundary,\n          throwOnError: query.throwOnError,\n          query: client.getQueryCache().get(query.queryHash),\n        })\n      )\n    },\n  )\n\n  if (firstSingleResultWhichShouldThrow?.error) {\n    throw firstSingleResultWhichShouldThrow.error\n  }\n\n  return getCombinedResult(trackResult())\n}\n", "'use client'\nimport * as React from 'react'\n\nimport type { QueryClient } from '@tanstack/query-core'\n\nexport const QueryClientContext = React.createContext<QueryClient | undefined>(\n  undefined,\n)\n\nexport const useQueryClient = (queryClient?: QueryClient) => {\n  const client = React.useContext(QueryClientContext)\n\n  if (queryClient) {\n    return queryClient\n  }\n\n  if (!client) {\n    throw new Error('No QueryClient set, use QueryClientProvider to set one')\n  }\n\n  return client\n}\n\nexport type QueryClientProviderProps = {\n  client: QueryClient\n  children?: React.ReactNode\n}\n\nexport const QueryClientProvider = ({\n  client,\n  children,\n}: QueryClientProviderProps): React.JSX.Element => {\n  React.useEffect(() => {\n    client.mount()\n    return () => {\n      client.unmount()\n    }\n  }, [client])\n\n  return (\n    <QueryClientContext.Provider value={client}>\n      {children}\n    </QueryClientContext.Provider>\n  )\n}\n", "'use client'\nimport * as React from 'react'\n\nconst IsRestoringContext = React.createContext(false)\n\nexport const useIsRestoring = () => React.useContext(IsRestoringContext)\nexport const IsRestoringProvider = IsRestoringContext.Provider\n", "'use client'\nimport * as React from 'react'\n\n// CONTEXT\nexport type QueryErrorResetFunction = () => void\nexport type QueryErrorIsResetFunction = () => boolean\nexport type QueryErrorClearResetFunction = () => void\n\nexport interface QueryErrorResetBoundaryValue {\n  clearReset: QueryErrorClearResetFunction\n  isReset: QueryErrorIsResetFunction\n  reset: QueryErrorResetFunction\n}\n\nfunction createValue(): QueryErrorResetBoundaryValue {\n  let isReset = false\n  return {\n    clearReset: () => {\n      isReset = false\n    },\n    reset: () => {\n      isReset = true\n    },\n    isReset: () => {\n      return isReset\n    },\n  }\n}\n\nconst QueryErrorResetBoundaryContext = React.createContext(createValue())\n\n// HOOK\n\nexport const useQueryErrorResetBoundary = () =>\n  React.useContext(QueryErrorResetBoundaryContext)\n\n// COMPONENT\n\nexport type QueryErrorResetBoundaryFunction = (\n  value: QueryErrorResetBoundaryValue,\n) => React.ReactNode\n\nexport interface QueryErrorResetBoundaryProps {\n  children: QueryErrorResetBoundaryFunction | React.ReactNode\n}\n\nexport const QueryErrorResetBoundary = ({\n  children,\n}: QueryErrorResetBoundaryProps) => {\n  const [value] = React.useState(() => createValue())\n  return (\n    <QueryErrorResetBoundaryContext.Provider value={value}>\n      {typeof children === 'function' ? children(value) : children}\n    </QueryErrorResetBoundaryContext.Provider>\n  )\n}\n", "'use client'\nimport * as React from 'react'\nimport { shouldThrowError } from './utils'\nimport type {\n  DefaultedQueryObserverOptions,\n  Query,\n  QueryKey,\n  QueryObserverResult,\n  ThrowOnError,\n} from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\n\nexport const ensurePreventErrorBoundaryRetry = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  options: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  if (\n    options.suspense ||\n    options.throwOnError ||\n    options.experimental_prefetchInRender\n  ) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      options.retryOnMount = false\n    }\n  }\n}\n\nexport const useClearResetErrorBoundary = (\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  React.useEffect(() => {\n    errorResetBoundary.clearReset()\n  }, [errorResetBoundary])\n}\n\nexport const getHasError = <\n  TData,\n  TError,\n  TQueryFnData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>({\n  result,\n  errorResetBoundary,\n  throwOnError,\n  query,\n}: {\n  result: QueryObserverResult<TData, TError>\n  errorResetBoundary: QueryErrorResetBoundaryValue\n  throwOnError: ThrowOnError<TQueryFnData, TError, TQueryData, TQueryKey>\n  query: Query<TQueryFnData, TError, TQueryData, TQueryKey> | undefined\n}) => {\n  return (\n    result.isError &&\n    !errorResetBoundary.isReset() &&\n    !result.isFetching &&\n    query &&\n    shouldThrowError(throwOnError, [result.error, query])\n  )\n}\n", "export function shouldThrowError<T extends (...args: Array<any>) => boolean>(\n  throwError: boolean | T | undefined,\n  params: Parameters<T>,\n): boolean {\n  // Allow throwError function to override throwing behavior on a per-error basis\n  if (typeof throwError === 'function') {\n    return throwError(...params)\n  }\n\n  return !!throwError\n}\n\nexport function noop(): void {}\n", "import type {\n  DefaultError,\n  DefaultedQueryObserverOptions,\n  Query,\n  QueryKey,\n  QueryObserver,\n  QueryObserverResult,\n} from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\n\nexport const defaultThrowOnError = <\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  _error: TError,\n  query: Query<TQueryFnData, TError, TData, TQueryKey>,\n) => query.state.data === undefined\n\nexport const ensureSuspenseTimers = (\n  defaultedOptions: DefaultedQueryObserverOptions<any, any, any, any, any>,\n) => {\n  const originalStaleTime = defaultedOptions.staleTime\n\n  if (defaultedOptions.suspense) {\n    // Handle staleTime to ensure minimum 1000ms in Suspense mode\n    // This prevents unnecessary refetching when components remount after suspending\n    defaultedOptions.staleTime =\n      typeof originalStaleTime === 'function'\n        ? (...args) => Math.max(originalStaleTime(...args), 1000)\n        : Math.max(originalStaleTime ?? 1000, 1000)\n\n    if (typeof defaultedOptions.gcTime === 'number') {\n      defaultedOptions.gcTime = Math.max(defaultedOptions.gcTime, 1000)\n    }\n  }\n}\n\nexport const willFetch = (\n  result: QueryObserverResult<any, any>,\n  isRestoring: boolean,\n) => result.isLoading && result.isFetching && !isRestoring\n\nexport const shouldSuspend = (\n  defaultedOptions:\n    | DefaultedQueryObserverOptions<any, any, any, any, any>\n    | undefined,\n  result: QueryObserverResult<any, any>,\n) => defaultedOptions?.suspense && result.isPending\n\nexport const fetchOptimistic = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  defaultedOptions: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) =>\n  observer.fetchOptimistic(defaultedOptions).catch(() => {\n    errorResetBoundary.clearReset()\n  })\n", "'use client'\nimport * as React from 'react'\n\nimport { isServer, notifyManager } from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary,\n} from './errorBoundaryUtils'\nimport { useIsRestoring } from './isRestoring'\nimport {\n  ensureSuspenseTimers,\n  fetchOptimistic,\n  shouldSuspend,\n  willFetch,\n} from './suspense'\nimport { noop } from './utils'\nimport type {\n  QueryClient,\n  QueryKey,\n  QueryObserver,\n  QueryObserverResult,\n} from '@tanstack/query-core'\nimport type { UseBaseQueryOptions } from './types'\n\nexport function useBaseQuery<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  options: UseBaseQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  Observer: typeof QueryObserver,\n  queryClient?: QueryClient,\n): QueryObserverResult<TData, TError> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof options !== 'object' || Array.isArray(options)) {\n      throw new Error(\n        'Bad argument type. Starting with v5, only the \"Object\" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object',\n      )\n    }\n  }\n\n  const client = useQueryClient(queryClient)\n  const isRestoring = useIsRestoring()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n  const defaultedOptions = client.defaultQueryOptions(options)\n\n  ;(client.getDefaultOptions().queries as any)?._experimental_beforeQuery?.(\n    defaultedOptions,\n  )\n\n  // Make sure results are optimistically set in fetching state before subscribing or updating options\n  defaultedOptions._optimisticResults = isRestoring\n    ? 'isRestoring'\n    : 'optimistic'\n\n  ensureSuspenseTimers(defaultedOptions)\n  ensurePreventErrorBoundaryRetry(defaultedOptions, errorResetBoundary)\n\n  useClearResetErrorBoundary(errorResetBoundary)\n\n  // this needs to be invoked before creating the Observer because that can create a cache entry\n  const isNewCacheEntry = !client\n    .getQueryCache()\n    .get(defaultedOptions.queryHash)\n\n  const [observer] = React.useState(\n    () =>\n      new Observer<TQueryFnData, TError, TData, TQueryData, TQueryKey>(\n        client,\n        defaultedOptions,\n      ),\n  )\n\n  // note: this must be called before useSyncExternalStore\n  const result = observer.getOptimisticResult(defaultedOptions)\n\n  const shouldSubscribe = !isRestoring && options.subscribed !== false\n  React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => {\n        const unsubscribe = shouldSubscribe\n          ? observer.subscribe(notifyManager.batchCalls(onStoreChange))\n          : noop\n\n        // Update result to make sure we did not miss any query updates\n        // between creating the observer and subscribing to it.\n        observer.updateResult()\n\n        return unsubscribe\n      },\n      [observer, shouldSubscribe],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  React.useEffect(() => {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setOptions(defaultedOptions, { listeners: false })\n  }, [defaultedOptions, observer])\n\n  // Handle suspense\n  if (shouldSuspend(defaultedOptions, result)) {\n    throw fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n  }\n\n  // Handle error boundary\n  if (\n    getHasError({\n      result,\n      errorResetBoundary,\n      throwOnError: defaultedOptions.throwOnError,\n      query: client\n        .getQueryCache()\n        .get<\n          TQueryFnData,\n          TError,\n          TQueryData,\n          TQueryKey\n        >(defaultedOptions.queryHash),\n    })\n  ) {\n    throw result.error\n  }\n\n  ;(client.getDefaultOptions().queries as any)?._experimental_afterQuery?.(\n    defaultedOptions,\n    result,\n  )\n\n  if (\n    defaultedOptions.experimental_prefetchInRender &&\n    !isServer &&\n    willFetch(result, isRestoring)\n  ) {\n    const promise = isNewCacheEntry\n      ? // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted\n        fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n      : // subscribe to the \"cache promise\" so that we can finalize the currentThenable once data comes in\n        client.getQueryCache().get(defaultedOptions.queryHash)?.promise\n\n    promise?.catch(noop).finally(() => {\n      // `.updateResult()` will trigger `.#currentThenable` to finalize\n      observer.updateResult()\n    })\n  }\n\n  // Handle result property usage tracking\n  return !defaultedOptions.notifyOnChangeProps\n    ? observer.trackResult(result)\n    : result\n}\n", "'use client'\nimport { QueryObserver } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type { DefaultError, QueryClient, QueryKey } from '@tanstack/query-core'\nimport type {\n  DefinedUseQueryResult,\n  UseQueryOptions,\n  UseQueryResult,\n} from './types'\nimport type {\n  DefinedInitialDataOptions,\n  UndefinedInitialDataOptions,\n} from './queryOptions'\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): DefinedUseQueryResult<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQ<PERSON>yKey extends QueryKey = QueryKey,\n>(\n  options: UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): UseQueryResult<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): UseQueryResult<TData, TError>\n\nexport function useQuery(options: UseQueryOptions, queryClient?: QueryClient) {\n  return useBaseQuery(options, QueryObserver, queryClient)\n}\n", "'use client'\nimport { QueryObserver, skipToken } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport { defaultThrowOnError } from './suspense'\nimport type { UseSuspenseQueryOptions, UseSuspenseQueryResult } from './types'\nimport type { DefaultError, QueryClient, QueryKey } from '@tanstack/query-core'\n\nexport function useSuspenseQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseSuspenseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): UseSuspenseQueryResult<TData, TError> {\n  if (process.env.NODE_ENV !== 'production') {\n    if ((options.queryFn as any) === skipToken) {\n      console.error('skipToken is not allowed for useSuspenseQuery')\n    }\n  }\n\n  return useBaseQuery(\n    {\n      ...options,\n      enabled: true,\n      suspense: true,\n      throwOnError: defaultThrowOnError,\n      placeholderData: undefined,\n    },\n    QueryObserver,\n    queryClient,\n  ) as UseSuspenseQueryResult<TData, TError>\n}\n", "'use client'\nimport { InfiniteQueryObserver, skipToken } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport { defaultThrowOnError } from './suspense'\nimport type {\n  DefaultError,\n  InfiniteData,\n  InfiniteQueryObserverSuccessResult,\n  QueryClient,\n  QueryKey,\n  QueryObserver,\n} from '@tanstack/query-core'\nimport type {\n  UseSuspenseInfiniteQueryOptions,\n  UseSuspenseInfiniteQueryResult,\n} from './types'\n\nexport function useSuspenseInfiniteQuery<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UseSuspenseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryFnData,\n    TQ<PERSON>y<PERSON><PERSON>,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n): UseSuspenseInfiniteQueryResult<TData, TError> {\n  if (process.env.NODE_ENV !== 'production') {\n    if ((options.queryFn as any) === skipToken) {\n      console.error('skipToken is not allowed for useSuspenseInfiniteQuery')\n    }\n  }\n\n  return useBaseQuery(\n    {\n      ...options,\n      enabled: true,\n      suspense: true,\n      throwOnError: defaultThrowOnError,\n    },\n    InfiniteQueryObserver as typeof QueryObserver,\n    queryClient,\n  ) as InfiniteQueryObserverSuccessResult<TData, TError>\n}\n", "'use client'\nimport { skipToken } from '@tanstack/query-core'\nimport { useQueries } from './useQueries'\nimport { defaultThrowOnError } from './suspense'\nimport type { UseSuspenseQueryOptions, UseSuspenseQueryResult } from './types'\nimport type {\n  DefaultError,\n  QueryClient,\n  QueryFunction,\n  ThrowOnError,\n} from '@tanstack/query-core'\n\n// Avoid TS depth-limit error in case of large array literal\ntype MAXIMUM_DEPTH = 20\n\n// Widen the type of the symbol to enable type inference even if skipToken is not immutable.\ntype SkipTokenForUseQueries = symbol\n\ntype GetUseSuspenseQueryOptions<T> =\n  // Part 1: responsible for applying explicit type parameter to function arguments, if object { queryFnData: TQueryFnData, error: TError, data: TData }\n  T extends {\n    queryFnData: infer TQueryFnData\n    error?: infer TError\n    data: infer TData\n  }\n    ? UseSuspenseQueryOptions<TQueryFnData, TError, TData>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n      ? UseSuspenseQueryOptions<TQueryFnData, TError>\n      : T extends { data: infer TData; error?: infer TError }\n        ? UseSuspenseQueryOptions<unknown, TError, TData>\n        : // Part 2: responsible for applying explicit type parameter to function arguments, if tuple [TQueryFnData, TError, TData]\n          T extends [infer TQueryFnData, infer TError, infer TData]\n          ? UseSuspenseQueryOptions<TQueryFnData, TError, TData>\n          : T extends [infer TQueryFnData, infer TError]\n            ? UseSuspenseQueryOptions<TQueryFnData, TError>\n            : T extends [infer TQueryFnData]\n              ? UseSuspenseQueryOptions<TQueryFnData>\n              : // Part 3: responsible for inferring and enforcing type if no explicit parameter was provided\n                T extends {\n                    queryFn?:\n                      | QueryFunction<infer TQueryFnData, infer TQueryKey>\n                      | SkipTokenForUseQueries\n                    select?: (data: any) => infer TData\n                    throwOnError?: ThrowOnError<any, infer TError, any, any>\n                  }\n                ? UseSuspenseQueryOptions<\n                    TQueryFnData,\n                    TError,\n                    TData,\n                    TQueryKey\n                  >\n                : T extends {\n                      queryFn?:\n                        | QueryFunction<infer TQueryFnData, infer TQueryKey>\n                        | SkipTokenForUseQueries\n                      throwOnError?: ThrowOnError<any, infer TError, any, any>\n                    }\n                  ? UseSuspenseQueryOptions<\n                      TQueryFnData,\n                      TError,\n                      TQueryFnData,\n                      TQueryKey\n                    >\n                  : // Fallback\n                    UseSuspenseQueryOptions\n\ntype GetUseSuspenseQueryResult<T> =\n  // Part 1: responsible for mapping explicit type parameter to function result, if object\n  T extends { queryFnData: any; error?: infer TError; data: infer TData }\n    ? UseSuspenseQueryResult<TData, TError>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n      ? UseSuspenseQueryResult<TQueryFnData, TError>\n      : T extends { data: infer TData; error?: infer TError }\n        ? UseSuspenseQueryResult<TData, TError>\n        : // Part 2: responsible for mapping explicit type parameter to function result, if tuple\n          T extends [any, infer TError, infer TData]\n          ? UseSuspenseQueryResult<TData, TError>\n          : T extends [infer TQueryFnData, infer TError]\n            ? UseSuspenseQueryResult<TQueryFnData, TError>\n            : T extends [infer TQueryFnData]\n              ? UseSuspenseQueryResult<TQueryFnData>\n              : // Part 3: responsible for mapping inferred type to results, if no explicit parameter was provided\n                T extends {\n                    queryFn?:\n                      | QueryFunction<infer TQueryFnData, any>\n                      | SkipTokenForUseQueries\n                    select?: (data: any) => infer TData\n                    throwOnError?: ThrowOnError<any, infer TError, any, any>\n                  }\n                ? UseSuspenseQueryResult<\n                    unknown extends TData ? TQueryFnData : TData,\n                    unknown extends TError ? DefaultError : TError\n                  >\n                : T extends {\n                      queryFn?:\n                        | QueryFunction<infer TQueryFnData, any>\n                        | SkipTokenForUseQueries\n                      throwOnError?: ThrowOnError<any, infer TError, any, any>\n                    }\n                  ? UseSuspenseQueryResult<\n                      TQueryFnData,\n                      unknown extends TError ? DefaultError : TError\n                    >\n                  : // Fallback\n                    UseSuspenseQueryResult\n\n/**\n * SuspenseQueriesOptions reducer recursively unwraps function arguments to infer/enforce type param\n */\nexport type SuspenseQueriesOptions<\n  T extends Array<any>,\n  TResults extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseSuspenseQueryOptions>\n  : T extends []\n    ? []\n    : T extends [infer Head]\n      ? [...TResults, GetUseSuspenseQueryOptions<Head>]\n      : T extends [infer Head, ...infer Tails]\n        ? SuspenseQueriesOptions<\n            [...Tails],\n            [...TResults, GetUseSuspenseQueryOptions<Head>],\n            [...TDepth, 1]\n          >\n        : Array<unknown> extends T\n          ? T\n          : // If T is *some* array but we couldn't assign unknown[] to it, then it must hold some known/homogenous type!\n            // use this to infer the param types in the case of Array.map() argument\n            T extends Array<\n                UseSuspenseQueryOptions<\n                  infer TQueryFnData,\n                  infer TError,\n                  infer TData,\n                  infer TQueryKey\n                >\n              >\n            ? Array<\n                UseSuspenseQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n              >\n            : // Fallback\n              Array<UseSuspenseQueryOptions>\n\n/**\n * SuspenseQueriesResults reducer recursively maps type param to results\n */\nexport type SuspenseQueriesResults<\n  T extends Array<any>,\n  TResults extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseSuspenseQueryResult>\n  : T extends []\n    ? []\n    : T extends [infer Head]\n      ? [...TResults, GetUseSuspenseQueryResult<Head>]\n      : T extends [infer Head, ...infer Tails]\n        ? SuspenseQueriesResults<\n            [...Tails],\n            [...TResults, GetUseSuspenseQueryResult<Head>],\n            [...TDepth, 1]\n          >\n        : T extends Array<\n              UseSuspenseQueryOptions<\n                infer TQueryFnData,\n                infer TError,\n                infer TData,\n                any\n              >\n            >\n          ? // Dynamic-size (homogenous) UseQueryOptions array: map directly to array of results\n            Array<\n              UseSuspenseQueryResult<\n                unknown extends TData ? TQueryFnData : TData,\n                unknown extends TError ? DefaultError : TError\n              >\n            >\n          : // Fallback\n            Array<UseSuspenseQueryResult>\n\nexport function useSuspenseQueries<\n  T extends Array<any>,\n  TCombinedResult = SuspenseQueriesResults<T>,\n>(\n  options: {\n    queries: readonly [...SuspenseQueriesOptions<T>]\n    combine?: (result: SuspenseQueriesResults<T>) => TCombinedResult\n  },\n  queryClient?: QueryClient,\n): TCombinedResult {\n  return useQueries(\n    {\n      ...options,\n      queries: options.queries.map((query) => {\n        if (process.env.NODE_ENV !== 'production') {\n          if (query.queryFn === skipToken) {\n            console.error('skipToken is not allowed for useSuspenseQueries')\n          }\n        }\n\n        return {\n          ...query,\n          suspense: true,\n          throwOnError: defaultThrowOnError,\n          enabled: true,\n          placeholderData: undefined,\n        }\n      }),\n    } as any,\n    queryClient,\n  )\n}\n", "import { useQueryClient } from './QueryClientProvider'\nimport type {\n  DefaultError,\n  FetchQueryOptions,\n  QueryClient,\n  QueryKey,\n} from '@tanstack/query-core'\n\nexport function usePrefetchQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n) {\n  const client = useQueryClient(queryClient)\n\n  if (!client.getQueryState(options.queryKey)) {\n    client.prefetchQuery(options)\n  }\n}\n", "import { useQueryClient } from './QueryClientProvider'\nimport type {\n  DefaultError,\n  FetchInfiniteQueryOptions,\n  QueryClient,\n  QueryKey,\n} from '@tanstack/query-core'\n\nexport function usePrefetchInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: FetchInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n) {\n  const client = useQueryClient(queryClient)\n\n  if (!client.getQueryState(options.queryKey)) {\n    client.prefetchInfiniteQuery(options)\n  }\n}\n", "import type {\n  DataTag,\n  DefaultError,\n  InitialDataFunction,\n  Omit<PERSON>eyof,\n  Query<PERSON>ey,\n  SkipToken,\n} from '@tanstack/query-core'\nimport type { UseQueryOptions } from './types'\n\nexport type UndefinedInitialDataOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = UseQueryOptions<TQueryFnData, TError, TData, TQueryKey> & {\n  initialData?:\n    | undefined\n    | InitialDataFunction<NonUndefinedGuard<TQueryFnData>>\n    | NonUndefinedGuard<TQueryFnData>\n}\n\nexport type UnusedSkipTokenOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQuery<PERSON><PERSON> extends QueryKey = QueryKey,\n> = OmitKeyof<\n  UseQueryOptions<TQueryFnData, TError, TD<PERSON>, TQ<PERSON>y<PERSON>ey>,\n  'queryFn'\n> & {\n  queryFn?: Exclude<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>['queryFn'],\n    SkipToken | undefined\n  >\n}\n\ntype NonUndefinedGuard<T> = T extends undefined ? never : T\n\nexport type DefinedInitialDataOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = UseQueryOptions<TQueryFnData, TError, TData, TQueryKey> & {\n  initialData:\n    | NonUndefinedGuard<TQueryFnData>\n    | (() => NonUndefinedGuard<TQueryFnData>)\n}\n\nexport function queryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n): DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey> & {\n  queryKey: DataTag<TQueryKey, TQueryFnData, TError>\n}\n\nexport function queryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UnusedSkipTokenOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UnusedSkipTokenOptions<TQueryFnData, TError, TData, TQueryKey> & {\n  queryKey: DataTag<TQueryKey, TQueryFnData, TError>\n}\n\nexport function queryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey> & {\n  queryKey: DataTag<TQueryKey, TQueryFnData, TError>\n}\n\nexport function queryOptions(options: unknown) {\n  return options\n}\n", "import type {\n  DataTag,\n  DefaultError,\n  InfiniteData,\n  InitialDataFunction,\n  OmitKeyof,\n  QueryKey,\n  SkipToken,\n} from '@tanstack/query-core'\nimport type { UseInfiniteQueryOptions } from './types'\n\nexport type UndefinedInitialDataInfiniteOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> = UseInfiniteQueryOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryFnData,\n  TQueryKey,\n  TPageParam\n> & {\n  initialData?:\n    | undefined\n    | NonUndefinedGuard<InfiniteData<TQueryFnData, TPageParam>>\n    | InitialDataFunction<\n        NonUndefinedGuard<InfiniteData<TQueryFnData, TPageParam>>\n      >\n}\n\nexport type UnusedSkipTokenInfiniteOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  T<PERSON><PERSON>y<PERSON>ey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> = OmitKeyof<\n  UseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryFnData,\n    TQueryKey,\n    TPageParam\n  >,\n  'queryFn'\n> & {\n  queryFn?: Exclude<\n    UseInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryFnData,\n      TQueryKey,\n      TPageParam\n    >['queryFn'],\n    SkipToken | undefined\n  >\n}\n\ntype NonUndefinedGuard<T> = T extends undefined ? never : T\n\nexport type DefinedInitialDataInfiniteOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> = UseInfiniteQueryOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryFnData,\n  TQueryKey,\n  TPageParam\n> & {\n  initialData:\n    | NonUndefinedGuard<InfiniteData<TQueryFnData, TPageParam>>\n    | (() => NonUndefinedGuard<InfiniteData<TQueryFnData, TPageParam>>)\n    | undefined\n}\n\nexport function infiniteQueryOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: DefinedInitialDataInfiniteOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n): DefinedInitialDataInfiniteOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey,\n  TPageParam\n> & {\n  queryKey: DataTag<TQueryKey, InfiniteData<TQueryFnData>, TError>\n}\n\nexport function infiniteQueryOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UnusedSkipTokenInfiniteOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n): UnusedSkipTokenInfiniteOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey,\n  TPageParam\n> & {\n  queryKey: DataTag<TQueryKey, InfiniteData<TQueryFnData>, TError>\n}\n\nexport function infiniteQueryOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UndefinedInitialDataInfiniteOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n): UndefinedInitialDataInfiniteOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey,\n  TPageParam\n> & {\n  queryKey: DataTag<TQueryKey, InfiniteData<TQueryFnData>, TError>\n}\n\nexport function infiniteQueryOptions(options: unknown) {\n  return options\n}\n", "/* eslint-disable react-compiler/react-compiler */\n\n'use client'\nimport * as React from 'react'\n\nimport { hydrate } from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport type {\n  DehydratedState,\n  HydrateOptions,\n  OmitKeyof,\n  QueryClient,\n} from '@tanstack/query-core'\n\nexport interface HydrationBoundaryProps {\n  state?: unknown\n  options?: OmitKeyof<HydrateOptions, 'defaultOptions'> & {\n    defaultOptions?: OmitKeyof<\n      Exclude<HydrateOptions['defaultOptions'], undefined>,\n      'mutations'\n    >\n  }\n  children?: React.ReactNode\n  queryClient?: QueryClient\n}\n\nexport const HydrationBoundary = ({\n  children,\n  options = {},\n  state,\n  queryClient,\n}: HydrationBoundaryProps) => {\n  const client = useQueryClient(queryClient)\n  const [hydrationQueue, setHydrationQueue] = React.useState<\n    DehydratedState['queries'] | undefined\n  >()\n\n  const optionsRef = React.useRef(options)\n  optionsRef.current = options\n\n  // This useMemo is for performance reasons only, everything inside it _must_\n  // be safe to run in every render and code here should be read as \"in render\".\n  //\n  // This code needs to happen during the render phase, because after initial\n  // SSR, hydration needs to happen _before_ children render. Also, if hydrating\n  // during a transition, we want to hydrate as much as is safe in render so\n  // we can prerender as much as possible.\n  //\n  // For any queries that already exist in the cache, we want to hold back on\n  // hydrating until _after_ the render phase. The reason for this is that during\n  // transitions, we don't want the existing queries and observers to update to\n  // the new data on the current page, only _after_ the transition is committed.\n  // If the transition is aborted, we will have hydrated any _new_ queries, but\n  // we throw away the fresh data for any existing ones to avoid unexpectedly\n  // updating the UI.\n  React.useMemo(() => {\n    if (state) {\n      if (typeof state !== 'object') {\n        return\n      }\n\n      const queryCache = client.getQueryCache()\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      const queries = (state as DehydratedState).queries || []\n\n      const newQueries: DehydratedState['queries'] = []\n      const existingQueries: DehydratedState['queries'] = []\n      for (const dehydratedQuery of queries) {\n        const existingQuery = queryCache.get(dehydratedQuery.queryHash)\n\n        if (!existingQuery) {\n          newQueries.push(dehydratedQuery)\n        } else {\n          const hydrationIsNewer =\n            dehydratedQuery.state.dataUpdatedAt >\n            existingQuery.state.dataUpdatedAt\n          const queryAlreadyQueued = hydrationQueue?.find(\n            (query) => query.queryHash === dehydratedQuery.queryHash,\n          )\n\n          if (\n            hydrationIsNewer &&\n            (!queryAlreadyQueued ||\n              dehydratedQuery.state.dataUpdatedAt >\n                queryAlreadyQueued.state.dataUpdatedAt)\n          ) {\n            existingQueries.push(dehydratedQuery)\n          }\n        }\n      }\n\n      if (newQueries.length > 0) {\n        // It's actually fine to call this with queries/state that already exists\n        // in the cache, or is older. hydrate() is idempotent for queries.\n        hydrate(client, { queries: newQueries }, optionsRef.current)\n      }\n      if (existingQueries.length > 0) {\n        setHydrationQueue((prev) =>\n          prev ? [...prev, ...existingQueries] : existingQueries,\n        )\n      }\n    }\n  }, [client, hydrationQueue, state])\n\n  React.useEffect(() => {\n    if (hydrationQueue) {\n      hydrate(client, { queries: hydrationQueue }, optionsRef.current)\n      setHydrationQueue(undefined)\n    }\n  }, [client, hydrationQueue])\n\n  return children as React.ReactElement\n}\n", "'use client'\nimport * as React from 'react'\nimport { notifyManager } from '@tanstack/query-core'\n\nimport { useQueryClient } from './QueryClientProvider'\nimport type { QueryClient, QueryFilters } from '@tanstack/query-core'\n\nexport function useIsFetching(\n  filters?: QueryFilters,\n  queryClient?: QueryClient,\n): number {\n  const client = useQueryClient(queryClient)\n  const queryCache = client.getQueryCache()\n\n  return React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        queryCache.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [queryCache],\n    ),\n    () => client.isFetching(filters),\n    () => client.isFetching(filters),\n  )\n}\n", "/* eslint-disable react-compiler/react-compiler */\n\n'use client'\nimport * as React from 'react'\n\nimport { notify<PERSON><PERSON><PERSON>, replaceEqual<PERSON>eep } from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport type {\n  Mutation,\n  MutationCache,\n  MutationFilters,\n  MutationState,\n  QueryClient,\n} from '@tanstack/query-core'\n\nexport function useIsMutating(\n  filters?: MutationFilters,\n  queryClient?: QueryClient,\n): number {\n  const client = useQueryClient(queryClient)\n  return useMutationState(\n    { filters: { ...filters, status: 'pending' } },\n    client,\n  ).length\n}\n\ntype MutationStateOptions<TResult = MutationState> = {\n  filters?: MutationFilters\n  select?: (mutation: Mutation) => TResult\n}\n\nfunction getResult<TResult = MutationState>(\n  mutationCache: MutationCache,\n  options: MutationStateOptions<TResult>,\n): Array<TResult> {\n  return mutationCache\n    .findAll(options.filters)\n    .map(\n      (mutation): TResult =>\n        (options.select ? options.select(mutation) : mutation.state) as TResult,\n    )\n}\n\nexport function useMutationState<TResult = MutationState>(\n  options: MutationStateOptions<TResult> = {},\n  queryClient?: QueryClient,\n): Array<TResult> {\n  const mutationCache = useQueryClient(queryClient).getMutationCache()\n  const optionsRef = React.useRef(options)\n  const result = React.useRef<Array<TResult>>(null)\n  if (!result.current) {\n    result.current = getResult(mutationCache, options)\n  }\n\n  React.useEffect(() => {\n    optionsRef.current = options\n  })\n\n  return React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        mutationCache.subscribe(() => {\n          const nextResult = replaceEqualDeep(\n            result.current,\n            getResult(mutationCache, optionsRef.current),\n          )\n          if (result.current !== nextResult) {\n            result.current = nextResult\n            notifyManager.schedule(onStoreChange)\n          }\n        }),\n      [mutationCache],\n    ),\n    () => result.current,\n    () => result.current,\n  )!\n}\n", "'use client'\nimport * as React from 'react'\nimport { MutationObserver, notifyManager } from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport { noop, shouldThrowError } from './utils'\nimport type {\n  UseMutateFunction,\n  UseMutationOptions,\n  UseMutationResult,\n} from './types'\nimport type { DefaultError, QueryClient } from '@tanstack/query-core'\n\n// HOOK\n\nexport function useMutation<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n>(\n  options: UseMutationOptions<TData, TError, TVariables, TContext>,\n  queryClient?: QueryClient,\n): UseMutationResult<TData, TError, TVariables, TContext> {\n  const client = useQueryClient(queryClient)\n\n  const [observer] = React.useState(\n    () =>\n      new MutationObserver<TData, TError, TVariables, TContext>(\n        client,\n        options,\n      ),\n  )\n\n  React.useEffect(() => {\n    observer.setOptions(options)\n  }, [observer, options])\n\n  const result = React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  const mutate = React.useCallback<\n    UseMutateFunction<TData, TError, TVariables, TContext>\n  >(\n    (variables, mutateOptions) => {\n      observer.mutate(variables, mutateOptions).catch(noop)\n    },\n    [observer],\n  )\n\n  if (\n    result.error &&\n    shouldThrowError(observer.options.throwOnError, [result.error])\n  ) {\n    throw result.error\n  }\n\n  return { ...result, mutate, mutateAsync: result.mutate }\n}\n", "'use client'\nimport { InfiniteQueryObserver } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type {\n  DefaultError,\n  InfiniteData,\n  QueryClient,\n  QueryKey,\n  QueryObserver,\n} from '@tanstack/query-core'\nimport type {\n  DefinedUseInfiniteQueryResult,\n  UseInfiniteQueryOptions,\n  UseInfiniteQueryResult,\n} from './types'\nimport type {\n  DefinedInitialDataInfiniteOptions,\n  UndefinedInitialDataInfiniteOptions,\n} from './infiniteQueryOptions'\n\nexport function useInfiniteQuery<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: DefinedInitialDataInfiniteOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n): DefinedUseInfiniteQueryResult<TData, TError>\n\nexport function useInfiniteQuery<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UndefinedInitialDataInfiniteOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n): UseInfiniteQueryResult<TData, TError>\n\nexport function useInfiniteQuery<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryFnData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n): UseInfiniteQueryResult<TData, TError>\n\nexport function useInfiniteQuery(\n  options: UseInfiniteQueryOptions,\n  queryClient?: QueryClient,\n) {\n  return useBaseQuery(\n    options,\n    InfiniteQueryObserver as typeof QueryObserver,\n    queryClient,\n  )\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAAO,IAAM,eAAN,MAA+C;EAGpD,cAAc;AAFd,SAAU,YAAY,oBAAI,IAAe;AAGvC,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;EAC3C;EAEA,UAAU,UAAiC;AACzC,SAAK,UAAU,IAAI,QAAQ;AAE3B,SAAK,YAAY;AAEjB,WAAO,MAAM;AACX,WAAK,UAAU,OAAO,QAAQ;AAC9B,WAAK,cAAc;IACrB;EACF;EAEA,eAAwB;AACtB,WAAO,KAAK,UAAU,OAAO;EAC/B;EAEU,cAAoB;EAE9B;EAEU,gBAAsB;EAEhC;AACF;;;ACmDO,IAAM,WAAW,OAAO,WAAW,eAAe,UAAU;AAI5D,SAAS,OAAO;AAAC;AAEjB,SAAS,iBACd,SACA,OACS;AACT,SAAO,OAAO,YAAY,aACrB,QAAmC,KAAK,IACzC;AACN;AAEO,SAAS,eAAe,OAAiC;AAC9D,SAAO,OAAO,UAAU,YAAY,SAAS,KAAK,UAAU;AAC9D;AAEO,SAAS,eAAe,WAAmB,WAA4B;AAC5E,SAAO,KAAK,IAAI,aAAa,aAAa,KAAK,KAAK,IAAI,GAAG,CAAC;AAC9D;AAEO,SAAS,iBAMd,WACA,OACoB;AACpB,SAAO,OAAO,cAAc,aAAa,UAAU,KAAK,IAAI;AAC9D;AAEO,SAAS,eAMd,SACA,OACqB;AACrB,SAAO,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AAC1D;AAEO,SAAS,WACd,SACA,OACS;AACT,QAAM;IACJ,OAAO;IACP;IACA;IACA;IACA;IACA;EACF,IAAI;AAEJ,MAAI,UAAU;AACZ,QAAI,OAAO;AACT,UAAI,MAAM,cAAc,sBAAsB,UAAU,MAAM,OAAO,GAAG;AACtE,eAAO;MACT;IACF,WAAW,CAAC,gBAAgB,MAAM,UAAU,QAAQ,GAAG;AACrD,aAAO;IACT;EACF;AAEA,MAAI,SAAS,OAAO;AAClB,UAAM,WAAW,MAAM,SAAS;AAChC,QAAI,SAAS,YAAY,CAAC,UAAU;AAClC,aAAO;IACT;AACA,QAAI,SAAS,cAAc,UAAU;AACnC,aAAO;IACT;EACF;AAEA,MAAI,OAAO,UAAU,aAAa,MAAM,QAAQ,MAAM,OAAO;AAC3D,WAAO;EACT;AAEA,MAAI,eAAe,gBAAgB,MAAM,MAAM,aAAa;AAC1D,WAAO;EACT;AAEA,MAAI,aAAa,CAAC,UAAU,KAAK,GAAG;AAClC,WAAO;EACT;AAEA,SAAO;AACT;AAEO,SAAS,cACd,SACA,UACS;AACT,QAAM,EAAE,OAAO,QAAQ,WAAW,YAAY,IAAI;AAClD,MAAI,aAAa;AACf,QAAI,CAAC,SAAS,QAAQ,aAAa;AACjC,aAAO;IACT;AACA,QAAI,OAAO;AACT,UAAI,QAAQ,SAAS,QAAQ,WAAW,MAAM,QAAQ,WAAW,GAAG;AAClE,eAAO;MACT;IACF,WAAW,CAAC,gBAAgB,SAAS,QAAQ,aAAa,WAAW,GAAG;AACtE,aAAO;IACT;EACF;AAEA,MAAI,UAAU,SAAS,MAAM,WAAW,QAAQ;AAC9C,WAAO;EACT;AAEA,MAAI,aAAa,CAAC,UAAU,QAAQ,GAAG;AACrC,WAAO;EACT;AAEA,SAAO;AACT;AAEO,SAAS,sBACd,UACA,SACQ;AACR,QAAM,UAAS,mCAAS,mBAAkB;AAC1C,SAAO,OAAO,QAAQ;AACxB;AAMO,SAAS,QAAQ,UAA0C;AAChE,SAAO,KAAK;IAAU;IAAU,CAAC,GAAG,QAClC,cAAc,GAAG,IACb,OAAO,KAAK,GAAG,EACZ,KAAK,EACL,OAAO,CAAC,QAAQ,QAAQ;AACvB,aAAO,GAAG,IAAI,IAAI,GAAG;AACrB,aAAO;IACT,GAAG,CAAC,CAAQ,IACd;EACN;AACF;AAMO,SAAS,gBAAgB,GAAQ,GAAiB;AACvD,MAAI,MAAM,GAAG;AACX,WAAO;EACT;AAEA,MAAI,OAAO,MAAM,OAAO,GAAG;AACzB,WAAO;EACT;AAEA,MAAI,KAAK,KAAK,OAAO,MAAM,YAAY,OAAO,MAAM,UAAU;AAC5D,WAAO,CAAC,OAAO,KAAK,CAAC,EAAE,KAAK,CAAC,QAAQ,CAAC,gBAAgB,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;EACvE;AAEA,SAAO;AACT;AAQO,SAAS,iBAAiB,GAAQ,GAAa;AACpD,MAAI,MAAM,GAAG;AACX,WAAO;EACT;AAEA,QAAM,QAAQ,aAAa,CAAC,KAAK,aAAa,CAAC;AAE/C,MAAI,SAAU,cAAc,CAAC,KAAK,cAAc,CAAC,GAAI;AACnD,UAAM,SAAS,QAAQ,IAAI,OAAO,KAAK,CAAC;AACxC,UAAM,QAAQ,OAAO;AACrB,UAAM,SAAS,QAAQ,IAAI,OAAO,KAAK,CAAC;AACxC,UAAM,QAAQ,OAAO;AACrB,UAAM,OAAY,QAAQ,CAAC,IAAI,CAAC;AAEhC,QAAI,aAAa;AAEjB,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,YAAM,MAAM,QAAQ,IAAI,OAAO,CAAC;AAChC,WACI,CAAC,SAAS,OAAO,SAAS,GAAG,KAAM,UACrC,EAAE,GAAG,MAAM,UACX,EAAE,GAAG,MAAM,QACX;AACA,aAAK,GAAG,IAAI;AACZ;MACF,OAAO;AACL,aAAK,GAAG,IAAI,iBAAiB,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAC3C,YAAI,KAAK,GAAG,MAAM,EAAE,GAAG,KAAK,EAAE,GAAG,MAAM,QAAW;AAChD;QACF;MACF;IACF;AAEA,WAAO,UAAU,SAAS,eAAe,QAAQ,IAAI;EACvD;AAEA,SAAO;AACT;AAKO,SAAS,oBACd,GACA,GACS;AACT,MAAI,CAAC,KAAK,OAAO,KAAK,CAAC,EAAE,WAAW,OAAO,KAAK,CAAC,EAAE,QAAQ;AACzD,WAAO;EACT;AAEA,aAAW,OAAO,GAAG;AACnB,QAAI,EAAE,GAAG,MAAM,EAAE,GAAG,GAAG;AACrB,aAAO;IACT;EACF;AAEA,SAAO;AACT;AAEO,SAAS,aAAa,OAAgB;AAC3C,SAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,WAAW,OAAO,KAAK,KAAK,EAAE;AACrE;AAIO,SAAS,cAAc,GAAqB;AACjD,MAAI,CAAC,mBAAmB,CAAC,GAAG;AAC1B,WAAO;EACT;AAGA,QAAM,OAAO,EAAE;AACf,MAAI,SAAS,QAAW;AACtB,WAAO;EACT;AAGA,QAAM,OAAO,KAAK;AAClB,MAAI,CAAC,mBAAmB,IAAI,GAAG;AAC7B,WAAO;EACT;AAGA,MAAI,CAAC,KAAK,eAAe,eAAe,GAAG;AACzC,WAAO;EACT;AAGA,MAAI,OAAO,eAAe,CAAC,MAAM,OAAO,WAAW;AACjD,WAAO;EACT;AAGA,SAAO;AACT;AAEA,SAAS,mBAAmB,GAAiB;AAC3C,SAAO,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM;AAC/C;AAEO,SAAS,MAAM,SAAgC;AACpD,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,SAAS,OAAO;EAC7B,CAAC;AACH;AAEO,SAAS,YAGd,UAA6B,MAAa,SAA0B;AACpE,MAAI,OAAO,QAAQ,sBAAsB,YAAY;AACnD,WAAO,QAAQ,kBAAkB,UAAU,IAAI;EACjD,WAAW,QAAQ,sBAAsB,OAAO;AAC9C,QAAI,MAAuC;AACzC,UAAI;AACF,eAAO,iBAAiB,UAAU,IAAI;MACxC,SAAS,OAAO;AACd,gBAAQ;UACN,0JAA0J,QAAQ,SAAS,MAAM,KAAK;QACxL;MACF;IACF;AAEA,WAAO,iBAAiB,UAAU,IAAI;EACxC;AACA,SAAO;AACT;AAEO,SAAS,iBACd,cACe;AACf,SAAO;AACT;AAEO,SAAS,SAAY,OAAiB,MAAS,MAAM,GAAa;AACvE,QAAM,WAAW,CAAC,GAAG,OAAO,IAAI;AAChC,SAAO,OAAO,SAAS,SAAS,MAAM,SAAS,MAAM,CAAC,IAAI;AAC5D;AAEO,SAAS,WAAc,OAAiB,MAAS,MAAM,GAAa;AACzE,QAAM,WAAW,CAAC,MAAM,GAAG,KAAK;AAChC,SAAO,OAAO,SAAS,SAAS,MAAM,SAAS,MAAM,GAAG,EAAE,IAAI;AAChE;AAEO,IAAM,YAAY,OAAO;AAGzB,SAAS,cAId,SAIA,cACwC;AACxC,MAAI,MAAuC;AACzC,QAAI,QAAQ,YAAY,WAAW;AACjC,cAAQ;QACN,yGAAyG,QAAQ,SAAS;MAC5H;IACF;EACF;AAKA,MAAI,CAAC,QAAQ,YAAW,6CAAc,iBAAgB;AACpD,WAAO,MAAM,aAAa;EAC5B;AAEA,MAAI,CAAC,QAAQ,WAAW,QAAQ,YAAY,WAAW;AACrD,WAAO,MACL,QAAQ,OAAO,IAAI,MAAM,qBAAqB,QAAQ,SAAS,GAAG,CAAC;EACvE;AAEA,SAAO,QAAQ;AACjB;;;;ACxaO,IAAM,gBAAN,mBAA2B,aAAuB;EAMvD,cAAc;AACZ,UAAM;AANR;AACA;AAEA;AAIE,uBAAK,QAAS,CAAC,YAAY;AAGzB,UAAI,CAAC,YAAY,OAAO,kBAAkB;AACxC,cAAM,WAAW,MAAM,QAAQ;AAE/B,eAAO,iBAAiB,oBAAoB,UAAU,KAAK;AAE3D,eAAO,MAAM;AAEX,iBAAO,oBAAoB,oBAAoB,QAAQ;QACzD;MACF;AACA;IACF;EACF;EAEU,cAAoB;AAC5B,QAAI,CAAC,mBAAK,WAAU;AAClB,WAAK,iBAAiB,mBAAK,OAAM;IACnC;EACF;EAEU,gBAAgB;;AACxB,QAAI,CAAC,KAAK,aAAa,GAAG;AACxB,OAAAA,OAAA,mBAAK,cAAL,gBAAAA,KAAA;AACA,yBAAK,UAAW;IAClB;EACF;EAEA,iBAAiB,OAAsB;;AACrC,uBAAK,QAAS;AACd,KAAAA,OAAA,mBAAK,cAAL,gBAAAA,KAAA;AACA,uBAAK,UAAW,MAAM,CAAC,YAAY;AACjC,UAAI,OAAO,YAAY,WAAW;AAChC,aAAK,WAAW,OAAO;MACzB,OAAO;AACL,aAAK,QAAQ;MACf;IACF,CAAC;EACH;EAEA,WAAW,SAAyB;AAClC,UAAM,UAAU,mBAAK,cAAa;AAClC,QAAI,SAAS;AACX,yBAAK,UAAW;AAChB,WAAK,QAAQ;IACf;EACF;EAEA,UAAgB;AACd,UAAM,YAAY,KAAK,UAAU;AACjC,SAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,eAAS,SAAS;IACpB,CAAC;EACH;EAEA,YAAqB;;AACnB,QAAI,OAAO,mBAAK,cAAa,WAAW;AACtC,aAAO,mBAAK;IACd;AAIA,aAAOA,OAAA,WAAW,aAAX,gBAAAA,KAAqB,qBAAoB;EAClD;AACF,GAzEE,0BACA,0BAEA,wBAJK;AA4EA,IAAM,eAAe,IAAI,aAAa;;;;AC/EtC,IAAM,iBAANC,MAAA,cAA4B,aAAuB;EAMxD,cAAc;AACZ,UAAM;AANR,gCAAU;AACV,uBAAAC;AAEA,uBAAAC;AAIE,uBAAKA,SAAS,CAAC,aAAa;AAG1B,UAAI,CAAC,YAAY,OAAO,kBAAkB;AACxC,cAAM,iBAAiB,MAAM,SAAS,IAAI;AAC1C,cAAM,kBAAkB,MAAM,SAAS,KAAK;AAE5C,eAAO,iBAAiB,UAAU,gBAAgB,KAAK;AACvD,eAAO,iBAAiB,WAAW,iBAAiB,KAAK;AAEzD,eAAO,MAAM;AAEX,iBAAO,oBAAoB,UAAU,cAAc;AACnD,iBAAO,oBAAoB,WAAW,eAAe;QACvD;MACF;AAEA;IACF;EACF;EAEU,cAAoB;AAC5B,QAAI,CAAC,mBAAKD,YAAU;AAClB,WAAK,iBAAiB,mBAAKC,QAAM;IACnC;EACF;EAEU,gBAAgB;;AACxB,QAAI,CAAC,KAAK,aAAa,GAAG;AACxB,OAAAF,OAAA,mBAAKC,eAAL,gBAAAD,KAAA;AACA,yBAAKC,WAAW;IAClB;EACF;EAEA,iBAAiB,OAAsB;;AACrC,uBAAKC,SAAS;AACd,KAAAF,OAAA,mBAAKC,eAAL,gBAAAD,KAAA;AACA,uBAAKC,WAAW,MAAM,KAAK,UAAU,KAAK,IAAI,CAAC;EACjD;EAEA,UAAU,QAAuB;AAC/B,UAAM,UAAU,mBAAK,aAAY;AAEjC,QAAI,SAAS;AACX,yBAAK,SAAU;AACf,WAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,iBAAS,MAAM;MACjB,CAAC;IACH;EACF;EAEA,WAAoB;AAClB,WAAO,mBAAK;EACd;AACF,GA7DE,yBACAA,YAAA,eAEAC,UAAA,eAJKF;AAgEA,IAAM,gBAAgB,IAAI,cAAc;;;AC7BxC,SAAS,kBAAyC;AACvD,MAAI;AACJ,MAAI;AAEJ,QAAM,WAAW,IAAI,QAAQ,CAAC,UAAU,YAAY;AAClD,cAAU;AACV,aAAS;EACX,CAAC;AAED,WAAS,SAAS;AAClB,WAAS,MAAM,MAAM;EAErB,CAAC;AAED,WAAS,SAAS,MAA+B;AAC/C,WAAO,OAAO,UAAU,IAAI;AAG5B,WAAQ,SAAyC;AACjD,WAAQ,SAAyC;EACnD;AAEA,WAAS,UAAU,CAAC,UAAU;AAC5B,aAAS;MACP,QAAQ;MACR;IACF,CAAC;AAED,YAAQ,KAAK;EACf;AACA,WAAS,SAAS,CAAC,WAAW;AAC5B,aAAS;MACP,QAAQ;MACR;IACF,CAAC;AAED,WAAO,MAAM;EACf;AAEA,SAAO;AACT;;;AClCA,SAAS,kBAAkB,cAAsB;AAC/C,SAAO,KAAK,IAAI,MAAO,KAAK,cAAc,GAAK;AACjD;AAEO,SAAS,SAAS,aAA+C;AACtE,UAAQ,eAAe,cAAc,WACjC,cAAc,SAAS,IACvB;AACN;AAEO,IAAM,iBAAN,cAA6B,MAAM;EAGxC,YAAY,SAAyB;AACnC,UAAM,gBAAgB;AACtB,SAAK,SAAS,mCAAS;AACvB,SAAK,SAAS,mCAAS;EACzB;AACF;AAEO,SAAS,iBAAiB,OAAqC;AACpE,SAAO,iBAAiB;AAC1B;AAEO,SAAS,cACd,QACgB;AAChB,MAAI,mBAAmB;AACvB,MAAI,eAAe;AACnB,MAAI,aAAa;AACjB,MAAI;AAEJ,QAAM,WAAW,gBAAuB;AAExC,QAAM,SAAS,CAAC,kBAAwC;;AACtD,QAAI,CAAC,YAAY;AACf,aAAO,IAAI,eAAe,aAAa,CAAC;AAExC,OAAAG,OAAA,OAAO,UAAP,gBAAAA,KAAA;IACF;EACF;AACA,QAAM,cAAc,MAAM;AACxB,uBAAmB;EACrB;AAEA,QAAM,gBAAgB,MAAM;AAC1B,uBAAmB;EACrB;AAEA,QAAM,cAAc,MAClB,aAAa,UAAU,MACtB,OAAO,gBAAgB,YAAY,cAAc,SAAS,MAC3D,OAAO,OAAO;AAEhB,QAAM,WAAW,MAAM,SAAS,OAAO,WAAW,KAAK,OAAO,OAAO;AAErE,QAAM,UAAU,CAAC,UAAe;;AAC9B,QAAI,CAAC,YAAY;AACf,mBAAa;AACb,OAAAA,OAAA,OAAO,cAAP,gBAAAA,KAAA,aAAmB;AACnB;AACA,eAAS,QAAQ,KAAK;IACxB;EACF;AAEA,QAAM,SAAS,CAAC,UAAe;;AAC7B,QAAI,CAAC,YAAY;AACf,mBAAa;AACb,OAAAA,OAAA,OAAO,YAAP,gBAAAA,KAAA,aAAiB;AACjB;AACA,eAAS,OAAO,KAAK;IACvB;EACF;AAEA,QAAM,QAAQ,MAAM;AAClB,WAAO,IAAI,QAAQ,CAAC,oBAAoB;;AACtC,mBAAa,CAAC,UAAU;AACtB,YAAI,cAAc,YAAY,GAAG;AAC/B,0BAAgB,KAAK;QACvB;MACF;AACA,OAAAA,OAAA,OAAO,YAAP,gBAAAA,KAAA;IACF,CAAC,EAAE,KAAK,MAAM;;AACZ,mBAAa;AACb,UAAI,CAAC,YAAY;AACf,SAAAA,OAAA,OAAO,eAAP,gBAAAA,KAAA;MACF;IACF,CAAC;EACH;AAGA,QAAM,MAAM,MAAM;AAEhB,QAAI,YAAY;AACd;IACF;AAEA,QAAI;AAGJ,UAAM,iBACJ,iBAAiB,IAAI,OAAO,iBAAiB;AAG/C,QAAI;AACF,uBAAiB,kBAAkB,OAAO,GAAG;IAC/C,SAAS,OAAO;AACd,uBAAiB,QAAQ,OAAO,KAAK;IACvC;AAEA,YAAQ,QAAQ,cAAc,EAC3B,KAAK,OAAO,EACZ,MAAM,CAAC,UAAU;;AAEhB,UAAI,YAAY;AACd;MACF;AAGA,YAAM,QAAQ,OAAO,UAAU,WAAW,IAAI;AAC9C,YAAM,aAAa,OAAO,cAAc;AACxC,YAAM,QACJ,OAAO,eAAe,aAClB,WAAW,cAAc,KAAK,IAC9B;AACN,YAAM,cACJ,UAAU,QACT,OAAO,UAAU,YAAY,eAAe,SAC5C,OAAO,UAAU,cAAc,MAAM,cAAc,KAAK;AAE3D,UAAI,oBAAoB,CAAC,aAAa;AAEpC,eAAO,KAAK;AACZ;MACF;AAEA;AAGA,OAAAA,OAAA,OAAO,WAAP,gBAAAA,KAAA,aAAgB,cAAc;AAG9B,YAAM,KAAK,EAER,KAAK,MAAM;AACV,eAAO,YAAY,IAAI,SAAY,MAAM;MAC3C,CAAC,EACA,KAAK,MAAM;AACV,YAAI,kBAAkB;AACpB,iBAAO,KAAK;QACd,OAAO;AACL,cAAI;QACN;MACF,CAAC;IACL,CAAC;EACL;AAEA,SAAO;IACL,SAAS;IACT;IACA,UAAU,MAAM;AACd;AACA,aAAO;IACT;IACA;IACA;IACA;IACA,OAAO,MAAM;AAEX,UAAI,SAAS,GAAG;AACd,YAAI;MACN,OAAO;AACL,cAAM,EAAE,KAAK,GAAG;MAClB;AACA,aAAO;IACT;EACF;AACF;;;ACpNO,SAAS,sBAAsB;AACpC,MAAI,QAA+B,CAAC;AACpC,MAAI,eAAe;AACnB,MAAI,WAA2B,CAAC,aAAa;AAC3C,aAAS;EACX;AACA,MAAI,gBAAqC,CAAC,aAAyB;AACjE,aAAS;EACX;AACA,MAAI,aAA+B,CAAC,OAAO,WAAW,IAAI,CAAC;AAE3D,QAAM,WAAW,CAAC,aAAmC;AACnD,QAAI,cAAc;AAChB,YAAM,KAAK,QAAQ;IACrB,OAAO;AACL,iBAAW,MAAM;AACf,iBAAS,QAAQ;MACnB,CAAC;IACH;EACF;AACA,QAAM,QAAQ,MAAY;AACxB,UAAM,gBAAgB;AACtB,YAAQ,CAAC;AACT,QAAI,cAAc,QAAQ;AACxB,iBAAW,MAAM;AACf,sBAAc,MAAM;AAClB,wBAAc,QAAQ,CAAC,aAAa;AAClC,qBAAS,QAAQ;UACnB,CAAC;QACH,CAAC;MACH,CAAC;IACH;EACF;AAEA,SAAO;IACL,OAAO,CAAI,aAAyB;AAClC,UAAI;AACJ;AACA,UAAI;AACF,iBAAS,SAAS;MACpB,UAAA;AACE;AACA,YAAI,CAAC,cAAc;AACjB,gBAAM;QACR;MACF;AACA,aAAO;IACT;;;;IAIA,YAAY,CACV,aAC0B;AAC1B,aAAO,IAAI,SAAS;AAClB,iBAAS,MAAM;AACb,mBAAS,GAAG,IAAI;QAClB,CAAC;MACH;IACF;IACA;;;;;IAKA,mBAAmB,CAAC,OAAuB;AACzC,iBAAW;IACb;;;;;IAKA,wBAAwB,CAAC,OAA4B;AACnD,sBAAgB;IAClB;IACA,cAAc,CAAC,OAAyB;AACtC,mBAAa;IACf;EACF;AACF;AAGO,IAAM,gBAAgB,oBAAoB;;;;AC5F1C,IAAe,aAAfC,MAAA,MAAyB;EAAzB;AAEL;;EAEA,UAAgB;AACd,SAAK,eAAe;EACtB;EAEU,aAAmB;AAC3B,SAAK,eAAe;AAEpB,QAAI,eAAe,KAAK,MAAM,GAAG;AAC/B,yBAAK,YAAa,WAAW,MAAM;AACjC,aAAK,eAAe;MACtB,GAAG,KAAK,MAAM;IAChB;EACF;EAEU,aAAa,WAAqC;AAE1D,SAAK,SAAS,KAAK;MACjB,KAAK,UAAU;MACf,cAAc,WAAW,WAAW,IAAI,KAAK;IAC/C;EACF;EAEU,iBAAiB;AACzB,QAAI,mBAAK,aAAY;AACnB,mBAAa,mBAAK,WAAU;AAC5B,yBAAK,YAAa;IACpB;EACF;AAGF,GAhCE,4BAFKA;;;;ACyJA,IAAM,SAANC,MAAA,cAKG,UAAU;EAclB,YAAY,QAA6D;AACvE,UAAM;AApBH;AAWL;AACA;AACA;AACA;AAEA;AACA;AAKE,uBAAK,sBAAuB;AAC5B,uBAAK,iBAAkB,OAAO;AAC9B,SAAK,WAAW,OAAO,OAAO;AAC9B,SAAK,YAAY,CAAC;AAClB,uBAAK,QAAS,OAAO;AACrB,SAAK,WAAW,OAAO;AACvB,SAAK,YAAY,OAAO;AACxB,uBAAK,eAAgB,gBAAgB,KAAK,OAAO;AACjD,SAAK,QAAQ,OAAO,SAAS,mBAAK;AAClC,SAAK,WAAW;EAClB;EACA,IAAI,OAA8B;AAChC,WAAO,KAAK,QAAQ;EACtB;EAEA,IAAI,UAAsC;;AACxC,YAAOA,OAAA,mBAAK,cAAL,gBAAAA,KAAe;EACxB;EAEA,WACE,SACM;AACN,SAAK,UAAU,EAAE,GAAG,mBAAK,kBAAiB,GAAG,QAAQ;AAErD,SAAK,aAAa,KAAK,QAAQ,MAAM;EACvC;EAEU,iBAAiB;AACzB,QAAI,CAAC,KAAK,UAAU,UAAU,KAAK,MAAM,gBAAgB,QAAQ;AAC/D,yBAAK,QAAO,OAAO,IAAI;IACzB;EACF;EAEA,QACE,SACA,SACO;AACP,UAAM,OAAO,YAAY,KAAK,MAAM,MAAM,SAAS,KAAK,OAAO;AAG/D,0BAAK,+BAAL,WAAe;MACb;MACA,MAAM;MACN,eAAe,mCAAS;MACxB,QAAQ,mCAAS;IACnB;AAEA,WAAO;EACT;EAEA,SACE,OACA,iBACM;AACN,0BAAK,+BAAL,WAAe,EAAE,MAAM,YAAY,OAAO,gBAAgB;EAC5D;EAEA,OAAO,SAAwC;;AAC7C,UAAM,WAAUA,OAAA,mBAAK,cAAL,gBAAAA,KAAe;AAC/B,6BAAK,cAAL,mBAAe,OAAO;AACtB,WAAO,UAAU,QAAQ,KAAK,IAAI,EAAE,MAAM,IAAI,IAAI,QAAQ,QAAQ;EACpE;EAEA,UAAgB;AACd,UAAM,QAAQ;AAEd,SAAK,OAAO,EAAE,QAAQ,KAAK,CAAC;EAC9B;EAEA,QAAc;AACZ,SAAK,QAAQ;AACb,SAAK,SAAS,mBAAK,cAAa;EAClC;EAEA,WAAoB;AAClB,WAAO,KAAK,UAAU;MACpB,CAAC,aAAa,eAAe,SAAS,QAAQ,SAAS,IAAI,MAAM;IACnE;EACF;EAEA,aAAsB;AACpB,QAAI,KAAK,kBAAkB,IAAI,GAAG;AAChC,aAAO,CAAC,KAAK,SAAS;IACxB;AAEA,WACE,KAAK,QAAQ,YAAY,aACzB,KAAK,MAAM,kBAAkB,KAAK,MAAM,qBAAqB;EAEjE;EAEA,UAAmB;AACjB,QAAI,KAAK,MAAM,eAAe;AAC5B,aAAO;IACT;AAEA,QAAI,KAAK,kBAAkB,IAAI,GAAG;AAChC,aAAO,KAAK,UAAU;QACpB,CAAC,aAAa,SAAS,iBAAiB,EAAE;MAC5C;IACF;AAEA,WAAO,KAAK,MAAM,SAAS;EAC7B;EAEA,cAAc,YAAY,GAAY;AACpC,WACE,KAAK,MAAM,iBACX,KAAK,MAAM,SAAS,UACpB,CAAC,eAAe,KAAK,MAAM,eAAe,SAAS;EAEvD;EAEA,UAAgB;;AACd,UAAM,WAAW,KAAK,UAAU,KAAK,CAAC,MAAM,EAAE,yBAAyB,CAAC;AAExE,yCAAU,QAAQ,EAAE,eAAe,MAAM;AAGzC,KAAAA,OAAA,mBAAK,cAAL,gBAAAA,KAAe;EACjB;EAEA,WAAiB;;AACf,UAAM,WAAW,KAAK,UAAU,KAAK,CAAC,MAAM,EAAE,uBAAuB,CAAC;AAEtE,yCAAU,QAAQ,EAAE,eAAe,MAAM;AAGzC,KAAAA,OAAA,mBAAK,cAAL,gBAAAA,KAAe;EACjB;EAEA,YAAY,UAAwD;AAClE,QAAI,CAAC,KAAK,UAAU,SAAS,QAAQ,GAAG;AACtC,WAAK,UAAU,KAAK,QAAQ;AAG5B,WAAK,eAAe;AAEpB,yBAAK,QAAO,OAAO,EAAE,MAAM,iBAAiB,OAAO,MAAM,SAAS,CAAC;IACrE;EACF;EAEA,eAAe,UAAwD;AACrE,QAAI,KAAK,UAAU,SAAS,QAAQ,GAAG;AACrC,WAAK,YAAY,KAAK,UAAU,OAAO,CAAC,MAAM,MAAM,QAAQ;AAE5D,UAAI,CAAC,KAAK,UAAU,QAAQ;AAG1B,YAAI,mBAAK,WAAU;AACjB,cAAI,mBAAK,uBAAsB;AAC7B,+BAAK,UAAS,OAAO,EAAE,QAAQ,KAAK,CAAC;UACvC,OAAO;AACL,+BAAK,UAAS,YAAY;UAC5B;QACF;AAEA,aAAK,WAAW;MAClB;AAEA,yBAAK,QAAO,OAAO,EAAE,MAAM,mBAAmB,OAAO,MAAM,SAAS,CAAC;IACvE;EACF;EAEA,oBAA4B;AAC1B,WAAO,KAAK,UAAU;EACxB;EAEA,aAAmB;AACjB,QAAI,CAAC,KAAK,MAAM,eAAe;AAC7B,4BAAK,+BAAL,WAAe,EAAE,MAAM,aAAa;IACtC;EACF;EAEA,MACE,SACA,cACgB;;AAChB,QAAI,KAAK,MAAM,gBAAgB,QAAQ;AACrC,UAAI,KAAK,MAAM,SAAS,WAAa,6CAAc,gBAAe;AAEhE,aAAK,OAAO,EAAE,QAAQ,KAAK,CAAC;MAC9B,WAAW,mBAAK,WAAU;AAExB,2BAAK,UAAS,cAAc;AAE5B,eAAO,mBAAK,UAAS;MACvB;IACF;AAGA,QAAI,SAAS;AACX,WAAK,WAAW,OAAO;IACzB;AAIA,QAAI,CAAC,KAAK,QAAQ,SAAS;AACzB,YAAM,WAAW,KAAK,UAAU,KAAK,CAAC,MAAM,EAAE,QAAQ,OAAO;AAC7D,UAAI,UAAU;AACZ,aAAK,WAAW,SAAS,OAAO;MAClC;IACF;AAEA,QAAI,MAAuC;AACzC,UAAI,CAAC,MAAM,QAAQ,KAAK,QAAQ,QAAQ,GAAG;AACzC,gBAAQ;UACN;QACF;MACF;IACF;AAEA,UAAM,kBAAkB,IAAI,gBAAgB;AAK5C,UAAM,oBAAoB,CAAC,WAAoB;AAC7C,aAAO,eAAe,QAAQ,UAAU;QACtC,YAAY;QACZ,KAAK,MAAM;AACT,6BAAK,sBAAuB;AAC5B,iBAAO,gBAAgB;QACzB;MACF,CAAC;IACH;AAGA,UAAM,UAAU,MAAM;AACpB,YAAM,UAAU,cAAc,KAAK,SAAS,YAAY;AAGxD,YAAM,iBAGF;QACF,UAAU,KAAK;QACf,MAAM,KAAK;MACb;AAEA,wBAAkB,cAAc;AAEhC,yBAAK,sBAAuB;AAC5B,UAAI,KAAK,QAAQ,WAAW;AAC1B,eAAO,KAAK,QAAQ;UAClB;UACA;UACA;QACF;MACF;AAEA,aAAO,QAAQ,cAAiD;IAClE;AAGA,UAAM,UAGF;MACF;MACA,SAAS,KAAK;MACd,UAAU,KAAK;MACf,OAAO,KAAK;MACZ;IACF;AAEA,sBAAkB,OAAO;AAEzB,KAAAA,OAAA,KAAK,QAAQ,aAAb,gBAAAA,KAAuB;MACrB;MACA;;AAIF,uBAAK,cAAe,KAAK;AAGzB,QACE,KAAK,MAAM,gBAAgB,UAC3B,KAAK,MAAM,gBAAc,aAAQ,iBAAR,mBAAsB,OAC/C;AACA,4BAAK,+BAAL,WAAe,EAAE,MAAM,SAAS,OAAM,aAAQ,iBAAR,mBAAsB,KAAK;IACnE;AAEA,UAAM,UAAU,CAAC,UAAyC;;AAExD,UAAI,EAAE,iBAAiB,KAAK,KAAK,MAAM,SAAS;AAC9C,8BAAK,+BAAL,WAAe;UACb,MAAM;UACN;QACF;MACF;AAEA,UAAI,CAAC,iBAAiB,KAAK,GAAG;AAE5B,SAAAC,OAAAD,OAAA,mBAAK,QAAO,QAAO,YAAnB,gBAAAC,IAAA;UAAAD;UACE;UACA;;AAEF,eAAAE,MAAA,mBAAK,QAAO,QAAO,cAAnB;UAAAA;UACE,KAAK,MAAM;UACX;UACA;;MAEJ;AAGA,WAAK,WAAW;IAClB;AAGA,uBAAK,UAAW,cAAc;MAC5B,gBAAgB,6CAAc;MAG9B,IAAI,QAAQ;MACZ,OAAO,gBAAgB,MAAM,KAAK,eAAe;MACjD,WAAW,CAAC,SAAS;;AACnB,YAAI,SAAS,QAAW;AACtB,cAAI,MAAuC;AACzC,oBAAQ;cACN,yIAAyI,KAAK,SAAS;YACzJ;UACF;AACA,kBAAQ,IAAI,MAAM,GAAG,KAAK,SAAS,oBAAoB,CAAQ;AAC/D;QACF;AAEA,YAAI;AACF,eAAK,QAAQ,IAAI;QACnB,SAAS,OAAO;AACd,kBAAQ,KAAe;AACvB;QACF;AAGA,SAAAD,OAAAD,OAAA,mBAAK,QAAO,QAAO,cAAnB,gBAAAC,IAAA,KAAAD,MAA+B,MAAM;AACrC,eAAAE,MAAA,mBAAK,QAAO,QAAO,cAAnB;UAAAA;UACE;UACA,KAAK,MAAM;UACX;;AAIF,aAAK,WAAW;MAClB;MACA;MACA,QAAQ,CAAC,cAAc,UAAU;AAC/B,8BAAK,+BAAL,WAAe,EAAE,MAAM,UAAU,cAAc,MAAM;MACvD;MACA,SAAS,MAAM;AACb,8BAAK,+BAAL,WAAe,EAAE,MAAM,QAAQ;MACjC;MACA,YAAY,MAAM;AAChB,8BAAK,+BAAL,WAAe,EAAE,MAAM,WAAW;MACpC;MACA,OAAO,QAAQ,QAAQ;MACvB,YAAY,QAAQ,QAAQ;MAC5B,aAAa,QAAQ,QAAQ;MAC7B,QAAQ,MAAM;IAChB,CAAC;AAED,WAAO,mBAAK,UAAS,MAAM;EAC7B;AAoFF,GA1cE,+BACA,8BACA,wBACA,0BAEA,iCACA,sCAjBK,kCAmYL,cAAA,SAAU,QAAqC;AAC7C,QAAM,UAAU,CACd,UAC8B;AAC9B,YAAQ,OAAO,MAAM;MACnB,KAAK;AACH,eAAO;UACL,GAAG;UACH,mBAAmB,OAAO;UAC1B,oBAAoB,OAAO;QAC7B;MACF,KAAK;AACH,eAAO;UACL,GAAG;UACH,aAAa;QACf;MACF,KAAK;AACH,eAAO;UACL,GAAG;UACH,aAAa;QACf;MACF,KAAK;AACH,eAAO;UACL,GAAG;UACH,GAAG,WAAW,MAAM,MAAM,KAAK,OAAO;UACtC,WAAW,OAAO,QAAQ;QAC5B;MACF,KAAK;AACH,eAAO;UACL,GAAG;UACH,MAAM,OAAO;UACb,iBAAiB,MAAM,kBAAkB;UACzC,eAAe,OAAO,iBAAiB,KAAK,IAAI;UAChD,OAAO;UACP,eAAe;UACf,QAAQ;UACR,GAAI,CAAC,OAAO,UAAU;YACpB,aAAa;YACb,mBAAmB;YACnB,oBAAoB;UACtB;QACF;MACF,KAAK;AACH,cAAM,QAAQ,OAAO;AAErB,YAAI,iBAAiB,KAAK,KAAK,MAAM,UAAU,mBAAK,eAAc;AAChE,iBAAO,EAAE,GAAG,mBAAK,eAAc,aAAa,OAAO;QACrD;AAEA,eAAO;UACL,GAAG;UACH;UACA,kBAAkB,MAAM,mBAAmB;UAC3C,gBAAgB,KAAK,IAAI;UACzB,mBAAmB,MAAM,oBAAoB;UAC7C,oBAAoB;UACpB,aAAa;UACb,QAAQ;QACV;MACF,KAAK;AACH,eAAO;UACL,GAAG;UACH,eAAe;QACjB;MACF,KAAK;AACH,eAAO;UACL,GAAG;UACH,GAAG,OAAO;QACZ;IACJ;EACF;AAEA,OAAK,QAAQ,QAAQ,KAAK,KAAK;AAE/B,gBAAc,MAAM,MAAM;AACxB,SAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,eAAS,cAAc;IACzB,CAAC;AAED,uBAAK,QAAO,OAAO,EAAE,OAAO,MAAM,MAAM,WAAW,OAAO,CAAC;EAC7D,CAAC;AACH,GApdKF;AAudA,SAAS,WAMd,MACA,SACA;AACA,SAAO;IACL,mBAAmB;IACnB,oBAAoB;IACpB,aAAa,SAAS,QAAQ,WAAW,IAAI,aAAa;IAC1D,GAAI,SAAS,UACV;MACC,OAAO;MACP,QAAQ;IACV;EACJ;AACF;AAEA,SAAS,gBAMP,SAC2B;AAC3B,QAAM,OACJ,OAAO,QAAQ,gBAAgB,aAC1B,QAAQ,YAA2C,IACpD,QAAQ;AAEd,QAAM,UAAU,SAAS;AAEzB,QAAM,uBAAuB,UACzB,OAAO,QAAQ,yBAAyB,aACrC,QAAQ,qBAAkD,IAC3D,QAAQ,uBACV;AAEJ,SAAO;IACL;IACA,iBAAiB;IACjB,eAAe,UAAW,wBAAwB,KAAK,IAAI,IAAK;IAChE,OAAO;IACP,kBAAkB;IAClB,gBAAgB;IAChB,mBAAmB;IACnB,oBAAoB;IACpB,WAAW;IACX,eAAe;IACf,QAAQ,UAAU,YAAY;IAC9B,aAAa;EACf;AACF;;;;AC/kBO,IAAM,cAANG,MAAA,cAAyB,aAAiC;EAG/D,YAAmB,SAA2B,CAAC,GAAG;AAChD,UAAM;AAHR;AAEmB,SAAA,SAAA;AAEjB,uBAAK,UAAW,oBAAI,IAAmB;EACzC;EAEA,MAME,QACA,SAIA,OAC+C;AAC/C,UAAM,WAAW,QAAQ;AACzB,UAAM,YACJ,QAAQ,aAAa,sBAAsB,UAAU,OAAO;AAC9D,QAAI,QAAQ,KAAK,IAA4C,SAAS;AAEtE,QAAI,CAAC,OAAO;AACV,cAAQ,IAAI,MAAM;QAChB,OAAO;QACP;QACA;QACA,SAAS,OAAO,oBAAoB,OAAO;QAC3C;QACA,gBAAgB,OAAO,iBAAiB,QAAQ;MAClD,CAAC;AACD,WAAK,IAAI,KAAK;IAChB;AAEA,WAAO;EACT;EAEA,IAAI,OAAwC;AAC1C,QAAI,CAAC,mBAAK,UAAS,IAAI,MAAM,SAAS,GAAG;AACvC,yBAAK,UAAS,IAAI,MAAM,WAAW,KAAK;AAExC,WAAK,OAAO;QACV,MAAM;QACN;MACF,CAAC;IACH;EACF;EAEA,OAAO,OAAwC;AAC7C,UAAM,aAAa,mBAAK,UAAS,IAAI,MAAM,SAAS;AAEpD,QAAI,YAAY;AACd,YAAM,QAAQ;AAEd,UAAI,eAAe,OAAO;AACxB,2BAAK,UAAS,OAAO,MAAM,SAAS;MACtC;AAEA,WAAK,OAAO,EAAE,MAAM,WAAW,MAAM,CAAC;IACxC;EACF;EAEA,QAAc;AACZ,kBAAc,MAAM,MAAM;AACxB,WAAK,OAAO,EAAE,QAAQ,CAAC,UAAU;AAC/B,aAAK,OAAO,KAAK;MACnB,CAAC;IACH,CAAC;EACH;EAEA,IAME,WAC2D;AAC3D,WAAO,mBAAK,UAAS,IAAI,SAAS;EAGpC;EAEA,SAAuB;AACrB,WAAO,CAAC,GAAG,mBAAK,UAAS,OAAO,CAAC;EACnC;EAEA,KACE,SACgD;AAChD,UAAM,mBAAmB,EAAE,OAAO,MAAM,GAAG,QAAQ;AAEnD,WAAO,KAAK,OAAO,EAAE;MAAK,CAAC,UACzB,WAAW,kBAAkB,KAAK;IACpC;EACF;EAEA,QAAQ,UAAwB,CAAC,GAAiB;AAChD,UAAM,UAAU,KAAK,OAAO;AAC5B,WAAO,OAAO,KAAK,OAAO,EAAE,SAAS,IACjC,QAAQ,OAAO,CAAC,UAAU,WAAW,SAAS,KAAK,CAAC,IACpD;EACN;EAEA,OAAO,OAAoC;AACzC,kBAAc,MAAM,MAAM;AACxB,WAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,iBAAS,KAAK;MAChB,CAAC;IACH,CAAC;EACH;EAEA,UAAgB;AACd,kBAAc,MAAM,MAAM;AACxB,WAAK,OAAO,EAAE,QAAQ,CAAC,UAAU;AAC/B,cAAM,QAAQ;MAChB,CAAC;IACH,CAAC;EACH;EAEA,WAAiB;AACf,kBAAc,MAAM,MAAM;AACxB,WAAK,OAAO,EAAE,QAAQ,CAAC,UAAU;AAC/B,cAAM,SAAS;MACjB,CAAC;IACH,CAAC;EACH;AACF,GAlIE,0BADKA;;;;ACXA,IAAM,YAANC,MAAA,cAKG,UAAU;EASlB,YAAY,QAA6D;AACvE,UAAM;AAfH;AAUL;AACA;AACA,uBAAAC;AAKE,SAAK,aAAa,OAAO;AACzB,uBAAK,gBAAiB,OAAO;AAC7B,uBAAK,YAAa,CAAC;AACnB,SAAK,QAAQ,OAAO,SAASC,iBAAgB;AAE7C,SAAK,WAAW,OAAO,OAAO;AAC9B,SAAK,WAAW;EAClB;EAEA,WACE,SACM;AACN,SAAK,UAAU;AAEf,SAAK,aAAa,KAAK,QAAQ,MAAM;EACvC;EAEA,IAAI,OAAiC;AACnC,WAAO,KAAK,QAAQ;EACtB;EAEA,YAAY,UAAsD;AAChE,QAAI,CAAC,mBAAK,YAAW,SAAS,QAAQ,GAAG;AACvC,yBAAK,YAAW,KAAK,QAAQ;AAG7B,WAAK,eAAe;AAEpB,yBAAK,gBAAe,OAAO;QACzB,MAAM;QACN,UAAU;QACV;MACF,CAAC;IACH;EACF;EAEA,eAAe,UAAsD;AACnE,uBAAK,YAAa,mBAAK,YAAW,OAAO,CAAC,MAAM,MAAM,QAAQ;AAE9D,SAAK,WAAW;AAEhB,uBAAK,gBAAe,OAAO;MACzB,MAAM;MACN,UAAU;MACV;IACF,CAAC;EACH;EAEU,iBAAiB;AACzB,QAAI,CAAC,mBAAK,YAAW,QAAQ;AAC3B,UAAI,KAAK,MAAM,WAAW,WAAW;AACnC,aAAK,WAAW;MAClB,OAAO;AACL,2BAAK,gBAAe,OAAO,IAAI;MACjC;IACF;EACF;EAEA,WAA6B;;AAC3B,aACEF,OAAA,mBAAKC,eAAL,gBAAAD,KAAe;IAEf,KAAK,QAAQ,KAAK,MAAM,SAAU;EAEtC;EAEA,MAAM,QAAQ,WAAuC;;AACnD,uBAAKC,WAAW,cAAc;MAC5B,IAAI,MAAM;AACR,YAAI,CAAC,KAAK,QAAQ,YAAY;AAC5B,iBAAO,QAAQ,OAAO,IAAI,MAAM,qBAAqB,CAAC;QACxD;AACA,eAAO,KAAK,QAAQ,WAAW,SAAS;MAC1C;MACA,QAAQ,CAAC,cAAc,UAAU;AAC/B,8BAAK,qBAAAE,cAAL,WAAe,EAAE,MAAM,UAAU,cAAc,MAAM;MACvD;MACA,SAAS,MAAM;AACb,8BAAK,qBAAAA,cAAL,WAAe,EAAE,MAAM,QAAQ;MACjC;MACA,YAAY,MAAM;AAChB,8BAAK,qBAAAA,cAAL,WAAe,EAAE,MAAM,WAAW;MACpC;MACA,OAAO,KAAK,QAAQ,SAAS;MAC7B,YAAY,KAAK,QAAQ;MACzB,aAAa,KAAK,QAAQ;MAC1B,QAAQ,MAAM,mBAAK,gBAAe,OAAO,IAAI;IAC/C,CAAC;AAED,UAAM,WAAW,KAAK,MAAM,WAAW;AACvC,UAAM,WAAW,CAAC,mBAAKF,WAAS,SAAS;AAEzC,QAAI;AACF,UAAI,CAAC,UAAU;AACb,8BAAK,qBAAAE,cAAL,WAAe,EAAE,MAAM,WAAW,WAAW,SAAS;AAEtD,gBAAM,MAAAH,OAAA,mBAAK,gBAAe,QAAO,aAA3B;UAAAA;UACJ;UACA;;AAEF,cAAM,UAAU,QAAM,gBAAK,SAAQ,aAAb,4BAAwB;AAC9C,YAAI,YAAY,KAAK,MAAM,SAAS;AAClC,gCAAK,qBAAAG,cAAL,WAAe;YACb,MAAM;YACN;YACA;YACA;UACF;QACF;MACF;AACA,YAAM,OAAO,MAAM,mBAAKF,WAAS,MAAM;AAGvC,cAAM,8BAAK,gBAAe,QAAO,cAA3B;;QACJ;QACA;QACA,KAAK,MAAM;QACX;;AAGF,cAAM,gBAAK,SAAQ,cAAb,4BAAyB,MAAM,WAAW,KAAK,MAAM;AAG3D,cAAM,8BAAK,gBAAe,QAAO,cAA3B;;QACJ;QACA;QACA,KAAK,MAAM;QACX,KAAK,MAAM;QACX;;AAGF,cAAM,gBAAK,SAAQ,cAAb,4BAAyB,MAAM,MAAM,WAAW,KAAK,MAAM;AAEjE,4BAAK,qBAAAE,cAAL,WAAe,EAAE,MAAM,WAAW,KAAK;AACvC,aAAO;IACT,SAAS,OAAO;AACd,UAAI;AAEF,gBAAM,8BAAK,gBAAe,QAAO,YAA3B;;UACJ;UACA;UACA,KAAK,MAAM;UACX;;AAGF,gBAAM,gBAAK,SAAQ,YAAb;;UACJ;UACA;UACA,KAAK,MAAM;;AAIb,gBAAM,8BAAK,gBAAe,QAAO,cAA3B;;UACJ;UACA;UACA,KAAK,MAAM;UACX,KAAK,MAAM;UACX;;AAGF,gBAAM,gBAAK,SAAQ,cAAb;;UACJ;UACA;UACA;UACA,KAAK,MAAM;;AAEb,cAAM;MACR,UAAA;AACE,8BAAK,qBAAAA,cAAL,WAAe,EAAE,MAAM,SAAS,MAAuB;MACzD;IACF,UAAA;AACE,yBAAK,gBAAe,QAAQ,IAAI;IAClC;EACF;AAuEF,GA3PE,4BACA,gCACAF,YAAA,eAZK,qCAgMLE,eAAA,SAAU,QAA2D;AACnE,QAAM,UAAU,CACd,UACuD;AACvD,YAAQ,OAAO,MAAM;MACnB,KAAK;AACH,eAAO;UACL,GAAG;UACH,cAAc,OAAO;UACrB,eAAe,OAAO;QACxB;MACF,KAAK;AACH,eAAO;UACL,GAAG;UACH,UAAU;QACZ;MACF,KAAK;AACH,eAAO;UACL,GAAG;UACH,UAAU;QACZ;MACF,KAAK;AACH,eAAO;UACL,GAAG;UACH,SAAS,OAAO;UAChB,MAAM;UACN,cAAc;UACd,eAAe;UACf,OAAO;UACP,UAAU,OAAO;UACjB,QAAQ;UACR,WAAW,OAAO;UAClB,aAAa,KAAK,IAAI;QACxB;MACF,KAAK;AACH,eAAO;UACL,GAAG;UACH,MAAM,OAAO;UACb,cAAc;UACd,eAAe;UACf,OAAO;UACP,QAAQ;UACR,UAAU;QACZ;MACF,KAAK;AACH,eAAO;UACL,GAAG;UACH,MAAM;UACN,OAAO,OAAO;UACd,cAAc,MAAM,eAAe;UACnC,eAAe,OAAO;UACtB,UAAU;UACV,QAAQ;QACV;IACJ;EACF;AACA,OAAK,QAAQ,QAAQ,KAAK,KAAK;AAE/B,gBAAc,MAAM,MAAM;AACxB,uBAAK,YAAW,QAAQ,CAAC,aAAa;AACpC,eAAS,iBAAiB,MAAM;IAClC,CAAC;AACD,uBAAK,gBAAe,OAAO;MACzB,UAAU;MACV,MAAM;MACN;IACF,CAAC;EACH,CAAC;AACH,GApQKH;AAuQA,SAASE,mBAKwC;AACtD,SAAO;IACL,SAAS;IACT,MAAM;IACN,OAAO;IACP,cAAc;IACd,eAAe;IACf,UAAU;IACV,QAAQ;IACR,WAAW;IACX,aAAa;EACf;AACF;;;;ACrRO,IAAM,iBAANE,MAAA,cAA4B,aAAoC;EAKrE,YAAmB,SAA8B,CAAC,GAAG;AACnD,UAAM;AALR;AACA;AACA;AAEmB,SAAA,SAAA;AAEjB,uBAAK,YAAa,oBAAI,IAAI;AAC1B,uBAAK,SAAU,oBAAI,IAAI;AACvB,uBAAK,aAAc;EACrB;EAEA,MACE,QACA,SACA,OAC+C;AAC/C,UAAM,WAAW,IAAI,SAAS;MAC5B,eAAe;MACf,YAAmB,EAAL,uBAAK,aAAL;MACd,SAAS,OAAO,uBAAuB,OAAO;MAC9C;IACF,CAAC;AAED,SAAK,IAAI,QAAQ;AAEjB,WAAO;EACT;EAEA,IAAI,UAA8C;AAChD,uBAAK,YAAW,IAAI,QAAQ;AAC5B,UAAM,QAAQ,SAAS,QAAQ;AAC/B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,kBAAkB,mBAAK,SAAQ,IAAI,KAAK;AAC9C,UAAI,iBAAiB;AACnB,wBAAgB,KAAK,QAAQ;MAC/B,OAAO;AACL,2BAAK,SAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC;MACpC;IACF;AACA,SAAK,OAAO,EAAE,MAAM,SAAS,SAAS,CAAC;EACzC;EAEA,OAAO,UAA8C;AACnD,QAAI,mBAAK,YAAW,OAAO,QAAQ,GAAG;AACpC,YAAM,QAAQ,SAAS,QAAQ;AAC/B,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,kBAAkB,mBAAK,SAAQ,IAAI,KAAK;AAC9C,YAAI,iBAAiB;AACnB,cAAI,gBAAgB,SAAS,GAAG;AAC9B,kBAAM,QAAQ,gBAAgB,QAAQ,QAAQ;AAC9C,gBAAI,UAAU,IAAI;AAChB,8BAAgB,OAAO,OAAO,CAAC;YACjC;UACF,WAAW,gBAAgB,CAAC,MAAM,UAAU;AAC1C,+BAAK,SAAQ,OAAO,KAAK;UAC3B;QACF;MACF;IACF;AAIA,SAAK,OAAO,EAAE,MAAM,WAAW,SAAS,CAAC;EAC3C;EAEA,OAAO,UAAiD;AACtD,UAAM,QAAQ,SAAS,QAAQ;AAC/B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,yBAAyB,mBAAK,SAAQ,IAAI,KAAK;AACrD,YAAM,uBAAuB,iEAAwB;QACnD,CAAC,MAAM,EAAE,MAAM,WAAW;;AAI5B,aAAO,CAAC,wBAAwB,yBAAyB;IAC3D,OAAO;AAGL,aAAO;IACT;EACF;EAEA,QAAQ,UAA0D;;AAChE,UAAM,QAAQ,SAAS,QAAQ;AAC/B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,iBAAgBA,OAAA,mBAAK,SACxB,IAAI,KAAK,MADU,gBAAAA,KAElB,KAAK,CAAC,MAAM,MAAM,YAAY,EAAE,MAAM;AAE1C,cAAO,+CAAe,eAAc,QAAQ,QAAQ;IACtD,OAAO;AACL,aAAO,QAAQ,QAAQ;IACzB;EACF;EAEA,QAAc;AACZ,kBAAc,MAAM,MAAM;AACxB,yBAAK,YAAW,QAAQ,CAAC,aAAa;AACpC,aAAK,OAAO,EAAE,MAAM,WAAW,SAAS,CAAC;MAC3C,CAAC;AACD,yBAAK,YAAW,MAAM;AACtB,yBAAK,SAAQ,MAAM;IACrB,CAAC;EACH;EAEA,SAA0B;AACxB,WAAO,MAAM,KAAK,mBAAK,WAAU;EACnC;EAEA,KAME,SAC2D;AAC3D,UAAM,mBAAmB,EAAE,OAAO,MAAM,GAAG,QAAQ;AAEnD,WAAO,KAAK,OAAO,EAAE;MAAK,CAAC,aACzB,cAAc,kBAAkB,QAAQ;IAC1C;EACF;EAEA,QAAQ,UAA2B,CAAC,GAAoB;AACtD,WAAO,KAAK,OAAO,EAAE,OAAO,CAAC,aAAa,cAAc,SAAS,QAAQ,CAAC;EAC5E;EAEA,OAAO,OAAiC;AACtC,kBAAc,MAAM,MAAM;AACxB,WAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,iBAAS,KAAK;MAChB,CAAC;IACH,CAAC;EACH;EAEA,wBAA0C;AACxC,UAAM,kBAAkB,KAAK,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,MAAM,QAAQ;AAEpE,WAAO,cAAc;MAAM,MACzB,QAAQ;QACN,gBAAgB,IAAI,CAAC,aAAa,SAAS,SAAS,EAAE,MAAM,IAAI,CAAC;MACnE;IACF;EACF;AACF,GAjJE,4BACA,yBACA,6BAHKA;AAoJP,SAAS,SAAS,UAAwC;;AACxD,UAAOA,OAAA,SAAS,QAAQ,UAAjB,gBAAAA,KAAwB;AACjC;;;AC/NO,SAAS,sBACd,OACsE;AACtE,SAAO;IACL,SAAS,CAAC,SAAS,UAAU;;AAC3B,YAAM,UAAU,QAAQ;AACxB,YAAM,aAAY,YAAAC,OAAA,QAAQ,iBAAR,gBAAAA,KAAsB,SAAtB,mBAA4B,cAA5B,mBAAuC;AACzD,YAAM,aAAW,aAAQ,MAAM,SAAd,mBAAoB,UAAS,CAAC;AAC/C,YAAM,kBAAgB,aAAQ,MAAM,SAAd,mBAAoB,eAAc,CAAC;AACzD,UAAI,SAAgC,EAAE,OAAO,CAAC,GAAG,YAAY,CAAC,EAAE;AAChE,UAAI,cAAc;AAElB,YAAM,UAAU,YAAY;AAC1B,YAAI,YAAY;AAChB,cAAM,oBAAoB,CAAC,WAAoB;AAC7C,iBAAO,eAAe,QAAQ,UAAU;YACtC,YAAY;YACZ,KAAK,MAAM;AACT,kBAAI,QAAQ,OAAO,SAAS;AAC1B,4BAAY;cACd,OAAO;AACL,wBAAQ,OAAO,iBAAiB,SAAS,MAAM;AAC7C,8BAAY;gBACd,CAAC;cACH;AACA,qBAAO,QAAQ;YACjB;UACF,CAAC;QACH;AAEA,cAAM,UAAU,cAAc,QAAQ,SAAS,QAAQ,YAAY;AAGnE,cAAM,YAAY,OAChB,MACA,OACA,aACmC;AACnC,cAAI,WAAW;AACb,mBAAO,QAAQ,OAAO;UACxB;AAEA,cAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AACtC,mBAAO,QAAQ,QAAQ,IAAI;UAC7B;AAEA,gBAAM,iBAGF;YACF,UAAU,QAAQ;YAClB,WAAW;YACX,WAAW,WAAW,aAAa;YACnC,MAAM,QAAQ,QAAQ;UACxB;AAEA,4BAAkB,cAAc;AAEhC,gBAAM,OAAO,MAAM;YACjB;UACF;AAEA,gBAAM,EAAE,SAAS,IAAI,QAAQ;AAC7B,gBAAM,QAAQ,WAAW,aAAa;AAEtC,iBAAO;YACL,OAAO,MAAM,KAAK,OAAO,MAAM,QAAQ;YACvC,YAAY,MAAM,KAAK,YAAY,OAAO,QAAQ;UACpD;QACF;AAGA,YAAI,aAAa,SAAS,QAAQ;AAChC,gBAAM,WAAW,cAAc;AAC/B,gBAAM,cAAc,WAAW,uBAAuB;AACtD,gBAAM,UAAU;YACd,OAAO;YACP,YAAY;UACd;AACA,gBAAM,QAAQ,YAAY,SAAS,OAAO;AAE1C,mBAAS,MAAM,UAAU,SAAS,OAAO,QAAQ;QACnD,OAAO;AACL,gBAAM,iBAAiB,SAAS,SAAS;AAGzC,aAAG;AACD,kBAAM,QACJ,gBAAgB,IACX,cAAc,CAAC,KAAK,QAAQ,mBAC7B,iBAAiB,SAAS,MAAM;AACtC,gBAAI,cAAc,KAAK,SAAS,MAAM;AACpC;YACF;AACA,qBAAS,MAAM,UAAU,QAAQ,KAAK;AACtC;UACF,SAAS,cAAc;QACzB;AAEA,eAAO;MACT;AACA,UAAI,QAAQ,QAAQ,WAAW;AAC7B,gBAAQ,UAAU,MAAM;;AACtB,kBAAOC,OAAAD,OAAA,QAAQ,SAAQ,cAAhB,gBAAAC,IAAA;YAAAD;YACL;YACA;cACE,UAAU,QAAQ;cAClB,MAAM,QAAQ,QAAQ;cACtB,QAAQ,QAAQ;YAClB;YACA;;QAEJ;MACF,OAAO;AACL,gBAAQ,UAAU;MACpB;IACF;EACF;AACF;AAEA,SAAS,iBACP,SACA,EAAE,OAAO,WAAW,GACC;AACrB,QAAM,YAAY,MAAM,SAAS;AACjC,SAAO,MAAM,SAAS,IAClB,QAAQ;IACN,MAAM,SAAS;IACf;IACA,WAAW,SAAS;IACpB;EACF,IACA;AACN;AAEA,SAAS,qBACP,SACA,EAAE,OAAO,WAAW,GACC;;AACrB,SAAO,MAAM,SAAS,KAClBA,OAAA,QAAQ,yBAAR,gBAAAA,KAAA,cAA+B,MAAM,CAAC,GAAG,OAAO,WAAW,CAAC,GAAG,cAC/D;AACN;AAKO,SAAS,YACd,SACA,MACS;AACT,MAAI,CAAC;AAAM,WAAO;AAClB,SAAO,iBAAiB,SAAS,IAAI,KAAK;AAC5C;AAKO,SAAS,gBACd,SACA,MACS;AACT,MAAI,CAAC,QAAQ,CAAC,QAAQ;AAAsB,WAAO;AACnD,SAAO,qBAAqB,SAAS,IAAI,KAAK;AAChD;;;;AClHO,IAAM,eAANE,MAAA,MAAkB;EAUvB,YAAY,SAA4B,CAAC,GAAG;AAT5C;AACA,uBAAAC;AACA,uBAAAC;AACA;AACA;AACA;AACA;AACA;AAGE,uBAAK,aAAc,OAAO,cAAc,IAAI,WAAW;AACvD,uBAAKD,iBAAiB,OAAO,iBAAiB,IAAI,cAAc;AAChE,uBAAKC,kBAAkB,OAAO,kBAAkB,CAAC;AACjD,uBAAK,gBAAiB,oBAAI,IAAI;AAC9B,uBAAK,mBAAoB,oBAAI,IAAI;AACjC,uBAAK,aAAc;EACrB;EAEA,QAAc;AACZ,2BAAK,aAAL;AACA,QAAI,mBAAK,iBAAgB;AAAG;AAE5B,uBAAK,mBAAoB,aAAa,UAAU,OAAO,YAAY;AACjE,UAAI,SAAS;AACX,cAAM,KAAK,sBAAsB;AACjC,2BAAK,aAAY,QAAQ;MAC3B;IACF,CAAC;AACD,uBAAK,oBAAqB,cAAc,UAAU,OAAO,WAAW;AAClE,UAAI,QAAQ;AACV,cAAM,KAAK,sBAAsB;AACjC,2BAAK,aAAY,SAAS;MAC5B;IACF,CAAC;EACH;EAEA,UAAgB;;AACd,2BAAK,aAAL;AACA,QAAI,mBAAK,iBAAgB;AAAG;AAE5B,KAAAF,OAAA,mBAAK,uBAAL,gBAAAA,KAAA;AACA,uBAAK,mBAAoB;AAEzB,6BAAK,wBAAL;AACA,uBAAK,oBAAqB;EAC5B;EAEA,WAEE,SAAiC;AACjC,WAAO,mBAAK,aAAY,QAAQ,EAAE,GAAG,SAAS,aAAa,WAAW,CAAC,EACpE;EACL;EAEA,WAEE,SAAoC;AACpC,WAAO,mBAAKC,iBAAe,QAAQ,EAAE,GAAG,SAAS,QAAQ,UAAU,CAAC,EAAE;EACxE;EAEA,aAUE,UAA6D;;AAC7D,UAAM,UAAU,KAAK,oBAAoB,EAAE,SAAS,CAAC;AAErD,YAAOD,OAAA,mBAAK,aAAY,IAAI,QAAQ,SAAS,MAAtC,gBAAAA,KAAyC,MAAM;EAGxD;EAEA,gBAME,SACgB;AAChB,UAAM,mBAAmB,KAAK,oBAAoB,OAAO;AACzD,UAAM,QAAQ,mBAAK,aAAY,MAAM,MAAM,gBAAgB;AAC3D,UAAM,aAAa,MAAM,MAAM;AAE/B,QAAI,eAAe,QAAW;AAC5B,aAAO,KAAK,WAAW,OAAO;IAChC;AAEA,QACE,QAAQ,qBACR,MAAM,cAAc,iBAAiB,iBAAiB,WAAW,KAAK,CAAC,GACvE;AACA,WAAK,KAAK,cAAc,gBAAgB;IAC1C;AAEA,WAAO,QAAQ,QAAQ,UAAU;EACnC;EAEA,eAiBE,SACqD;AACrD,WAAO,mBAAK,aAAY,QAAQ,OAAO,EAAE,IAAI,CAAC,EAAE,UAAU,MAAM,MAAM;AACpE,YAAM,OAAO,MAAM;AACnB,aAAO,CAAC,UAAU,IAAI;IACxB,CAAC;EACH;EAEA,aAWE,UACA,SAIA,SACkC;AAClC,UAAM,mBAAmB,KAAK,oBAM5B,EAAE,SAAS,CAAC;AAEd,UAAM,QAAQ,mBAAK,aAAY;MAC7B,iBAAiB;IACnB;AACA,UAAM,WAAW,+BAAO,MAAM;AAC9B,UAAM,OAAO,iBAAiB,SAAS,QAAQ;AAE/C,QAAI,SAAS,QAAW;AACtB,aAAO;IACT;AAEA,WAAO,mBAAK,aACT,MAAM,MAAM,gBAAgB,EAC5B,QAAQ,MAAM,EAAE,GAAG,SAAS,QAAQ,KAAK,CAAC;EAC/C;EAEA,eAiBE,SACA,SAIA,SACqD;AACrD,WAAO,cAAc;MAAM,MACzB,mBAAK,aACF,QAAQ,OAAO,EACf,IAAI,CAAC,EAAE,SAAS,MAAM;QACrB;QACA,KAAK,aAAmC,UAAU,SAAS,OAAO;MACpE,CAAC;IACL;EACF;EAEA,cAqBE,UAC8D;;AAC9D,UAAM,UAAU,KAAK,oBAAoB,EAAE,SAAS,CAAC;AACrD,YAAOA,OAAA,mBAAK,aAAY;MACtB,QAAQ;IACV,MAFO,gBAAAA,KAEJ;EACL;EAEA,cAEE,SAA+B;AAC/B,UAAM,aAAa,mBAAK;AACxB,kBAAc,MAAM,MAAM;AACxB,iBAAW,QAAQ,OAAO,EAAE,QAAQ,CAAC,UAAU;AAC7C,mBAAW,OAAO,KAAK;MACzB,CAAC;IACH,CAAC;EACH;EAEA,aAEE,SAAyB,SAAuC;AAChE,UAAM,aAAa,mBAAK;AAExB,UAAM,iBAAsC;MAC1C,MAAM;MACN,GAAG;IACL;AAEA,WAAO,cAAc,MAAM,MAAM;AAC/B,iBAAW,QAAQ,OAAO,EAAE,QAAQ,CAAC,UAAU;AAC7C,cAAM,MAAM;MACd,CAAC;AACD,aAAO,KAAK,eAAe,gBAAgB,OAAO;IACpD,CAAC;EACH;EAEA,cAEE,SAAyB,gBAA+B,CAAC,GAAkB;AAC3E,UAAM,yBAAyB,EAAE,QAAQ,MAAM,GAAG,cAAc;AAEhE,UAAM,WAAW,cAAc;MAAM,MACnC,mBAAK,aACF,QAAQ,OAAO,EACf,IAAI,CAAC,UAAU,MAAM,OAAO,sBAAsB,CAAC;IACxD;AAEA,WAAO,QAAQ,IAAI,QAAQ,EAAE,KAAK,IAAI,EAAE,MAAM,IAAI;EACpD;EAEA,kBAQE,SACA,UAA6B,CAAC,GACf;AACf,WAAO,cAAc,MAAM,MAAM;AAC/B,yBAAK,aAAY,QAAQ,OAAO,EAAE,QAAQ,CAAC,UAAU;AACnD,cAAM,WAAW;MACnB,CAAC;AAED,WAAI,mCAAS,iBAAgB,QAAQ;AACnC,eAAO,QAAQ,QAAQ;MACzB;AACA,YAAM,iBAAsC;QAC1C,GAAG;QACH,OAAM,mCAAS,iBAAe,mCAAS,SAAQ;MACjD;AACA,aAAO,KAAK,eAAe,gBAAgB,OAAO;IACpD,CAAC;EACH;EAEA,eAQE,SACA,UAA0B,CAAC,GACZ;AACf,UAAM,eAAe;MACnB,GAAG;MACH,eAAe,QAAQ,iBAAiB;IAC1C;AACA,UAAM,WAAW,cAAc;MAAM,MACnC,mBAAK,aACF,QAAQ,OAAO,EACf,OAAO,CAAC,UAAU,CAAC,MAAM,WAAW,CAAC,EACrC,IAAI,CAAC,UAAU;AACd,YAAI,UAAU,MAAM,MAAM,QAAW,YAAY;AACjD,YAAI,CAAC,aAAa,cAAc;AAC9B,oBAAU,QAAQ,MAAM,IAAI;QAC9B;AACA,eAAO,MAAM,MAAM,gBAAgB,WAC/B,QAAQ,QAAQ,IAChB;MACN,CAAC;IACL;AAEA,WAAO,QAAQ,IAAI,QAAQ,EAAE,KAAK,IAAI;EACxC;EAEA,WAOE,SAOgB;AAChB,UAAM,mBAAmB,KAAK,oBAAoB,OAAO;AAGzD,QAAI,iBAAiB,UAAU,QAAW;AACxC,uBAAiB,QAAQ;IAC3B;AAEA,UAAM,QAAQ,mBAAK,aAAY,MAAM,MAAM,gBAAgB;AAE3D,WAAO,MAAM;MACX,iBAAiB,iBAAiB,WAAW,KAAK;IACpD,IACI,MAAM,MAAM,gBAAgB,IAC5B,QAAQ,QAAQ,MAAM,MAAM,IAAa;EAC/C;EAEA,cAME,SACe;AACf,WAAO,KAAK,WAAW,OAAO,EAAE,KAAK,IAAI,EAAE,MAAM,IAAI;EACvD;EAEA,mBAOE,SAO0C;AAC1C,YAAQ,WAAW,sBAKjB,QAAQ,KAAK;AACf,WAAO,KAAK,WAAW,OAAc;EACvC;EAEA,sBAOE,SAOe;AACf,WAAO,KAAK,mBAAmB,OAAO,EAAE,KAAK,IAAI,EAAE,MAAM,IAAI;EAC/D;EAEA,wBAOE,SAO0C;AAC1C,YAAQ,WAAW,sBAKjB,QAAQ,KAAK;AAEf,WAAO,KAAK,gBAAgB,OAAc;EAC5C;EAEA,wBAA0C;AACxC,QAAI,cAAc,SAAS,GAAG;AAC5B,aAAO,mBAAKC,iBAAe,sBAAsB;IACnD;AACA,WAAO,QAAQ,QAAQ;EACzB;EAEA,gBAA4B;AAC1B,WAAO,mBAAK;EACd;EAEA,mBAAkC;AAChC,WAAO,mBAAKA;EACd;EAEA,oBAAoC;AAClC,WAAO,mBAAKC;EACd;EAEA,kBAAkB,SAA+B;AAC/C,uBAAKA,kBAAkB;EACzB;EAEA,iBAME,UACA,SAMM;AACN,uBAAK,gBAAe,IAAI,QAAQ,QAAQ,GAAG;MACzC;MACA,gBAAgB;IAClB,CAAC;EACH;EAEA,iBACE,UACsE;AACtE,UAAM,WAAW,CAAC,GAAG,mBAAK,gBAAe,OAAO,CAAC;AAEjD,UAAM,SAGF,CAAC;AAEL,aAAS,QAAQ,CAAC,iBAAiB;AACjC,UAAI,gBAAgB,UAAU,aAAa,QAAQ,GAAG;AACpD,eAAO,OAAO,QAAQ,aAAa,cAAc;MACnD;IACF,CAAC;AACD,WAAO;EACT;EAEA,oBAME,aACA,SAIM;AACN,uBAAK,mBAAkB,IAAI,QAAQ,WAAW,GAAG;MAC/C;MACA,gBAAgB;IAClB,CAAC;EACH;EAEA,oBACE,aAC6C;AAC7C,UAAM,WAAW,CAAC,GAAG,mBAAK,mBAAkB,OAAO,CAAC;AAEpD,QAAI,SAAsD,CAAC;AAE3D,aAAS,QAAQ,CAAC,iBAAiB;AACjC,UAAI,gBAAgB,aAAa,aAAa,WAAW,GAAG;AAC1D,iBAAS,EAAE,GAAG,QAAQ,GAAG,aAAa,eAAe;MACvD;IACF,CAAC;AAED,WAAO;EACT;EAEA,oBAQE,SAsBA;AACA,QAAI,QAAQ,YAAY;AACtB,aAAO;IAOT;AAEA,UAAM,mBAAmB;MACvB,GAAG,mBAAKA,kBAAgB;MACxB,GAAG,KAAK,iBAAiB,QAAQ,QAAQ;MACzC,GAAG;MACH,YAAY;IACd;AAEA,QAAI,CAAC,iBAAiB,WAAW;AAC/B,uBAAiB,YAAY;QAC3B,iBAAiB;QACjB;MACF;IACF;AAGA,QAAI,iBAAiB,uBAAuB,QAAW;AACrD,uBAAiB,qBACf,iBAAiB,gBAAgB;IACrC;AACA,QAAI,iBAAiB,iBAAiB,QAAW;AAC/C,uBAAiB,eAAe,CAAC,CAAC,iBAAiB;IACrD;AAEA,QAAI,CAAC,iBAAiB,eAAe,iBAAiB,WAAW;AAC/D,uBAAiB,cAAc;IACjC;AAEA,QAAI,iBAAiB,YAAY,WAAW;AAC1C,uBAAiB,UAAU;IAC7B;AAEA,WAAO;EAOT;EAEA,uBACE,SACG;AACH,QAAI,mCAAS,YAAY;AACvB,aAAO;IACT;AACA,WAAO;MACL,GAAG,mBAAKA,kBAAgB;MACxB,IAAI,mCAAS,gBACX,KAAK,oBAAoB,QAAQ,WAAW;MAC9C,GAAG;MACH,YAAY;IACd;EACF;EAEA,QAAc;AACZ,uBAAK,aAAY,MAAM;AACvB,uBAAKD,iBAAe,MAAM;EAC5B;AACF,GAhoBE,6BACAA,kBAAA,eACAC,mBAAA,eACA,gCACA,mCACA,6BACA,mCACA,oCARKF;;;;AClBA,IAAM,iBAANG,MAAA,cAMG,aAAmD;EAyB3D,YACE,QACO,SAOP;AACA,UAAM;AAzCH;AAOL;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AAGA;;;AACA;AACA;AACA;AACA,sCAAgB,oBAAI,IAA+B;AAI1C,SAAA,UAAA;AAUP,uBAAK,SAAU;AACf,uBAAK,cAAe;AACpB,uBAAK,kBAAmB,gBAAgB;AACxC,QAAI,CAAC,KAAK,QAAQ,+BAA+B;AAC/C,yBAAK,kBAAiB;QACpB,IAAI,MAAM,2DAA2D;MACvE;IACF;AAEA,SAAK,YAAY;AACjB,SAAK,WAAW,OAAO;EACzB;EAEU,cAAoB;AAC5B,SAAK,UAAU,KAAK,QAAQ,KAAK,IAAI;EACvC;EAEU,cAAoB;AAC5B,QAAI,KAAK,UAAU,SAAS,GAAG;AAC7B,yBAAK,eAAc,YAAY,IAAI;AAEnC,UAAI,mBAAmB,mBAAK,gBAAe,KAAK,OAAO,GAAG;AACxD,8BAAK,2CAAL;MACF,OAAO;AACL,aAAK,aAAa;MACpB;AAEA,4BAAK,2CAAL;IACF;EACF;EAEU,gBAAsB;AAC9B,QAAI,CAAC,KAAK,aAAa,GAAG;AACxB,WAAK,QAAQ;IACf;EACF;EAEA,yBAAkC;AAChC,WAAO;MACL,mBAAK;MACL,KAAK;MACL,KAAK,QAAQ;IACf;EACF;EAEA,2BAAoC;AAClC,WAAO;MACL,mBAAK;MACL,KAAK;MACL,KAAK,QAAQ;IACf;EACF;EAEA,UAAgB;AACd,SAAK,YAAY,oBAAI,IAAI;AACzB,0BAAK,gDAAL;AACA,0BAAK,mDAAL;AACA,uBAAK,eAAc,eAAe,IAAI;EACxC;EAEA,WACE,SAOA,eACM;AACN,UAAM,cAAc,KAAK;AACzB,UAAM,YAAY,mBAAK;AAEvB,SAAK,UAAU,mBAAK,SAAQ,oBAAoB,OAAO;AAEvD,QACE,KAAK,QAAQ,YAAY,UACzB,OAAO,KAAK,QAAQ,YAAY,aAChC,OAAO,KAAK,QAAQ,YAAY,cAChC,OAAO,eAAe,KAAK,QAAQ,SAAS,mBAAK,cAAa,MAC5D,WACF;AACA,YAAM,IAAI;QACR;MACF;IACF;AAEA,0BAAK,0CAAL;AACA,uBAAK,eAAc,WAAW,KAAK,OAAO;AAE1C,QACE,YAAY,cACZ,CAAC,oBAAoB,KAAK,SAAS,WAAW,GAC9C;AACA,yBAAK,SAAQ,cAAc,EAAE,OAAO;QAClC,MAAM;QACN,OAAO,mBAAK;QACZ,UAAU;MACZ,CAAC;IACH;AAEA,UAAM,UAAU,KAAK,aAAa;AAGlC,QACE,WACA;MACE,mBAAK;MACL;MACA,KAAK;MACL;IACF,GACA;AACA,4BAAK,2CAAL;IACF;AAGA,SAAK,aAAa,aAAa;AAG/B,QACE,YACC,mBAAK,mBAAkB,aACtB,eAAe,KAAK,QAAQ,SAAS,mBAAK,cAAa,MACrD,eAAe,YAAY,SAAS,mBAAK,cAAa,KACxD,iBAAiB,KAAK,QAAQ,WAAW,mBAAK,cAAa,MACzD,iBAAiB,YAAY,WAAW,mBAAK,cAAa,IAC9D;AACA,4BAAK,iDAAL;IACF;AAEA,UAAM,sBAAsB,sBAAK,qDAAL;AAG5B,QACE,YACC,mBAAK,mBAAkB,aACtB,eAAe,KAAK,QAAQ,SAAS,mBAAK,cAAa,MACrD,eAAe,YAAY,SAAS,mBAAK,cAAa,KACxD,wBAAwB,mBAAK,2BAC/B;AACA,4BAAK,oDAAL,WAA4B;IAC9B;EACF;EAEA,oBACE,SAOoC;AACpC,UAAM,QAAQ,mBAAK,SAAQ,cAAc,EAAE,MAAM,mBAAK,UAAS,OAAO;AAEtE,UAAM,SAAS,KAAK,aAAa,OAAO,OAAO;AAE/C,QAAI,sCAAsC,MAAM,MAAM,GAAG;AAiBvD,yBAAK,gBAAiB;AACtB,yBAAK,uBAAwB,KAAK;AAClC,yBAAK,qBAAsB,mBAAK,eAAc;IAChD;AACA,WAAO;EACT;EAEA,mBAAuD;AACrD,WAAO,mBAAK;EACd;EAEA,YACE,QACA,eACoC;AACpC,UAAM,gBAAgB,CAAC;AAEvB,WAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,QAAQ;AACnC,aAAO,eAAe,eAAe,KAAK;QACxC,cAAc;QACd,YAAY;QACZ,KAAK,MAAM;AACT,eAAK,UAAU,GAAgC;AAC/C,yDAAgB;AAChB,iBAAO,OAAO,GAAgC;QAChD;MACF,CAAC;IACH,CAAC;AAED,WAAO;EACT;EAEA,UAAU,KAAgC;AACxC,uBAAK,eAAc,IAAI,GAAG;EAC5B;EAEA,kBAAsE;AACpE,WAAO,mBAAK;EACd;EAEA,QAAQ,EAAE,GAAG,QAAQ,IAAoB,CAAC,GAExC;AACA,WAAO,KAAK,MAAM;MAChB,GAAG;IACL,CAAC;EACH;EAEA,gBACE,SAO6C;AAC7C,UAAM,mBAAmB,mBAAK,SAAQ,oBAAoB,OAAO;AAEjE,UAAM,QAAQ,mBAAK,SAChB,cAAc,EACd,MAAM,mBAAK,UAAS,gBAAgB;AAEvC,WAAO,MAAM,MAAM,EAAE,KAAK,MAAM,KAAK,aAAa,OAAO,gBAAgB,CAAC;EAC5E;EAEU,MACR,cAC6C;AAC7C,WAAO,sBAAK,2CAAL,WAAmB;MACxB,GAAG;MACH,eAAe,aAAa,iBAAiB;IAC/C,GAAG,KAAK,MAAM;AACZ,WAAK,aAAa;AAClB,aAAO,mBAAK;IACd,CAAC;EACH;EAgGU,aACR,OACA,SAOoC;;AACpC,UAAM,YAAY,mBAAK;AACvB,UAAM,cAAc,KAAK;AACzB,UAAM,aAAa,mBAAK;AAGxB,UAAM,kBAAkB,mBAAK;AAC7B,UAAM,oBAAoB,mBAAK;AAC/B,UAAM,cAAc,UAAU;AAC9B,UAAM,oBAAoB,cACtB,MAAM,QACN,mBAAK;AAET,UAAM,EAAE,MAAM,IAAI;AAClB,QAAI,WAAW,EAAE,GAAG,MAAM;AAC1B,QAAI,oBAAoB;AACxB,QAAI;AAGJ,QAAI,QAAQ,oBAAoB;AAC9B,YAAM,UAAU,KAAK,aAAa;AAElC,YAAM,eAAe,CAAC,WAAW,mBAAmB,OAAO,OAAO;AAElE,YAAM,kBACJ,WAAW,sBAAsB,OAAO,WAAW,SAAS,WAAW;AAEzE,UAAI,gBAAgB,iBAAiB;AACnC,mBAAW;UACT,GAAG;UACH,GAAG,WAAW,MAAM,MAAM,MAAM,OAAO;QACzC;MACF;AACA,UAAI,QAAQ,uBAAuB,eAAe;AAChD,iBAAS,cAAc;MACzB;IACF;AAEA,QAAI,EAAE,OAAO,gBAAgB,OAAO,IAAI;AAGxC,QAAI,QAAQ,UAAU,SAAS,SAAS,QAAW;AAEjD,UACE,cACA,SAAS,UAAS,mDAAiB,SACnC,QAAQ,WAAW,mBAAK,YACxB;AACA,eAAO,mBAAK;MACd,OAAO;AACL,YAAI;AACF,6BAAK,WAAY,QAAQ;AACzB,iBAAO,QAAQ,OAAO,SAAS,IAAI;AACnC,iBAAO,YAAY,yCAAY,MAAM,MAAM,OAAO;AAClD,6BAAK,eAAgB;AACrB,6BAAK,cAAe;QACtB,SAAS,aAAa;AACpB,6BAAK,cAAe;QACtB;MACF;IACF,OAEK;AACH,aAAO,SAAS;IAClB;AAGA,QACE,QAAQ,oBAAoB,UAC5B,SAAS,UACT,WAAW,WACX;AACA,UAAI;AAGJ,WACE,yCAAY,sBACZ,QAAQ,qBAAoB,uDAAmB,kBAC/C;AACA,0BAAkB,WAAW;MAC/B,OAAO;AACL,0BACE,OAAO,QAAQ,oBAAoB,aAE7B,QAAQ;WAERA,OAAA,mBAAK,+BAAL,gBAAAA,KAAgC,MAAM;UACtC,mBAAK;QACP,IACA,QAAQ;AACd,YAAI,QAAQ,UAAU,oBAAoB,QAAW;AACnD,cAAI;AACF,8BAAkB,QAAQ,OAAO,eAAe;AAChD,+BAAK,cAAe;UACtB,SAAS,aAAa;AACpB,+BAAK,cAAe;UACtB;QACF;MACF;AAEA,UAAI,oBAAoB,QAAW;AACjC,iBAAS;AACT,eAAO;UACL,yCAAY;UACZ;UACA;QACF;AACA,4BAAoB;MACtB;IACF;AAEA,QAAI,mBAAK,eAAc;AACrB,cAAQ,mBAAK;AACb,aAAO,mBAAK;AACZ,uBAAiB,KAAK,IAAI;AAC1B,eAAS;IACX;AAEA,UAAM,aAAa,SAAS,gBAAgB;AAC5C,UAAM,YAAY,WAAW;AAC7B,UAAM,UAAU,WAAW;AAE3B,UAAM,YAAY,aAAa;AAC/B,UAAM,UAAU,SAAS;AAEzB,UAAM,SAAiD;MACrD;MACA,aAAa,SAAS;MACtB;MACA,WAAW,WAAW;MACtB;MACA,kBAAkB;MAClB;MACA;MACA,eAAe,SAAS;MACxB;MACA;MACA,cAAc,SAAS;MACvB,eAAe,SAAS;MACxB,kBAAkB,SAAS;MAC3B,WAAW,SAAS,kBAAkB,KAAK,SAAS,mBAAmB;MACvE,qBACE,SAAS,kBAAkB,kBAAkB,mBAC7C,SAAS,mBAAmB,kBAAkB;MAChD;MACA,cAAc,cAAc,CAAC;MAC7B,gBAAgB,WAAW,CAAC;MAC5B,UAAU,SAAS,gBAAgB;MACnC;MACA,gBAAgB,WAAW;MAC3B,SAAS,QAAQ,OAAO,OAAO;MAC/B,SAAS,KAAK;MACd,SAAS,mBAAK;IAChB;AAEA,UAAM,aAAa;AAEnB,QAAI,KAAK,QAAQ,+BAA+B;AAC9C,YAAM,6BAA6B,CAAC,aAAqC;AACvE,YAAI,WAAW,WAAW,SAAS;AACjC,mBAAS,OAAO,WAAW,KAAK;QAClC,WAAW,WAAW,SAAS,QAAW;AACxC,mBAAS,QAAQ,WAAW,IAAI;QAClC;MACF;AAKA,YAAM,mBAAmB,MAAM;AAC7B,cAAM,UACH,mBAAK,kBACN,WAAW,UACT,gBAAgB;AAEpB,mCAA2B,OAAO;MACpC;AAEA,YAAM,eAAe,mBAAK;AAC1B,cAAQ,aAAa,QAAQ;QAC3B,KAAK;AAGH,cAAI,MAAM,cAAc,UAAU,WAAW;AAC3C,uCAA2B,YAAY;UACzC;AACA;QACF,KAAK;AACH,cACE,WAAW,WAAW,WACtB,WAAW,SAAS,aAAa,OACjC;AACA,6BAAiB;UACnB;AACA;QACF,KAAK;AACH,cACE,WAAW,WAAW,WACtB,WAAW,UAAU,aAAa,QAClC;AACA,6BAAiB;UACnB;AACA;MACJ;IACF;AAEA,WAAO;EACT;EAEA,aAAa,eAAqC;AAChD,UAAM,aAAa,mBAAK;AAIxB,UAAM,aAAa,KAAK,aAAa,mBAAK,gBAAe,KAAK,OAAO;AAErE,uBAAK,qBAAsB,mBAAK,eAAc;AAC9C,uBAAK,uBAAwB,KAAK;AAElC,QAAI,mBAAK,qBAAoB,SAAS,QAAW;AAC/C,yBAAK,2BAA4B,mBAAK;IACxC;AAGA,QAAI,oBAAoB,YAAY,UAAU,GAAG;AAC/C;IACF;AAEA,uBAAK,gBAAiB;AAGtB,UAAM,uBAAsC,CAAC;AAE7C,UAAM,wBAAwB,MAAe;AAC3C,UAAI,CAAC,YAAY;AACf,eAAO;MACT;AAEA,YAAM,EAAE,oBAAoB,IAAI,KAAK;AACrC,YAAM,2BACJ,OAAO,wBAAwB,aAC3B,oBAAoB,IACpB;AAEN,UACE,6BAA6B,SAC5B,CAAC,4BAA4B,CAAC,mBAAK,eAAc,MAClD;AACA,eAAO;MACT;AAEA,YAAM,gBAAgB,IAAI;QACxB,4BAA4B,mBAAK;MACnC;AAEA,UAAI,KAAK,QAAQ,cAAc;AAC7B,sBAAc,IAAI,OAAO;MAC3B;AAEA,aAAO,OAAO,KAAK,mBAAK,eAAc,EAAE,KAAK,CAAC,QAAQ;AACpD,cAAM,WAAW;AACjB,cAAM,UAAU,mBAAK,gBAAe,QAAQ,MAAM,WAAW,QAAQ;AAErE,eAAO,WAAW,cAAc,IAAI,QAAQ;MAC9C,CAAC;IACH;AAEA,SAAI,+CAAe,eAAc,SAAS,sBAAsB,GAAG;AACjE,2BAAqB,YAAY;IACnC;AAEA,0BAAK,qCAAL,WAAa,EAAE,GAAG,sBAAsB,GAAG,cAAc;EAC3D;EAqBA,gBAAsB;AACpB,SAAK,aAAa;AAElB,QAAI,KAAK,aAAa,GAAG;AACvB,4BAAK,2CAAL;IACF;EACF;AAkBF,GArsBE,yBACA,+BACA,2CACA,gCACA,qCACA,uCAOA,kCACA,8BACA,2BACA,+BAGA,2CACA,iCACA,oCACA,yCACA,+BA7BK,0CAwSL,kBAAA,SACE,cACiC;AAEjC,wBAAK,0CAAL;AAGA,MAAI,UAA2C,mBAAK,eAAc;IAChE,KAAK;IACL;EACF;AAEA,MAAI,EAAC,6CAAc,eAAc;AAC/B,cAAU,QAAQ,MAAM,IAAI;EAC9B;AAEA,SAAO;AACT,GAEA,wBAAA,WAA4B;AAC1B,wBAAK,gDAAL;AACA,QAAM,YAAY;IAChB,KAAK,QAAQ;IACb,mBAAK;EACP;AAEA,MAAI,YAAY,mBAAK,gBAAe,WAAW,CAAC,eAAe,SAAS,GAAG;AACzE;EACF;AAEA,QAAM,OAAO,eAAe,mBAAK,gBAAe,eAAe,SAAS;AAIxE,QAAM,UAAU,OAAO;AAEvB,qBAAK,iBAAkB,WAAW,MAAM;AACtC,QAAI,CAAC,mBAAK,gBAAe,SAAS;AAChC,WAAK,aAAa;IACpB;EACF,GAAG,OAAO;AACZ,GAEA,4BAAA,WAA0B;AACxB,UACG,OAAO,KAAK,QAAQ,oBAAoB,aACrC,KAAK,QAAQ,gBAAgB,mBAAK,cAAa,IAC/C,KAAK,QAAQ,oBAAoB;AAEzC,GAEA,2BAAA,SAAuB,cAAoC;AACzD,wBAAK,mDAAL;AAEA,qBAAK,yBAA0B;AAE/B,MACE,YACA,eAAe,KAAK,QAAQ,SAAS,mBAAK,cAAa,MAAM,SAC7D,CAAC,eAAe,mBAAK,wBAAuB,KAC5C,mBAAK,6BAA4B,GACjC;AACA;EACF;AAEA,qBAAK,oBAAqB,YAAY,MAAM;AAC1C,QACE,KAAK,QAAQ,+BACb,aAAa,UAAU,GACvB;AACA,4BAAK,2CAAL;IACF;EACF,GAAG,mBAAK,wBAAuB;AACjC,GAEA,kBAAA,WAAsB;AACpB,wBAAK,iDAAL;AACA,wBAAK,oDAAL,WAA4B,sBAAK,qDAAL;AAC9B,GAEA,uBAAA,WAA2B;AACzB,MAAI,mBAAK,kBAAiB;AACxB,iBAAa,mBAAK,gBAAe;AACjC,uBAAK,iBAAkB;EACzB;AACF,GAEA,0BAAA,WAA8B;AAC5B,MAAI,mBAAK,qBAAoB;AAC3B,kBAAc,mBAAK,mBAAkB;AACrC,uBAAK,oBAAqB;EAC5B;AACF,GA6RA,iBAAA,WAAqB;AACnB,QAAM,QAAQ,mBAAK,SAAQ,cAAc,EAAE,MAAM,mBAAK,UAAS,KAAK,OAAO;AAE3E,MAAI,UAAU,mBAAK,gBAAe;AAChC;EACF;AAEA,QAAM,YAAY,mBAAK;AAGvB,qBAAK,eAAgB;AACrB,qBAAK,2BAA4B,MAAM;AAEvC,MAAI,KAAK,aAAa,GAAG;AACvB,2CAAW,eAAe;AAC1B,UAAM,YAAY,IAAI;EACxB;AACF,GAUA,YAAA,SAAQ,eAAoC;AAC1C,gBAAc,MAAM,MAAM;AAExB,QAAI,cAAc,WAAW;AAC3B,WAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,iBAAS,mBAAK,eAAc;MAC9B,CAAC;IACH;AAGA,uBAAK,SAAQ,cAAc,EAAE,OAAO;MAClC,OAAO,mBAAK;MACZ,MAAM;IACR,CAAC;EACH,CAAC;AACH,GA3sBKA;AA8sBP,SAAS,kBACP,OACA,SACS;AACT,SACE,eAAe,QAAQ,SAAS,KAAK,MAAM,SAC3C,MAAM,MAAM,SAAS,UACrB,EAAE,MAAM,MAAM,WAAW,WAAW,QAAQ,iBAAiB;AAEjE;AAEA,SAAS,mBACP,OACA,SACS;AACT,SACE,kBAAkB,OAAO,OAAO,KAC/B,MAAM,MAAM,SAAS,UACpB,cAAc,OAAO,SAAS,QAAQ,cAAc;AAE1D;AAEA,SAAS,cACP,OACA,SACA,OAGA;AACA,MAAI,eAAe,QAAQ,SAAS,KAAK,MAAM,OAAO;AACpD,UAAM,QAAQ,OAAO,UAAU,aAAa,MAAM,KAAK,IAAI;AAE3D,WAAO,UAAU,YAAa,UAAU,SAAS,QAAQ,OAAO,OAAO;EACzE;AACA,SAAO;AACT;AAEA,SAAS,sBACP,OACA,WACA,SACA,aACS;AACT,UACG,UAAU,aACT,eAAe,YAAY,SAAS,KAAK,MAAM,WAChD,CAAC,QAAQ,YAAY,MAAM,MAAM,WAAW,YAC7C,QAAQ,OAAO,OAAO;AAE1B;AAEA,SAAS,QACP,OACA,SACS;AACT,SACE,eAAe,QAAQ,SAAS,KAAK,MAAM,SAC3C,MAAM,cAAc,iBAAiB,QAAQ,WAAW,KAAK,CAAC;AAElE;AAIA,SAAS,sCAOP,UACA,kBACA;AAGA,MAAI,CAAC,oBAAoB,SAAS,iBAAiB,GAAG,gBAAgB,GAAG;AACvE,WAAO;EACT;AAGA,SAAO;AACT;;;AC7zBA,SAAS,WAAc,QAAkB,QAA4B;AACnE,SAAO,OAAO,OAAO,CAAC,MAAM,CAAC,OAAO,SAAS,CAAC,CAAC;AACjD;AAEA,SAAS,UAAa,OAAiB,OAAe,OAAoB;AACxE,QAAM,OAAO,MAAM,MAAM,CAAC;AAC1B,OAAK,KAAK,IAAI;AACd,SAAO;AACT;;AAcO,IAAM,mBAANC,OAAA,cAEG,aAAsC;EAU9C,YACE,QACA,SACA,SACA;AACA,UAAM;AAjBH;AAGL,uBAAAC;AACA;AACA,uBAAAC;AACA;AACA,uBAAAC;AACA;AACA;AACA;AASE,uBAAKF,UAAU;AACf,uBAAK,UAAW;AAChB,uBAAKC,WAAW,CAAC;AACjB,uBAAKC,aAAa,CAAC;AACnB,uBAAK,SAAU,CAAC;AAEhB,SAAK,WAAW,OAAO;EACzB;EAEU,cAAoB;AAC5B,QAAI,KAAK,UAAU,SAAS,GAAG;AAC7B,yBAAKA,aAAW,QAAQ,CAAC,aAAa;AACpC,iBAAS,UAAU,CAAC,WAAW;AAC7B,gCAAK,yCAAL,WAAe,UAAU;QAC3B,CAAC;MACH,CAAC;IACH;EACF;EAEU,gBAAsB;AAC9B,QAAI,CAAC,KAAK,UAAU,MAAM;AACxB,WAAK,QAAQ;IACf;EACF;EAEA,UAAgB;AACd,SAAK,YAAY,oBAAI,IAAI;AACzB,uBAAKA,aAAW,QAAQ,CAAC,aAAa;AACpC,eAAS,QAAQ;IACnB,CAAC;EACH;EAEA,WACE,SACA,SACA,eACM;AACN,uBAAKD,WAAW;AAChB,uBAAK,UAAW;AAEhB,QAAI,MAAuC;AACzC,YAAM,cAAc,QAAQ;QAC1B,CAAC,UAAU,mBAAKD,UAAQ,oBAAoB,KAAK,EAAE;MACrD;AACA,UAAI,IAAI,IAAI,WAAW,EAAE,SAAS,YAAY,QAAQ;AACpD,gBAAQ;UACN;QACF;MACF;IACF;AAEA,kBAAc,MAAM,MAAM;AACxB,YAAM,gBAAgB,mBAAKE;AAE3B,YAAM,qBAAqB,sBAAK,sDAAL,WAA4B,mBAAKD;AAG5D,yBAAmB;QAAQ,CAAC,UAC1B,MAAM,SAAS,WAAW,MAAM,uBAAuB,aAAa;MACtE;AAEA,YAAM,eAAe,mBAAmB,IAAI,CAAC,UAAU,MAAM,QAAQ;AACrE,YAAM,YAAY,aAAa;QAAI,CAAC,aAClC,SAAS,iBAAiB;MAC5B;AAEA,YAAM,iBAAiB,aAAa;QAClC,CAAC,UAAU,UAAU,aAAa,cAAc,KAAK;MACvD;AAEA,UAAI,cAAc,WAAW,aAAa,UAAU,CAAC,gBAAgB;AACnE;MACF;AAEA,yBAAKC,aAAa;AAClB,yBAAK,SAAU;AAEf,UAAI,CAAC,KAAK,aAAa,GAAG;AACxB;MACF;AAEA,iBAAW,eAAe,YAAY,EAAE,QAAQ,CAAC,aAAa;AAC5D,iBAAS,QAAQ;MACnB,CAAC;AAED,iBAAW,cAAc,aAAa,EAAE,QAAQ,CAAC,aAAa;AAC5D,iBAAS,UAAU,CAAC,WAAW;AAC7B,gCAAK,yCAAL,WAAe,UAAU;QAC3B,CAAC;MACH,CAAC;AAED,4BAAK,4BAAAC,YAAL;IACF,CAAC;EACH;EAEA,mBAA+C;AAC7C,WAAO,mBAAK;EACd;EAEA,aAAa;AACX,WAAO,mBAAKD,aAAW,IAAI,CAAC,aAAa,SAAS,gBAAgB,CAAC;EACrE;EAEA,eAAe;AACb,WAAO,mBAAKA;EACd;EAEA,oBACE,SACA,SAKA;AACA,UAAM,UAAU,sBAAK,sDAAL,WAA4B;AAC5C,UAAM,SAAS,QAAQ;MAAI,CAAC,UAC1B,MAAM,SAAS,oBAAoB,MAAM,qBAAqB;IAChE;AAEA,WAAO;MACL;MACA,CAAC,MAAmC;AAClC,eAAO,sBAAK,8CAAL,WAAoB,KAAK,QAAQ;MAC1C;MACA,MAAM;AACJ,eAAO,sBAAK,4CAAL,WAAkB,QAAQ;MACnC;IACF;EACF;AAiGF,GAlPEF,WAAA,eACA,yBACAC,YAAA,eACA,0BACAC,cAAA,eACA,iCACA,8BACA,6BAVK,4CAsJL,iBAAA,SACE,QACA,SACA;AACA,QAAM,UAAU,sBAAK,sDAAL,WAA4B;AAE5C,SAAO,QAAQ,IAAI,CAAC,OAAO,UAAU;AACnC,UAAM,iBAAiB,OAAO,KAAK;AACnC,WAAO,CAAC,MAAM,sBAAsB,sBAChC,MAAM,SAAS,YAAY,gBAAgB,CAAC,iBAAiB;AAE3D,cAAQ,QAAQ,CAAC,MAAM;AACrB,UAAE,SAAS,UAAU,YAAY;MACnC,CAAC;IACH,CAAC,IACD;EACN,CAAC;AACH,GAEA,mBAAA,SACE,OACA,SACiB;AACjB,MAAI,SAAS;AACX,QACE,CAAC,mBAAK,oBACN,mBAAK,aAAY,mBAAK,gBACtB,YAAY,mBAAK,eACjB;AACA,yBAAK,cAAe;AACpB,yBAAK,aAAc,mBAAK;AACxB,yBAAK,iBAAkB;QACrB,mBAAK;QACL,QAAQ,KAAK;MACf;IACF;AAEA,WAAO,mBAAK;EACd;AACA,SAAO;AACT,GAEA,2BAAA,SACE,SAC2B;AAC3B,QAAM,mBAAmB,IAAI;IAC3B,mBAAKA,aAAW,IAAI,CAAC,aAAa,CAAC,SAAS,QAAQ,WAAW,QAAQ,CAAC;EAC1E;AAEA,QAAM,YAAuC,CAAC;AAE9C,UAAQ,QAAQ,CAAC,YAAY;AAC3B,UAAM,mBAAmB,mBAAKF,UAAQ,oBAAoB,OAAO;AACjE,UAAM,QAAQ,iBAAiB,IAAI,iBAAiB,SAAS;AAC7D,QAAI,OAAO;AACT,gBAAU,KAAK;QACb,uBAAuB;QACvB,UAAU;MACZ,CAAC;IACH,OAAO;AACL,gBAAU,KAAK;QACb,uBAAuB;QACvB,UAAU,IAAI,cAAc,mBAAKA,WAAS,gBAAgB;MAC5D,CAAC;IACH;EACF,CAAC;AAED,SAAO;AACT,GAEA,cAAA,SAAU,UAAyB,QAAmC;AACpE,QAAM,QAAQ,mBAAKE,aAAW,QAAQ,QAAQ;AAC9C,MAAI,UAAU,IAAI;AAChB,uBAAK,SAAU,UAAU,mBAAK,UAAS,OAAO,MAAM;AACpD,0BAAK,4BAAAC,YAAL;EACF;AACF,GAEAA,aAAA,WAAgB;;AACd,MAAI,KAAK,aAAa,GAAG;AACvB,UAAM,iBAAiB,mBAAK;AAC5B,UAAM,YAAY,sBAAK,8CAAL,WAChB,sBAAK,4CAAL,WAAkB,mBAAK,UAAS,mBAAKF,cACrCF,OAAA,mBAAK,cAAL,gBAAAA,KAAe;AAGjB,QAAI,mBAAmB,WAAW;AAChC,oBAAc,MAAM,MAAM;AACxB,aAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,mBAAS,mBAAK,QAAO;QACvB,CAAC;MACH,CAAC;IACH;EACF;AACF,GApPKA;;;ACRA,IAAM,wBAAN,cAOG,cAMR;EA8BA,YACE,QACA,SAQA;AACA,UAAM,QAAQ,OAAO;EACvB;EAEU,cAAoB;AAC5B,UAAM,YAAY;AAClB,SAAK,gBAAgB,KAAK,cAAc,KAAK,IAAI;AACjD,SAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;EAC3D;EAEA,WACE,SAQA,eACM;AACN,UAAM;MACJ;QACE,GAAG;QACH,UAAU,sBAAsB;MAClC;MACA;IACF;EACF;EAEA,oBACE,SAQ4C;AAC5C,YAAQ,WAAW,sBAAsB;AACzC,WAAO,MAAM,oBAAoB,OAAO;EAI1C;EAEA,cACE,SACqD;AACrD,WAAO,KAAK,MAAM;MAChB,GAAG;MACH,MAAM;QACJ,WAAW,EAAE,WAAW,UAAU;MACpC;IACF,CAAC;EACH;EAEA,kBACE,SACqD;AACrD,WAAO,KAAK,MAAM;MAChB,GAAG;MACH,MAAM;QACJ,WAAW,EAAE,WAAW,WAAW;MACrC;IACF,CAAC;EACH;EAEU,aACR,OAMA,SAQ4C;;AAC5C,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,eAAe,MAAM,aAAa,OAAO,OAAO;AAEtD,UAAM,EAAE,YAAY,cAAc,SAAS,eAAe,IAAI;AAC9D,UAAM,kBAAiB,MAAAK,OAAA,MAAM,cAAN,gBAAAA,KAAiB,cAAjB,mBAA4B;AAEnD,UAAM,uBAAuB,WAAW,mBAAmB;AAC3D,UAAM,qBAAqB,cAAc,mBAAmB;AAE5D,UAAM,2BAA2B,WAAW,mBAAmB;AAC/D,UAAM,yBAAyB,cAAc,mBAAmB;AAEhE,UAAM,SAAyD;MAC7D,GAAG;MACH,eAAe,KAAK;MACpB,mBAAmB,KAAK;MACxB,aAAa,YAAY,SAAS,MAAM,IAAI;MAC5C,iBAAiB,gBAAgB,SAAS,MAAM,IAAI;MACpD;MACA;MACA;MACA;MACA,gBACE,kBAAkB,CAAC,wBAAwB,CAAC;MAC9C,cACE,gBAAgB,CAAC,sBAAsB,CAAC;IAC5C;AAEA,WAAO;EACT;AACF;;;;AC7KO,IAAM,oBAANC,OAAA,cAKG,aAER;EASA,YACE,QACA,SACA;AACA,UAAM;AApBH;AAUL,uBAAAC;AACA,uBAAAC;AAEA;AACA;AAQE,uBAAKD,UAAU;AACf,SAAK,WAAW,OAAO;AACvB,SAAK,YAAY;AACjB,0BAAK,8CAAL;EACF;EAEU,cAAoB;AAC5B,SAAK,SAAS,KAAK,OAAO,KAAK,IAAI;AACnC,SAAK,QAAQ,KAAK,MAAM,KAAK,IAAI;EACnC;EAEA,WACE,SACA;;AACA,UAAM,cAAc,KAAK;AAGzB,SAAK,UAAU,mBAAKA,UAAQ,uBAAuB,OAAO;AAC1D,QAAI,CAAC,oBAAoB,KAAK,SAAS,WAAW,GAAG;AACnD,yBAAKA,UAAQ,iBAAiB,EAAE,OAAO;QACrC,MAAM;QACN,UAAU,mBAAK;QACf,UAAU;MACZ,CAAC;IACH;AAEA,SACE,2CAAa,gBACb,KAAK,QAAQ,eACb,QAAQ,YAAY,WAAW,MAAM,QAAQ,KAAK,QAAQ,WAAW,GACrE;AACA,WAAK,MAAM;IACb,aAAWD,OAAA,mBAAK,sBAAL,gBAAAA,KAAuB,MAAM,YAAW,WAAW;AAC5D,yBAAK,kBAAiB,WAAW,KAAK,OAAO;IAC/C;EACF;EAEU,gBAAsB;;AAC9B,QAAI,CAAC,KAAK,aAAa,GAAG;AACxB,OAAAA,OAAA,mBAAK,sBAAL,gBAAAA,KAAuB,eAAe;IACxC;EACF;EAEA,iBAAiB,QAA2D;AAC1E,0BAAK,8CAAL;AAEA,0BAAK,6BAAAG,YAAL,WAAa;EACf;EAEA,mBAKE;AACA,WAAO,mBAAKD;EACd;EAEA,QAAc;;AAGZ,KAAAF,OAAA,mBAAK,sBAAL,gBAAAA,KAAuB,eAAe;AACtC,uBAAK,kBAAmB;AACxB,0BAAK,8CAAL;AACA,0BAAK,6BAAAG,YAAL;EACF;EAEA,OACE,WACA,SACgB;;AAChB,uBAAK,gBAAiB;AAEtB,KAAAH,OAAA,mBAAK,sBAAL,gBAAAA,KAAuB,eAAe;AAEtC,uBAAK,kBAAmB,mBAAKC,UAC1B,iBAAiB,EACjB,MAAM,mBAAKA,WAAS,KAAK,OAAO;AAEnC,uBAAK,kBAAiB,YAAY,IAAI;AAEtC,WAAO,mBAAK,kBAAiB,QAAQ,SAAS;EAChD;AA6CF,GA3IEA,WAAA,eACAC,kBAAA,eAEA,kCACA,gCAdK,6CA0GL,kBAAA,WAAsB;;AACpB,QAAM,UACJF,OAAA,mBAAK,sBAAL,gBAAAA,KAAuB,UACvBI,iBAAqD;AAEvD,qBAAKF,iBAAiB;IACpB,GAAG;IACH,WAAW,MAAM,WAAW;IAC5B,WAAW,MAAM,WAAW;IAC5B,SAAS,MAAM,WAAW;IAC1B,QAAQ,MAAM,WAAW;IACzB,QAAQ,KAAK;IACb,OAAO,KAAK;EACd;AACF,GAEAC,aAAA,SAAQ,QAA4D;AAClE,gBAAc,MAAM,MAAM;;AAExB,QAAI,mBAAK,mBAAkB,KAAK,aAAa,GAAG;AAC9C,YAAM,YAAY,mBAAKD,iBAAe;AACtC,YAAM,UAAU,mBAAKA,iBAAe;AAEpC,WAAI,iCAAQ,UAAS,WAAW;AAC9B,eAAAF,OAAA,mBAAK,iBAAe,cAApB,wBAAAA,MAAgC,OAAO,MAAM,WAAW;AACxD,uCAAK,iBAAe,cAApB,4BAAgC,OAAO,MAAM,MAAM,WAAW;MAChE,YAAW,iCAAQ,UAAS,SAAS;AACnC,uCAAK,iBAAe,YAApB,4BAA8B,OAAO,OAAO,WAAW;AACvD,uCAAK,iBAAe,cAApB;;UACE;UACA,OAAO;UACP;UACA;;MAEJ;IACF;AAGA,SAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,eAAS,mBAAKE,gBAAc;IAC9B,CAAC;EACH,CAAC;AACH,GApJKF;;;ACLP,SAAS,qBAAqB,MAAgB;AAC5C,SAAO;AACT;AAsCA,SAAS,kBAAkB,UAAwC;AACjE,SAAO;IACL,aAAa,SAAS,QAAQ;IAC9B,OAAO,SAAS;IAChB,GAAI,SAAS,QAAQ,SAAS,EAAE,OAAO,SAAS,QAAQ,MAAM;IAC9D,GAAI,SAAS,QAAQ,EAAE,MAAM,SAAS,KAAK;EAC7C;AACF;AAMA,SAAS,eACP,OACA,eACiB;;AACjB,SAAO;IACL,OAAO;MACL,GAAG,MAAM;MACT,GAAI,MAAM,MAAM,SAAS,UAAa;QACpC,MAAM,cAAc,MAAM,MAAM,IAAI;MACtC;IACF;IACA,UAAU,MAAM;IAChB,WAAW,MAAM;IACjB,GAAI,MAAM,MAAM,WAAW,aAAa;MACtC,UAASK,OAAA,MAAM,YAAN,gBAAAA,KAAe,KAAK,eAAe,MAAM,CAAC,UAAU;AAC3D,YAAI,MAAuC;AACzC,kBAAQ;YACN,+DAA+D,MAAM,SAAS,MAAM,KAAK;UAC3F;QACF;AACA,eAAO,QAAQ,OAAO,IAAI,MAAM,UAAU,CAAC;MAC7C;IACF;IACA,GAAI,MAAM,QAAQ,EAAE,MAAM,MAAM,KAAK;EACvC;AACF;AAEO,SAAS,+BAA+B,UAAoB;AACjE,SAAO,SAAS,MAAM;AACxB;AAEO,SAAS,4BAA4B,OAAc;AACxD,SAAO,MAAM,MAAM,WAAW;AAChC;AAEO,SAAS,UACd,QACA,UAA4B,CAAC,GACZ;;AACjB,QAAM,iBACJ,QAAQ,6BACRA,OAAA,OAAO,kBAAkB,EAAE,cAA3B,gBAAAA,KAAsC,4BACtC;AAEF,QAAM,YAAY,OACf,iBAAiB,EACjB,OAAO,EACP;IAAQ,CAAC,aACR,eAAe,QAAQ,IAAI,CAAC,kBAAkB,QAAQ,CAAC,IAAI,CAAC;EAC9D;AAEF,QAAM,cACJ,QAAQ,0BACR,YAAO,kBAAkB,EAAE,cAA3B,mBAAsC,yBACtC;AAEF,QAAM,gBACJ,QAAQ,mBACR,YAAO,kBAAkB,EAAE,cAA3B,mBAAsC,kBACtC;AAEF,QAAM,UAAU,OACb,cAAc,EACd,OAAO,EACP;IAAQ,CAAC,UACR,YAAY,KAAK,IAAI,CAAC,eAAe,OAAO,aAAa,CAAC,IAAI,CAAC;EACjE;AAEF,SAAO,EAAE,WAAW,QAAQ;AAC9B;AAEO,SAAS,QACd,QACA,iBACA,SACM;;AACN,MAAI,OAAO,oBAAoB,YAAY,oBAAoB,MAAM;AACnE;EACF;AAEA,QAAM,gBAAgB,OAAO,iBAAiB;AAC9C,QAAM,aAAa,OAAO,cAAc;AACxC,QAAM,oBACJA,OAAA,mCAAS,mBAAT,gBAAAA,KAAyB,sBACzB,YAAO,kBAAkB,EAAE,YAA3B,mBAAoC,oBACpC;AAGF,QAAM,YAAa,gBAAoC,aAAa,CAAC;AAErE,QAAM,UAAW,gBAAoC,WAAW,CAAC;AAEjE,YAAU,QAAQ,CAAC,EAAE,OAAO,GAAG,gBAAgB,MAAM;;AACnD,kBAAc;MACZ;MACA;QACE,IAAGA,OAAA,OAAO,kBAAkB,EAAE,YAA3B,gBAAAA,KAAoC;QACvC,IAAGC,MAAA,mCAAS,mBAAT,gBAAAA,IAAyB;QAC5B,GAAG;MACL;MACA;IACF;EACF,CAAC;AAED,UAAQ,QAAQ,CAAC,EAAE,UAAU,OAAO,WAAW,MAAM,QAAQ,MAAM;;AACjE,QAAI,QAAQ,WAAW,IAAI,SAAS;AAEpC,UAAM,OACJ,MAAM,SAAS,SAAY,MAAM,OAAO,gBAAgB,MAAM,IAAI;AAGpE,QAAI,OAAO;AACT,UAAI,MAAM,MAAM,gBAAgB,MAAM,eAAe;AAGnD,cAAM,EAAE,aAAa,UAAU,GAAG,gBAAgB,IAAI;AACtD,cAAM,SAAS;UACb,GAAG;UACH;QACF,CAAC;MACH;IACF,OAAO;AAEL,cAAQ,WAAW;QACjB;QACA;UACE,IAAGD,OAAA,OAAO,kBAAkB,EAAE,YAA3B,gBAAAA,KAAoC;UACvC,IAAGC,MAAA,mCAAS,mBAAT,gBAAAA,IAAyB;UAC5B;UACA;UACA;QACF;;;QAGA;UACE,GAAG;UACH;UACA,aAAa;QACf;MACF;IACF;AAEA,QAAI,SAAS;AAGX,YAAM,iBAAiB,QAAQ,QAAQ,OAAO,EAAE,KAAK,eAAe;AAIpE,WAAK,MAAM,MAAM,QAAW,EAAE,eAAe,CAAC;IAChD;EACF,CAAC;AACH;;;ACjLO,IAAM,gBAAgB,OAAO,eAAe;AAE5C,IAAM,qBAAqB,OAAO,oBAAoB;AAEtD,IAAM,cAAc,OAAO,aAAa;;;AC/C/C,IAAAC,SAAuB;;;ACAvB,YAAuB;AAuCnB,yBAAA;AAnCG,IAAM,qBAA2B;EACtC;AACF;AAEO,IAAM,iBAAiB,CAAC,gBAA8B;AAC3D,QAAM,SAAe,iBAAW,kBAAkB;AAElD,MAAI,aAAa;AACf,WAAO;EACT;AAEA,MAAI,CAAC,QAAQ;AACX,UAAM,IAAI,MAAM,wDAAwD;EAC1E;AAEA,SAAO;AACT;AAOO,IAAM,sBAAsB,CAAC;EAClC;EACA;AACF,MAAmD;AAC3C,EAAA,gBAAU,MAAM;AACpB,WAAO,MAAM;AACb,WAAO,MAAM;AACX,aAAO,QAAQ;IACjB;EACF,GAAG,CAAC,MAAM,CAAC;AAEX,aACE,wBAAC,mBAAmB,UAAnB,EAA4B,OAAO,QACjC,SAAA,CACH;AAEJ;;;AC3CA,IAAAC,SAAuB;AAEvB,IAAM,qBAA2B,qBAAc,KAAK;AAE7C,IAAM,iBAAiB,MAAY,kBAAW,kBAAkB;AAChE,IAAM,sBAAsB,mBAAmB;;;ACLtD,IAAAC,SAAuB;AAkDnB,IAAAC,sBAAA;AArCJ,SAAS,cAA4C;AACnD,MAAI,UAAU;AACd,SAAO;IACL,YAAY,MAAM;AAChB,gBAAU;IACZ;IACA,OAAO,MAAM;AACX,gBAAU;IACZ;IACA,SAAS,MAAM;AACb,aAAO;IACT;EACF;AACF;AAEA,IAAM,iCAAuC,qBAAc,YAAY,CAAC;AAIjE,IAAM,6BAA6B,MAClC,kBAAW,8BAA8B;AAY1C,IAAM,0BAA0B,CAAC;EACtC;AACF,MAAoC;AAClC,QAAM,CAAC,KAAK,IAAU,gBAAS,MAAM,YAAY,CAAC;AAClD,aACE,yBAAC,+BAA+B,UAA/B,EAAwC,OACtC,UAAA,OAAO,aAAa,aAAa,SAAS,KAAK,IAAI,SAAA,CACtD;AAEJ;;;ACtDA,IAAAC,SAAuB;;;ACDhB,SAAS,iBACd,YACA,QACS;AAET,MAAI,OAAO,eAAe,YAAY;AACpC,WAAO,WAAW,GAAG,MAAM;EAC7B;AAEA,SAAO,CAAC,CAAC;AACX;AAEO,SAASC,QAAa;AAAC;;;ADAvB,IAAM,kCAAkC,CAO7C,SAOA,uBACG;AACH,MACE,QAAQ,YACR,QAAQ,gBACR,QAAQ,+BACR;AAEA,QAAI,CAAC,mBAAmB,QAAQ,GAAG;AACjC,cAAQ,eAAe;IACzB;EACF;AACF;AAEO,IAAM,6BAA6B,CACxC,uBACG;AACG,EAAA,iBAAU,MAAM;AACpB,uBAAmB,WAAW;EAChC,GAAG,CAAC,kBAAkB,CAAC;AACzB;AAEO,IAAM,cAAc,CAMzB;EACA;EACA;EACA;EACA;AACF,MAKM;AACJ,SACE,OAAO,WACP,CAAC,mBAAmB,QAAQ,KAC5B,CAAC,OAAO,cACR,SACA,iBAAiB,cAAc,CAAC,OAAO,OAAO,KAAK,CAAC;AAExD;;;AE9DO,IAAM,sBAAsB,CAMjC,QACA,UACG,MAAM,MAAM,SAAS;AAEnB,IAAM,uBAAuB,CAClC,qBACG;AACH,QAAM,oBAAoB,iBAAiB;AAE3C,MAAI,iBAAiB,UAAU;AAG7B,qBAAiB,YACf,OAAO,sBAAsB,aACzB,IAAI,SAAS,KAAK,IAAI,kBAAkB,GAAG,IAAI,GAAG,GAAI,IACtD,KAAK,IAAI,qBAAqB,KAAM,GAAI;AAE9C,QAAI,OAAO,iBAAiB,WAAW,UAAU;AAC/C,uBAAiB,SAAS,KAAK,IAAI,iBAAiB,QAAQ,GAAI;IAClE;EACF;AACF;AAEO,IAAM,YAAY,CACvB,QACA,gBACG,OAAO,aAAa,OAAO,cAAc,CAAC;AAExC,IAAM,gBAAgB,CAC3B,kBAGA,YACG,qDAAkB,aAAY,OAAO;AAEnC,IAAM,kBAAkB,CAO7B,kBAOA,UACA,uBAEA,SAAS,gBAAgB,gBAAgB,EAAE,MAAM,MAAM;AACrD,qBAAmB,WAAW;AAChC,CAAC;;;ANyJI,SAAS,WAId;EACE;EACA,GAAG;AACL,GAKA,aACiB;AACjB,QAAM,SAAS,eAAe,WAAW;AACzC,QAAM,cAAc,eAAe;AACnC,QAAM,qBAAqB,2BAA2B;AAEtD,QAAM,mBAAyB;IAC7B,MACE,QAAQ,IAAI,CAAC,SAAS;AACpB,YAAM,mBAAmB,OAAO;QAC9B;MACF;AAGA,uBAAiB,qBAAqB,cAClC,gBACA;AAEJ,aAAO;IACT,CAAC;IACH,CAAC,SAAS,QAAQ,WAAW;EAC/B;AAEA,mBAAiB,QAAQ,CAAC,UAAU;AAClC,yBAAqB,KAAK;AAC1B,oCAAgC,OAAO,kBAAkB;EAC3D,CAAC;AAED,6BAA2B,kBAAkB;AAE7C,QAAM,CAAC,QAAQ,IAAU;IACvB,MACE,IAAI;MACF;MACA;MACA;IACF;EACJ;AAGA,QAAM,CAAC,kBAAkB,mBAAmB,WAAW,IACrD,SAAS;IACP;IACC,QAAoD;EACvD;AAEF,QAAM,kBAAkB,CAAC,eAAe,QAAQ,eAAe;AACzD,EAAA;IACE;MACJ,CAAC,kBACC,kBACI,SAAS,UAAU,cAAc,WAAW,aAAa,CAAC,IAC1DC;MACN,CAAC,UAAU,eAAe;IAC5B;IACA,MAAM,SAAS,iBAAiB;IAChC,MAAM,SAAS,iBAAiB;EAClC;AAEM,EAAA,iBAAU,MAAM;AAGpB,aAAS;MACP;MACA;MACA;QACE,WAAW;MACb;IACF;EACF,GAAG,CAAC,kBAAkB,SAAS,QAAQ,CAAC;AAExC,QAAM,0BAA0B,iBAAiB;IAAK,CAAC,QAAQ,UAC7D,cAAc,iBAAiB,KAAK,GAAG,MAAM;EAC/C;AAEA,QAAM,mBAAmB,0BACrB,iBAAiB,QAAQ,CAAC,QAAQ,UAAU;AAC1C,UAAM,OAAO,iBAAiB,KAAK;AAEnC,QAAI,MAAM;AACR,YAAM,gBAAgB,IAAI,cAAc,QAAQ,IAAI;AACpD,UAAI,cAAc,MAAM,MAAM,GAAG;AAC/B,eAAO,gBAAgB,MAAM,eAAe,kBAAkB;MAChE,WAAW,UAAU,QAAQ,WAAW,GAAG;AACzC,aAAK,gBAAgB,MAAM,eAAe,kBAAkB;MAC9D;IACF;AACA,WAAO,CAAC;EACV,CAAC,IACD,CAAC;AAEL,MAAI,iBAAiB,SAAS,GAAG;AAC/B,UAAM,QAAQ,IAAI,gBAAgB;EACpC;AACA,QAAM,oCAAoC,iBAAiB;IACzD,CAAC,QAAQ,UAAU;AACjB,YAAM,QAAQ,iBAAiB,KAAK;AACpC,aACE,SACA,YAAY;QACV;QACA;QACA,cAAc,MAAM;QACpB,OAAO,OAAO,cAAc,EAAE,IAAI,MAAM,SAAS;MACnD,CAAC;IAEL;EACF;AAEA,MAAI,uFAAmC,OAAO;AAC5C,UAAM,kCAAkC;EAC1C;AAEA,SAAO,kBAAkB,YAAY,CAAC;AACxC;;;AO5VA,IAAAC,SAAuB;AA0BhB,SAAS,aAOd,SAOA,UACA,aACoC;;AACpC,MAAI,MAAuC;AACzC,QAAI,OAAO,YAAY,YAAY,MAAM,QAAQ,OAAO,GAAG;AACzD,YAAM,IAAI;QACR;MACF;IACF;EACF;AAEA,QAAM,SAAS,eAAe,WAAW;AACzC,QAAM,cAAc,eAAe;AACnC,QAAM,qBAAqB,2BAA2B;AACtD,QAAM,mBAAmB,OAAO,oBAAoB,OAAO;AAEzD,SAAAC,OAAA,OAAO,kBAAkB,EAAE,YAA3B,gBAAAA,KAA4C,8BAA5C;IAAAA;IACA;;AAIF,mBAAiB,qBAAqB,cAClC,gBACA;AAEJ,uBAAqB,gBAAgB;AACrC,kCAAgC,kBAAkB,kBAAkB;AAEpE,6BAA2B,kBAAkB;AAG7C,QAAM,kBAAkB,CAAC,OACtB,cAAc,EACd,IAAI,iBAAiB,SAAS;AAEjC,QAAM,CAAC,QAAQ,IAAU;IACvB,MACE,IAAI;MACF;MACA;IACF;EACJ;AAGA,QAAM,SAAS,SAAS,oBAAoB,gBAAgB;AAE5D,QAAM,kBAAkB,CAAC,eAAe,QAAQ,eAAe;AACzD,EAAA;IACE;MACJ,CAAC,kBAAkB;AACjB,cAAM,cAAc,kBAChB,SAAS,UAAU,cAAc,WAAW,aAAa,CAAC,IAC1DC;AAIJ,iBAAS,aAAa;AAEtB,eAAO;MACT;MACA,CAAC,UAAU,eAAe;IAC5B;IACA,MAAM,SAAS,iBAAiB;IAChC,MAAM,SAAS,iBAAiB;EAClC;AAEM,EAAA,iBAAU,MAAM;AAGpB,aAAS,WAAW,kBAAkB,EAAE,WAAW,MAAM,CAAC;EAC5D,GAAG,CAAC,kBAAkB,QAAQ,CAAC;AAG/B,MAAI,cAAc,kBAAkB,MAAM,GAAG;AAC3C,UAAM,gBAAgB,kBAAkB,UAAU,kBAAkB;EACtE;AAGA,MACE,YAAY;IACV;IACA;IACA,cAAc,iBAAiB;IAC/B,OAAO,OACJ,cAAc,EACd,IAKC,iBAAiB,SAAS;EAChC,CAAC,GACD;AACA,UAAM,OAAO;EACf;AAEA;AAAE,qBAAO,kBAAkB,EAAE,YAA3B,mBAA4C,6BAA5C;;IACA;IACA;;AAGF,MACE,iBAAiB,iCACjB,CAAC,YACD,UAAU,QAAQ,WAAW,GAC7B;AACA,UAAM,UAAU;;MAEZ,gBAAgB,kBAAkB,UAAU,kBAAkB;;;OAE9D,YAAO,cAAc,EAAE,IAAI,iBAAiB,SAAS,MAArD,mBAAwD;;AAE5D,uCAAS,MAAMA,OAAM,QAAQ,MAAM;AAEjC,eAAS,aAAa;IACxB;EACF;AAGA,SAAO,CAAC,iBAAiB,sBACrB,SAAS,YAAY,MAAM,IAC3B;AACN;;;ACvHO,SAAS,SAAS,SAA0B,aAA2B;AAC5E,SAAO,aAAa,SAAS,eAAe,WAAW;AACzD;;;ACvCO,SAAS,iBAMd,SACA,aACuC;AACvC,MAAI,MAAuC;AACzC,QAAK,QAAQ,YAAoB,WAAW;AAC1C,cAAQ,MAAM,+CAA+C;IAC/D;EACF;AAEA,SAAO;IACL;MACE,GAAG;MACH,SAAS;MACT,UAAU;MACV,cAAc;MACd,iBAAiB;IACnB;IACA;IACA;EACF;AACF;;;AChBO,SAAS,yBAOd,SAQA,aAC+C;AAC/C,MAAI,MAAuC;AACzC,QAAK,QAAQ,YAAoB,WAAW;AAC1C,cAAQ,MAAM,uDAAuD;IACvE;EACF;AAEA,SAAO;IACL;MACE,GAAG;MACH,SAAS;MACT,UAAU;MACV,cAAc;IAChB;IACA;IACA;EACF;AACF;;;ACkIO,SAAS,mBAId,SAIA,aACiB;AACjB,SAAO;IACL;MACE,GAAG;MACH,SAAS,QAAQ,QAAQ,IAAI,CAAC,UAAU;AACtC,YAAI,MAAuC;AACzC,cAAI,MAAM,YAAY,WAAW;AAC/B,oBAAQ,MAAM,iDAAiD;UACjE;QACF;AAEA,eAAO;UACL,GAAG;UACH,UAAU;UACV,cAAc;UACd,SAAS;UACT,iBAAiB;QACnB;MACF,CAAC;IACH;IACA;EACF;AACF;;;AC3MO,SAAS,iBAMd,SACA,aACA;AACA,QAAM,SAAS,eAAe,WAAW;AAEzC,MAAI,CAAC,OAAO,cAAc,QAAQ,QAAQ,GAAG;AAC3C,WAAO,cAAc,OAAO;EAC9B;AACF;;;ACdO,SAAS,yBAOd,SAOA,aACA;AACA,QAAM,SAAS,eAAe,WAAW;AAEzC,MAAI,CAAC,OAAO,cAAc,QAAQ,QAAQ,GAAG;AAC3C,WAAO,sBAAsB,OAAO;EACtC;AACF;;;ACsDO,SAAS,aAAa,SAAkB;AAC7C,SAAO;AACT;;;ACwEO,SAAS,qBAAqB,SAAkB;AACrD,SAAO;AACT;;;AC5JA,IAAAC,SAAuB;AAuBhB,IAAM,oBAAoB,CAAC;EAChC;EACA,UAAU,CAAC;EACX;EACA;AACF,MAA8B;AAC5B,QAAM,SAAS,eAAe,WAAW;AACzC,QAAM,CAAC,gBAAgB,iBAAiB,IAAU,gBAEhD;AAEF,QAAM,aAAmB,cAAO,OAAO;AACvC,aAAW,UAAU;AAiBf,EAAA,eAAQ,MAAM;AAClB,QAAI,OAAO;AACT,UAAI,OAAO,UAAU,UAAU;AAC7B;MACF;AAEA,YAAM,aAAa,OAAO,cAAc;AAExC,YAAM,UAAW,MAA0B,WAAW,CAAC;AAEvD,YAAM,aAAyC,CAAC;AAChD,YAAM,kBAA8C,CAAC;AACrD,iBAAW,mBAAmB,SAAS;AACrC,cAAM,gBAAgB,WAAW,IAAI,gBAAgB,SAAS;AAE9D,YAAI,CAAC,eAAe;AAClB,qBAAW,KAAK,eAAe;QACjC,OAAO;AACL,gBAAM,mBACJ,gBAAgB,MAAM,gBACtB,cAAc,MAAM;AACtB,gBAAM,qBAAqB,iDAAgB;YACzC,CAAC,UAAU,MAAM,cAAc,gBAAgB;;AAGjD,cACE,qBACC,CAAC,sBACA,gBAAgB,MAAM,gBACpB,mBAAmB,MAAM,gBAC7B;AACA,4BAAgB,KAAK,eAAe;UACtC;QACF;MACF;AAEA,UAAI,WAAW,SAAS,GAAG;AAGzB,gBAAQ,QAAQ,EAAE,SAAS,WAAW,GAAG,WAAW,OAAO;MAC7D;AACA,UAAI,gBAAgB,SAAS,GAAG;AAC9B;UAAkB,CAAC,SACjB,OAAO,CAAC,GAAG,MAAM,GAAG,eAAe,IAAI;QACzC;MACF;IACF;EACF,GAAG,CAAC,QAAQ,gBAAgB,KAAK,CAAC;AAE5B,EAAA,iBAAU,MAAM;AACpB,QAAI,gBAAgB;AAClB,cAAQ,QAAQ,EAAE,SAAS,eAAe,GAAG,WAAW,OAAO;AAC/D,wBAAkB,MAAS;IAC7B;EACF,GAAG,CAAC,QAAQ,cAAc,CAAC;AAE3B,SAAO;AACT;;;AC/GA,IAAAC,SAAuB;AAMhB,SAAS,cACd,SACA,aACQ;AACR,QAAM,SAAS,eAAe,WAAW;AACzC,QAAM,aAAa,OAAO,cAAc;AAExC,SAAa;IACL;MACJ,CAAC,kBACC,WAAW,UAAU,cAAc,WAAW,aAAa,CAAC;MAC9D,CAAC,UAAU;IACb;IACA,MAAM,OAAO,WAAW,OAAO;IAC/B,MAAM,OAAO,WAAW,OAAO;EACjC;AACF;;;ACpBA,IAAAC,SAAuB;AAYhB,SAAS,cACd,SACA,aACQ;AACR,QAAM,SAAS,eAAe,WAAW;AACzC,SAAO;IACL,EAAE,SAAS,EAAE,GAAG,SAAS,QAAQ,UAAU,EAAE;IAC7C;EACF,EAAE;AACJ;AAOA,SAAS,UACP,eACA,SACgB;AAChB,SAAO,cACJ,QAAQ,QAAQ,OAAO,EACvB;IACC,CAAC,aACE,QAAQ,SAAS,QAAQ,OAAO,QAAQ,IAAI,SAAS;EAC1D;AACJ;AAEO,SAAS,iBACd,UAAyC,CAAC,GAC1C,aACgB;AAChB,QAAM,gBAAgB,eAAe,WAAW,EAAE,iBAAiB;AACnE,QAAM,aAAmB,cAAO,OAAO;AACvC,QAAM,SAAe,cAAuB,IAAI;AAChD,MAAI,CAAC,OAAO,SAAS;AACnB,WAAO,UAAU,UAAU,eAAe,OAAO;EACnD;AAEM,EAAA,iBAAU,MAAM;AACpB,eAAW,UAAU;EACvB,CAAC;AAED,SAAa;IACL;MACJ,CAAC,kBACC,cAAc,UAAU,MAAM;AAC5B,cAAM,aAAa;UACjB,OAAO;UACP,UAAU,eAAe,WAAW,OAAO;QAC7C;AACA,YAAI,OAAO,YAAY,YAAY;AACjC,iBAAO,UAAU;AACjB,wBAAc,SAAS,aAAa;QACtC;MACF,CAAC;MACH,CAAC,aAAa;IAChB;IACA,MAAM,OAAO;IACb,MAAM,OAAO;EACf;AACF;;;AC3EA,IAAAC,UAAuB;AAahB,SAAS,YAMd,SACA,aACwD;AACxD,QAAM,SAAS,eAAe,WAAW;AAEzC,QAAM,CAAC,QAAQ,IAAU;IACvB,MACE,IAAI;MACF;MACA;IACF;EACJ;AAEM,EAAA,kBAAU,MAAM;AACpB,aAAS,WAAW,OAAO;EAC7B,GAAG,CAAC,UAAU,OAAO,CAAC;AAEtB,QAAM,SAAe;IACb;MACJ,CAAC,kBACC,SAAS,UAAU,cAAc,WAAW,aAAa,CAAC;MAC5D,CAAC,QAAQ;IACX;IACA,MAAM,SAAS,iBAAiB;IAChC,MAAM,SAAS,iBAAiB;EAClC;AAEA,QAAM,SAAe;IAGnB,CAAC,WAAW,kBAAkB;AAC5B,eAAS,OAAO,WAAW,aAAa,EAAE,MAAMC,KAAI;IACtD;IACA,CAAC,QAAQ;EACX;AAEA,MACE,OAAO,SACP,iBAAiB,SAAS,QAAQ,cAAc,CAAC,OAAO,KAAK,CAAC,GAC9D;AACA,UAAM,OAAO;EACf;AAEA,SAAO,EAAE,GAAG,QAAQ,QAAQ,aAAa,OAAO,OAAO;AACzD;;;ACQO,SAAS,iBACd,SACA,aACA;AACA,SAAO;IACL;IACA;IACA;EACF;AACF;", "names": ["_a", "_a", "_cleanup", "_setup", "_a", "_a", "_a", "_b", "_c", "_a", "_a", "_retryer", "getDefaultState", "dispatch_fn", "_a", "_a", "_b", "_a", "_mutationCache", "_defaultOptions", "_a", "_a", "_client", "_queries", "_observers", "notify_fn", "_a", "_a", "_client", "_currentResult", "notify_fn", "getDefaultState", "_a", "_b", "React", "React", "React", "import_jsx_runtime", "React", "noop", "noop", "React", "_a", "noop", "React", "React", "React", "React", "noop"]}