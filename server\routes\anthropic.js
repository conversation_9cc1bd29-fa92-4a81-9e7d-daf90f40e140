import express from 'express';
import fetch from 'node-fetch';

const router = express.Router();

// Model discovery endpoint
router.get('/models', async (req, res) => {
  try {
    // Get API key from environment variables
    const apiKey = process.env.ANTHROPIC_API_KEY;
    if (!apiKey) {
      throw new Error('Anthropic API key is not configured in .env file');
    }
    
    // Make the API request to get models
    const response = await fetch('https://api.anthropic.com/v1/models', {
      method: 'GET',
      headers: {
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01'
      }
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Anthropic API error: ${errorData?.error?.message || response.statusText || 'Unknown error'}`);
    }

    const result = await response.json();
    
    // Add detailed logging about the models received
    console.log('Anthropic API response:', JSON.stringify(result, null, 2));
    console.log('Model IDs available:', result.data.map(model => model.id).join(', '));
    
    // Format models for frontend display
    const formattedModels = {};
    
    // Add a header/separator for Anthropic models
    formattedModels['anthropic-header'] = {
      id: 'anthropic-header',
      name: '── Anthropic Models ──',
      isGroupHeader: true
    };
    
    // Process and add each model
    result.data.forEach(model => {
      const modelId = `anthropic:${model.id}`;
      
      // Format context window for display
      let contextInfo = '';
      if (model.context_window_tokens) {
        const contextK = Math.round(model.context_window_tokens / 1000);
        contextInfo = ` (${contextK}K)`;
      }
      
      // Use the API-provided display_name exactly as it comes from the API
      // This ensures we're using the official model names instead of our custom formatting
      const displayName = model.display_name + contextInfo;
      
      formattedModels[modelId] = {
        id: modelId,
        name: displayName,
        description: model.description || 'Anthropic model',
        contextWindow: model.context_window_tokens || 100000,
        provider: 'anthropic'
      };
    });
    
    console.log('Anthropic models fetched successfully');
    res.json(formattedModels);
  } catch (error) {
    console.error('Error fetching Anthropic models:', error);
    res.status(500).json({ error: error.message });
  }
});

// Main anthropic content enhancement route
router.post('/enhance', async (req, res) => {
  try {
    console.log('Received Anthropic enhance request:', req.body);
    const { 
      model, 
      content, 
      targetWordCount, 
      locationContext, 
      contextualData,
      systemPrompt,
      temperature,
      topP,
      max_tokens
    } = req.body;
    
    // Format the prompt
    const prompt = `
Content to enhance (target word count: ${targetWordCount}):
${content}

Location: ${locationContext.name}
Context: ${locationContext.hierarchyPath}

Enhance this content following these primary rules:
1. PRESERVE EVERY DETAIL FROM THE ORIGINAL CONTENT
2. Follow the prompt guide exactly as specified 
3. Keep the original tone and style of the content
4. Maintain all original location references
5. Meet the target word count
6. Use natural, grammatically correct language
`;

    // Get API key from environment variables
    const apiKey = process.env.ANTHROPIC_API_KEY;
    if (!apiKey) {
      throw new Error('Anthropic API key is not configured in .env file');
    }
    
    console.log('Using Anthropic API with model:', model);
    
    // Format the request according to Anthropic API requirements
    const requestBody = {
      model: model.replace('anthropic:', ''),
      system: systemPrompt || 'You are a helpful assistant.',
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ]
    };
    
    console.log('Anthropic enhance request:', JSON.stringify(requestBody, null, 2));
    
    // Make the API request
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Anthropic API error: ${errorData?.error?.message || response.statusText || 'Unknown error'}`);
    }

    const result = await response.json();
    const enhancedContent = result.content[0].text;
    
    console.log('Anthropic enhancement successful');
    res.json({ enhancedContent });
  } catch (error) {
    console.error('Anthropic Enhancement Error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Language detection route
router.post('/detect-language', async (req, res) => {
  try {
    console.log('Received Anthropic language detection request:', req.body);
    const { 
      model, 
      location, 
      content 
    } = req.body;
    
    // Get API key from environment variables
    const apiKey = process.env.ANTHROPIC_API_KEY;
    if (!apiKey) {
      throw new Error('Anthropic API key is not configured in .env file');
    }
    
    // Bulletproof prompt matching contentTranslation.ts
    const prompt = `Return ONLY a JSON array of FULL LANGUAGE NAMES for ${location.name}:

CRITICAL RULES:
1. ALWAYS include the primary native language first
2. ALWAYS include "English" as second language (regardless of local usage)
3. EXCEPTION: If primary language IS English, return only ["English"]
4. Use full names never codes (use "Chinese" not "zh")

EXAMPLES: ["Chinese","English"] for China, ["Japanese","English"] for Japan, ["Spanish","English"] for Spain, ["English"] for USA/UK

Return ONLY the JSON array with full language names.`;
    
    console.log('Using Anthropic for language detection with model:', model);
    
    // Format the request according to Anthropic API requirements - system parameter at top level
    const requestBody = {
      model: model.replace('anthropic:', ''),
      system: "You are a language detection AI. CRITICAL: ALWAYS include primary native language first, then 'English' second (except if primary IS English, then only ['English']). Return ONLY JSON array of full language names.",
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.2,
      max_tokens: 1000
    };
    
    console.log('Anthropic language detection request:', JSON.stringify(requestBody, null, 2));
    
    // Make the API request
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Anthropic API error: ${errorData?.error?.message || response.statusText || 'Unknown error'}`);
    }

    const result = await response.json();
    const detectionResult = result.content[0].text;
    
    console.log('Anthropic language detection successful');
    res.json({ enhancedContent: detectionResult });
  } catch (error) {
    console.error('Anthropic Language Detection Error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Translation route
router.post('/translate', async (req, res) => {
  try {
    console.log('Received Anthropic translation request:', req.body);
    const { 
      model, 
      content, 
      sourceLang, 
      targetLang 
    } = req.body;
    
    // Get API key from environment variables
    const apiKey = process.env.ANTHROPIC_API_KEY;
    if (!apiKey) {
      throw new Error('Anthropic API key is not configured in .env file');
    }
    
    console.log('Using Anthropic for translation with model:', model);
    
    // Format the request with system parameter at top level
    const requestBody = {
      model: model.replace('anthropic:', ''),
      system: `You are a professional translator. Translate from ${sourceLang} to ${targetLang}. 
               Maintain all formatting, HTML tags, and markdown exactly as provided.
               Preserve all special characters, emojis, and spacing.
               Do not add or remove content - translate exactly what is provided.`,
      messages: [
        {
          role: 'user',
          content
        }
      ]
    };
    
    console.log('Anthropic translation request:', JSON.stringify(requestBody, null, 2));
    
    // Make the API request
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Anthropic API error: ${errorData?.error?.message || response.statusText || 'Unknown error'}`);
    }

    const result = await response.json();
    const translatedContent = result.content[0].text;
    
    console.log('Anthropic translation successful');
    res.json({ content: translatedContent });
  } catch (error) {
    console.error('Anthropic Translation Error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Default completion endpoint
router.post('/', async (req, res) => {
  try {
    console.log('Received Anthropic completion request:', req.body);
    const { 
      model,
      content,
      systemPrompt
    } = req.body;
    
    // Get API key from environment variables
    const apiKey = process.env.ANTHROPIC_API_KEY;
    if (!apiKey) {
      throw new Error('Anthropic API key is not configured in .env file');
    }
    
    // Format the request with system parameter at top level
    const requestBody = {
      model: model.replace('anthropic:', ''),
      system: systemPrompt,
      messages: [
        {
          role: 'user',
          content
        }
      ],
      temperature,
      max_tokens
    };
    
    console.log(`Using Anthropic API for completion with model: ${requestBody.model}`);
    
    // Make the API request
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Anthropic API error: ${errorData?.error?.message || response.statusText || 'Unknown error'}`);
    }

    const result = await response.json();
    const enhancedContent = result.content[0].text;
    
    console.log('Anthropic completion successful');
    res.json({ content: enhancedContent, enhancedContent });
  } catch (error) {
    console.error('Anthropic Completion Error:', error);
    res.status(500).json({ error: error.message });
  }
});

export default router;
