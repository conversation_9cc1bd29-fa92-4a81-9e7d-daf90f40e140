import React, { useState, useEffect, useRef, useCallback, useMemo, forwardRef, useImperativeHandle, useContext } from 'react';
import { Send, Plus, Loader, Maximize2, Minimize2, RefreshCw, ChevronsDown, ChevronsUp } from 'lucide-react';
import { Button } from './Button';
import { usePandemicStore } from '../store';
import { useConsoleStore } from '../stores/consoleStore';
import { wordpressService } from '../services/wordpress';
import { ImageGenerator } from './ImageGenerator';
import { SectionEditor } from './SectionEditor';
import { WordPressSiteManager } from './WordPressSiteManager';
import { Section } from '../types';
import { handleLaunchCampaign } from './TopNav';
import { geonamesApi } from '../services/geonames';
import { TitleGuide } from './TitleGuide';
import { contentTranslationService } from '../services/contentTranslation';
import type { Location } from '../services/locations';
import { locationService } from '../services/locations';
import { neuroEnhancerService } from '../services/neuroEnhancer';
import { directOpenAIService } from '../services/directOpenAIService';
import { stabilityService } from '../services/stability';
import { imageGeneratorService } from '../services/imageGenerator';
import { TemplateLoader } from './TemplateLoader';
import { batchProcessor } from '../services/batchProcessor';
import { BatchProgress } from './BatchProgress';
import { imageLibraryService } from '../services/imageLibrary';
import { TemplateManager } from './TemplateManager';
import { templateService } from '../services/templateService';
import { ContentTemplate } from '../types/templates';
import { CollapsibleSection } from './CollapsibleSection';
import { useQuery, useMutation } from '@tanstack/react-query';
import { Input } from './ui/input';
import { queryClient } from '../services/queryClient';
import { getTemplateForLocation } from '../templates/generic-templates';
import { multiLocationTemplate } from '../templates/generic-templates';
import { TemplateSettingsContext } from '../contexts/TemplateSettingsContext';
import { getSettings, saveSettings } from '../config/systemSettings';
import { lmStudioDetectionService } from '../services/lmStudioDetectionService';
import { ollamaDetectionService } from '../services/ollamaDetectionService';
import { openRouterService } from '../services/openRouterService';
import { anthropicService } from '../services/anthropicService';
import { API_BASE_URL } from '../config/apiConfig';

interface CampaignProgress {
  total: number;
  completed: number;
  current: string;
  errors: Array<{ location: string; error: string }>;
  phase: 'idle' | 'processing' | 'completed' | 'failed';
}

interface LocationWithPath {
  id: string;
  name: string;
  countryCode?: string;
  population?: number;
  parentId?: string;
  featureClass?: string;
  featureCode?: string;
  hierarchyPath: string;
  fullPath: string;
  has_children: boolean;
}

interface LocationProcessingState {
  location: Location;
  phase: 'preparing' | 'content' | 'translation' | 'images' | 'publishing';
  status: 'pending' | 'active' | 'completed' | 'failed';
  error?: string;
}

interface CampaignState {
  status: 'idle' | 'initializing' | 'processing' | 'paused' | 'completed' | 'failed';
  queue: Location[];
  currentLocation: LocationProcessingState | null;
  completedLocations: Location[];
  errors: Array<{
    location: Location;
    phase: string;
    message: string;
  }>;
  isPaused: boolean;
}

// Add campaign mode type
type CampaignMode = 'single' | 'mass' | 'idle' | 'template';

// Add retry mechanism interface
interface RetryConfig {
  maxAttempts: number;
  delayMs: number;
  backoffFactor: number;
}

// Add retry utility function
const retryOperation = async <T,>(
  operation: () => Promise<T>,
  config: RetryConfig,
  phase: string,
  locationName: string
): Promise<T> => {
  const consoleStore = useConsoleStore.getState();
  let lastError: Error;
  let delay = config.delayMs;

  for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error: any) {
      lastError = error;
      if (attempt < config.maxAttempts) {
        consoleStore.addMessage('warning', 
          `Attempt ${attempt}/${config.maxAttempts} failed for ${locationName} (${phase}). Retrying in ${delay/1000}s...`
        );
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= config.backoffFactor;
      }
    }
  }
  throw lastError!;
};

// Add to ContentEditor props
interface ContentEditorProps {
  onTemplateSaved?: (template: ContentTemplate) => void;
}

// Define the ref type for ContentEditor
export interface ContentEditorRefType {
  headerShortcode: string;
  footerShortcode: string;
}

// Declare global window type with contentEditorRef
declare global {
  interface Window {
    contentEditorRef: React.RefObject<ContentEditorRefType>;
  }
}

// Export the ContentEditor component
export const ContentEditor: React.FC<ContentEditorProps> = ({ onTemplateSaved }) => {
  const consoleStore = useConsoleStore.getState();
  const [debugLog, setDebugLog] = useState<string[]>([]);
  
  // Add state for saved campaign tracking
  const [hasSavedCampaign, setHasSavedCampaign] = useState(false);
  
  // Add state for tracking if all sections should be expanded/collapsed
  const [allSectionsExpanded, setAllSectionsExpanded] = useState(false);
  
  // Function to toggle all sections expanded state
  const toggleAllSections = () => {
    setAllSectionsExpanded(!allSectionsExpanded);
  };
  
  // Check for saved campaign on mount
  useEffect(() => {
    const checkForSavedCampaign = () => {
      const savedState = localStorage.getItem('batchProcessorState');
      setHasSavedCampaign(!!savedState);
    };
    
    checkForSavedCampaign();
    
    // Listen for storage events in case another tab/window changes the state
    const handleStorageChange = (e) => {
      if (e.key === 'batchProcessorState') {
        checkForSavedCampaign();
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);
  
  // Handler for resuming a saved campaign
  const handleResumeCampaign = async () => {
    const consoleStore = useConsoleStore.getState();
    
    // Skip site ID check - just use whatever site is currently selected
    
    consoleStore.addMessage('info', '🔄 Resuming saved campaign...');
    
    // Check if we have the required data to process locations
    if (!activeSiteId) {
      consoleStore.addMessage('error', '❌ No WordPress site selected. Please select a site first.');
      return;
    }
    
    if (sections.length === 0) {
      consoleStore.addMessage('error', '❌ No content sections defined. Please create content sections first.');
      return;
    }
    
    // Update UI to show we're running
    setBatchProgress({
      ...batchProgress,
      status: 'running'
    });
    
    // Show loading message in console
    consoleStore.addMessage('info', '⏳ Loading saved locations... This may take several minutes for large campaigns.');
    
    // This function provides full location data for IDs in the saved state and ensures full hierarchical paths are built
    const getLocationById = async (id: string): Promise<any> => {
      try {
        // Helper function to recursively build the location hierarchy path
        const buildLocationHierarchy = async (locationId: string, maxDepth = 10): Promise<{path: string[], names: string[]}> => {
          if (maxDepth <= 0) {
            // Safety check to prevent infinite recursion
            return { path: [], names: [] };
          }
          
          try {
            // Get the location data
            await locationService.ensureLocationHierarchy(locationId);
            const location = await locationService.getLocation(locationId);
            
            // Get the parent ID (only use parentId which is the correct property name)
            const parentId = location.parentId;
            
            if (parentId) {
              // Recursively get the parent hierarchy
              const parentHierarchy = await buildLocationHierarchy(parentId, maxDepth - 1);
              
              // Add current location to hierarchy
              return {
                path: [...parentHierarchy.path, locationId],
                names: [...parentHierarchy.names, location.name]
              };
            } else {
              // Base case - no parent, this is a root location
              return { 
                path: [locationId], 
                names: [location.name] 
              };
            }
          } catch (error: unknown) {
            // Handle error during hierarchy building
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            console.error(`Failed to build hierarchy for location ${locationId}: ${errorMessage}`);
            return { 
              path: [locationId], 
              names: [`Unknown-${locationId}`] 
            };
          }
        };
        
        // First check if it's in the selected locations map
        if (selectedLocations.has(id)) {
          const location = selectedLocations.get(id);
          if (location) {
            // Make sure it has a full path
            if (!location.hierarchyPath && !location.fullPath) {
              try {
                // Build full hierarchy path
                const hierarchy = await buildLocationHierarchy(id);
                
                // Assign the hierarchy paths to the location object
                location.hierarchyPath = hierarchy.path.join('/');
                location.fullPath = hierarchy.names.join('/');
                
                consoleStore.addMessage('info', `✅ Built full path data for ${location.name}: ${location.fullPath}`);
                console.log(`LOCATION PATH BUILT: ${location.name} with full path: ${location.fullPath}`);
                
                return location;
              } catch (e: unknown) {
                // Continue with the location we have but add a basic path
                const errorMessage = e instanceof Error ? e.message : 'Unknown error';
                consoleStore.addMessage('warning', `⚠️ Could not build full path for ${location.name}: ${errorMessage}`);
                
                // Add a basic path for disambiguation
                location.hierarchyPath = `Restored/${id}`;
                location.fullPath = `Restored/${location.name || id}`;
                return location;
              }
            }
            return location;
          }
        }
        
        // Otherwise fetch and build the complete location with hierarchy
        try {
          // First ensure we have the full hierarchy data for this location
          await locationService.ensureLocationHierarchy(id);
          const location = await locationService.getLocation(id);
          
          // Build the full hierarchical path if it doesn't exist
          if (!location.hierarchyPath || !location.fullPath) {
            const hierarchy = await buildLocationHierarchy(id);
            location.hierarchyPath = hierarchy.path.join('/');
            location.fullPath = hierarchy.names.join('/');
            consoleStore.addMessage('info', `✅ Built full path data for ${location.name}: ${location.fullPath}`);
          }
          
          // Log the path info to debug
          console.log(`LOCATION PATH: ${location.name} with full path: ${location.fullPath || location.hierarchyPath || 'MISSING PATH'}`);
          
          return location;
        } catch (error: unknown) {
          // Create a minimal location object as fallback with a distinctive path
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          consoleStore.addMessage('warning', `⚠️ Using minimal location data for ${id}: ${errorMessage}`);
          return {
            id,
            name: `Location ${id}`,
            has_children: false,
            hierarchyPath: `Unknown/${id}`,
            fullPath: `Unknown/Location ${id}`
          };
        }
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        consoleStore.addMessage('error', `❌ Failed to process location ${id}: ${errorMessage}`);
        throw error;
      }
    };
    
    // Restore the queue
    const restored = await batchProcessor.restoreQueue(getLocationById);
    
    if (!restored) {
      consoleStore.addMessage('error', '❌ No saved campaign found');
      // Reset UI status on failure
      setBatchProgress({
        ...batchProgress,
        status: 'idle'
      });
      return;
    }
    
    // Log that we're starting the processing
    consoleStore.addMessage('info', '🚀 Starting batch process for restored locations...');
    
    try {
      // Start the processor with whatever site is currently selected
      await batchProcessor.start(async (location: any, options) => {
        try {
          // Process with existing function that already handles all the WordPress steps
          const result = await handleLaunchSingle(location);
          return result;
        } catch (error: any) {
          consoleStore.addMessage('error', `❌ Failed to process location ${location.name}: ${error.message}`);
          throw error;
        }
      });
      
      // Update UI when complete
      setBatchProgress({
        ...batchProgress,
        status: 'completed'
      });
      
    } catch (error: any) {
      consoleStore.addMessage('error', `❌ Campaign processing failed: ${error.message}`);
      // Reset UI status on failure
      setBatchProgress({
        ...batchProgress,
        status: 'idle'
      });
    }
  };
  
  // Handler for clearing a saved campaign
  const handleClearSavedCampaign = () => {
    if (confirm('Are you sure you want to clear the saved campaign?')) {
      batchProcessor.clearState();
      setHasSavedCampaign(false);
      useConsoleStore.getState().addMessage('info', '🧹 Saved campaign cleared');
    }
  };
  
  const addDebugLog = useCallback((message: string) => {
    console.log(`[DEBUG] ${message}`);
    setDebugLog(prev => [...prev, `[${new Date().toISOString()}] ${message}`]);
  }, []);

  // Properly typed ref for ImageGenerator
  const imageGeneratorRef = useRef<{
    getPrompt: () => string;
    setPrompt: (prompt: string) => void;
    generateImage: (location: any) => Promise<{ imageBlob: Blob | null; imageUrl: string }>;
  }>(null);

  // Store generated images for each location
  const [generatedImages, setGeneratedImages] = useState<Map<string, Blob>>(new Map());

  const [title, setTitle] = useState('');
  const [sections, setSections] = useState<Section[]>([]);
  const [isLaunching, setIsLaunching] = useState(false);
  const [featuredImageBlob, setFeaturedImageBlob] = useState<Blob | null>(null);
  const [previousImages, setPreviousImages] = useState<Array<{ url: string; blob: Blob }>>([]);
  const [campaignProgress, setCampaignProgress] = useState<CampaignProgress>({
    total: 0,
    completed: 0,
    current: '',
    errors: [],
    phase: 'idle'
  });
  const [currentLeafLocation, setCurrentLeafLocation] = useState<Location | null>(null);
  const { selectedLocations, activeSiteId } = usePandemicStore();
  const [currentSection, setCurrentSection] = useState<Section | null>(null);

  // Models state - moved here to fix initialization order
  const [availableModels, setAvailableModels] = useState<{[key: string]: any}>({});
  const [isLoadingModels, setIsLoadingModels] = useState(false);
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [languageDetectionModel, setLanguageDetectionModel] = useState<string>(''); // For language detection specifically
  const [modelFilter, setModelFilter] = useState('');
  const [languageDetectionModelFilter, setLanguageDetectionModelFilter] = useState('');

  // New campaign state
  const [campaignState, setCampaignState] = useState<CampaignState>({
    status: 'idle',
    queue: [],
    currentLocation: null,
    completedLocations: [],
    errors: [],
    isPaused: false
  });

  // Add campaign mode type
  const [campaignMode, setCampaignMode] = useState<CampaignMode>('idle');

  // Add mode detection effect
  useEffect(() => {
    if (selectedLocations.size === 0) {
      setCampaignMode('idle');
    } else if (selectedLocations.size === 1) {
      setCampaignMode('mass');
    } else {
      setCampaignMode('mass');
    }
  }, [selectedLocations.size]);

  useEffect(() => {
    const allLocations = Array.from(selectedLocations.values())
      .filter(location => location && location.id);
    
    // Find leaf locations
    const leafLocations = allLocations.filter(location =>
      !allLocations.some(otherLocation => otherLocation.parentId === location.id)
    );

    // Analyze languages for each leaf location
    leafLocations.forEach(async (location) => {
      try {
        // Build proper full hierarchical path using the same method as content generation
        const hierarchy = await geonamesApi.getLocationHierarchy(location.id);
        const fullPath = hierarchy.map(l => l.name).join(' > ');
        
        const locationWithPath: LocationWithPath = {
          ...location,
          hierarchyPath: fullPath,
          fullPath: fullPath,
          has_children: false
        };
        
        console.log(`🔍 Analyzing languages for ${location.name} with full path: ${fullPath}`);
        useConsoleStore.getState().addMessage('success', `✅ Built full hierarchy path for language detection: ${fullPath}`);
        
        if (!validateModelSelection()) {
          return; // Stop execution if no model selected
        }
        // Use language detection model if set, otherwise fallback to main model
        const modelForLanguageDetection = languageDetectionModel || selectedModel;
        await contentTranslationService.getLocationLanguages(locationWithPath, modelForLanguageDetection);
      } catch (error) {
        console.error(`Failed to analyze languages for ${location.name}:`, error);
        useConsoleStore.getState().addMessage('error', error.message || 'Failed to analyze languages');
      }
    });

    // Set current leaf location if exactly one
    if (leafLocations.length === 1) {
      setCurrentLeafLocation(leafLocations[0]);
    } else {
      setCurrentLeafLocation(null);
    }
  }, [selectedLocations]);  // eslint-disable-line react-hooks/exhaustive-deps

  // Helper function to process a single location
  const processLocation = useCallback(async (locationId: string) => {
    try {
      const location = selectedLocations.get(locationId);
      if (!location) return;
      
      // Don't automatically set any prompts
      
      if (imageGeneratorRef.current && sections.length > 0) {
        const allLocations = Array.from(selectedLocations);
        const leafLocation = allLocations.find(location => 
          !allLocations.some(otherLocation => 
            otherLocation.parentId === location.id
          )
        );
      }
      
      // No-op - don't set any prompts
      
    } catch (error: any) {
      console.error('Error processing location', error);
    }
  }, [selectedLocations, sections, imageGeneratorRef]);

  // Update handleLaunchMass with complete implementation
  const [batchProgress, setBatchProgress] = useState<{
    total: number;
    completed: number;
    failed: number;
    current: null;
    status: 'idle' | 'running' | 'paused' | 'completed' | 'failed';
  }>({
    total: 0,
    completed: 0,
    failed: 0,
    current: null,
      status: 'idle'
  });

  useEffect(() => {
    const handleProgress = (progress: any) => {
      setBatchProgress(progress);
    };

    batchProcessor.on('progress', handleProgress);
    return () => {
      batchProcessor.off('progress', handleProgress);
    };
  }, []);

  const handleLaunchSingle = async (location: Location) => {
    const consoleStore = useConsoleStore.getState();
    
    try {
      // CHECKPOINT: Pre-flight validation
      if (!activeSiteId) {
        consoleStore.addMessage('error', '❌ No WordPress site selected');
        throw new Error('WordPress site not selected');
      }

      if (sections.length === 0) {
        consoleStore.addMessage('error', '❌ No content sections defined');
        throw new Error('Content sections required');
      }

      // CHECKPOINT: Get template and validate
      const template = getTemplateForLocation(selectedLocations.size > 1);
      if (!template) {
        consoleStore.addMessage('error', '❌ No template found for location');
        throw new Error('Template not found');
      }

      if (!template.title || !template.imagePromptGuide) {
        consoleStore.addMessage('error', '❌ Invalid template structure');
        throw new Error('Invalid template structure');
      }

      consoleStore.addMessage('info', `\n🔄 Processing content for ${location.name}...`);

      // CHECKPOINT: Process title
      const processedTitle = (title || template.title).replace(/\[Location\]/g, location.name);
      if (!processedTitle) {
        consoleStore.addMessage('error', '❌ Failed to process title');
        throw new Error('Title processing failed');
      }

      // IMPORTANT FIX: Always build full hierarchical path for each location
      // This ensures accurate location disambiguation in prompts
      consoleStore.addMessage('info', `🔄 Building full location hierarchy for ${location.name} (ID: ${location.id})...`);
      
      // Start with default values in case hierarchy building fails
      let locationWithPath = {
        ...location,
        hierarchyPath: location.fullPath || location.name,
        fullPath: location.fullPath || location.name,
        has_children: location.has_children || false
      };
      
      try {
        // Call the authoritative hierarchy-building function
        const hierarchy = await geonamesApi.getLocationHierarchy(location.id);
        
        // Build a proper full path string from the hierarchy
        const fullPath = hierarchy.map(l => l.name).join(' > ');
        
        // Create enriched location with full path
        locationWithPath = {
          ...location,
          hierarchyPath: fullPath,
          fullPath: fullPath,
          has_children: location.has_children || false
        };
        
        consoleStore.addMessage('success', `✅ Built full hierarchy path: ${fullPath}`);
      } catch (error: any) {
        // If hierarchy building fails, log error but continue with existing data
        consoleStore.addMessage('error', `⚠️ Failed to build location hierarchy: ${error.message}. Using fallback values.`);
        
        // Fallback to existing values or basic name
        locationWithPath = {
          ...location,
          hierarchyPath: location.fullPath || 
                        (location.feature_class || location.featureClass) ? 
                        `${location.feature_class || location.featureClass}/${location.name}` : 
                        location.name,
          fullPath: location.fullPath || 
                   (location.feature_class || location.featureClass) ? 
                   `${location.feature_class || location.featureClass}/${location.name}` : 
                   location.name,
          has_children: location.has_children || false
        };
      }
      
      // DEBUGGING: Log location path details for verification
      consoleStore.addMessage('info', `📍 LOCATION PATH: ${locationWithPath.fullPath}`);


      // CHECKPOINT: Enhance sections
      const enhancedSections = await Promise.all(sections.map(async (section, index) => {
        consoleStore.addMessage('info', `📝 Processing section ${index + 1}/${sections.length}`);
        
        // Get settings for this section to determine which service to use
        const sectionSettings = templateSettingsContext.getSettingsForSection(section.id);
        
        // Select the appropriate service based on settings
        const enhancementService = sectionSettings.generationStyle === 'direct' ? 
                                  directOpenAIService : neuroEnhancerService;
        
        // Create a function to validate and convert wordCount to an acceptable value
        const getValidWordCount = (count: number): 250 | 500 | 800 | 1200 | 1500 | 2000 | 2500 | 3000 | 3500 | 4000 | 4500 | 5000 | 5500 | 6000 | 6500 | 7000 | 7500 | 8000 => {
          const validCounts = [250, 500, 800, 1200, 1500, 2000, 2500, 3000, 3500, 4000, 4500, 5000, 5500, 6000, 6500, 7000, 7500, 8000];
          const closest = validCounts.reduce((prev, curr) => 
            Math.abs(curr - count) < Math.abs(prev - count) ? curr : prev
          );
          return closest as 250 | 500 | 800 | 1200 | 1500 | 2000 | 2500 | 3000 | 3500 | 4000 | 4500 | 5000 | 5500 | 6000 | 6500 | 7000 | 7500 | 8000;
        };
        
        const targetWordCount = getValidWordCount(sectionSettings.wordCount);
        
        const enhanced = await enhancementService.enhance({
          content: section.content,
          targetWordCount,
          model: sectionSettings.modelType,          // Pass full location context for more accurate content generation
          // IMPORTANT: Ensure fullPath is passed with priority over hierarchyPath
          locationContext: {
            id: location.id,
            name: location.name,
            // Always set hierarchyPath for compatibility with older code
            hierarchyPath: locationWithPath.hierarchyPath,
            // Pass fullPath with priority - using locationWithPath.fullPath which already includes correct fallbacks
            fullPath: locationWithPath.fullPath || '',
            // Support both naming conventions for feature_class
            type: location.feature_class || location.featureClass || 'unknown'
          },
          contextualData: {
            currentSection: {
              id: section.id,
              title: section.title,
              level: section.level,
              type: section.type,
              promptGuide: section.promptGuide,
              requirements: { wordCount: getValidWordCount(targetWordCount) }
            },
            siblingContexts: sections
              .filter(s => s.id !== section.id)
              .map(s => {
                // Get settings for this sibling section
                const siblingSettings = templateSettingsContext.getSettingsForSection(s.id);
                return {
                  title: s.title,
                  level: s.level,
                  type: s.type,
                  content: '', // Add empty content to satisfy type requirements
                  promptGuide: s.promptGuide || '',
                  requirements: { wordCount: getValidWordCount(siblingSettings.wordCount) }
                };
              })
          }
        });

        return {
          ...section,
          content: enhanced.enhancedContent
        };
      }));

      // CHECKPOINT: Translate content
      consoleStore.addMessage('info', '🌐 Translating content...');
      
      // Use language detection model if set, otherwise fallback to main model
      const modelForLanguageDetection = languageDetectionModel || selectedModel;
      
      // Send ALL sections together to be translated at once - this keeps languages grouped properly
      const translatedSections = await contentTranslationService.translateContent(
        enhancedSections,  // Send ALL sections together
        locationWithPath,
        modelForLanguageDetection,  // For language detection ONLY
        selectedModel  // For actual translation - ALWAYS use main model
      );

      // CHECKPOINT: Generate image
      consoleStore.addMessage('info', '🎨 Generating image...');
      
      // Get the user's prompt from the Control Matrix
      let userPrompt = '';
      if (imageGeneratorRef.current) {
        userPrompt = imageGeneratorRef.current.getPrompt();
        if (userPrompt && userPrompt.trim() !== '') {
          consoleStore.addMessage('info', `📝 Using your custom prompt from Control Matrix`);
        }
      }
      
      // In the imageGeneratorService, the 3rd parameter (promptGuide) has highest priority
      // Now passing location hierarchy path as 4th parameter for better location disambiguation
      const locationHierarchy = location.fullPath || location.hierarchyPath || '';
      const { imageBlob, imageUrl } = await imageGeneratorService.generateImage(
        "NOT_USED",        // Only used if promptGuide is empty
        location.name,     // Location name for [Location] replacement
        userPrompt,        // YOUR PROMPT from Control Matrix - this will be used
        locationHierarchy  // Full path for location disambiguation
      );

      if (!imageBlob) {
        throw new Error('Image generation failed');
      }

      // Show image preview in console
      consoleStore.addMessage('image', `Generated preview for ${location.name}`, {
        imageUrl,
        location: location.name,
        phase: 'preview',
        preview: true
      });

      // CHECKPOINT: Upload image
      consoleStore.addMessage('info', '📤 Uploading image to WordPress...');
      
      const mediaResponse = await wordpressService.media.uploadImage(
        activeSiteId,
        imageBlob,
        `${location.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}-featured-image.png`
      );

      if (!mediaResponse || !mediaResponse.id || !mediaResponse.source_url) {
        consoleStore.addMessage('error', '❌ Failed to upload image: Invalid media response');
        throw new Error('Invalid media response from WordPress');
      }

      // Show WordPress media URL and verification
      consoleStore.addMessage('success', `✅ Image uploaded successfully\n\nVerification Details:\n- Media ID: ${mediaResponse.id}\n- URL: ${mediaResponse.source_url}\n- File: ${mediaResponse.title.rendered}\n- Type: ${mediaResponse.media_type}`, {
        wordpressUrl: mediaResponse.source_url,
        location: location.name,
        phase: 'upload'
      });

      // CHECKPOINT: Create post
      consoleStore.addMessage('info', '📝 Creating WordPress post...', { phase: 'publishing' });
      
      const post = await wordpressService.posts.createSinglePost(
        activeSiteId,
        location.id,
        {
          title: processedTitle,
          translations: translatedSections,
          featuredMediaId: mediaResponse.id,
          headerShortcode: headerShortcode,
          footerShortcode: footerShortcode
        }
      );

      if (!post || !post.id || !post.link) {
        consoleStore.addMessage('error', '❌ Failed to create post: Invalid post response');
        throw new Error('Invalid post response from WordPress');
      }

      // Show WordPress post URL and full verification
      consoleStore.addMessage('success', `✅ Post published successfully!\n\nVerification Details:\n- Post ID: ${post.id}\n- Title: ${post.title.rendered}\n- Status: ${post.status}\n- Featured Image ID: ${post.featured_media}\n- Category: ${post.categories.join(', ')}\n- URL: ${post.link}\n\nPost can be viewed at: ${post.link}`, {
        wordpressUrl: post.link,
        location: location.name,
        phase: 'complete'
      });

      // Final verification summary
      consoleStore.addMessage('success', `🎉 FULL VERIFICATION REPORT for ${location.name}:\n\n1. Image Generation ✅\n   - Preview available above\n\n2. WordPress Media Upload ✅\n   - ID: ${mediaResponse.id}\n   - URL: ${mediaResponse.source_url}\n\n3. WordPress Post Creation ✅\n   - ID: ${post.id}\n   - URL: ${post.link}\n   - Status: ${post.status}\n\nAll steps completed successfully! Click the URLs above to verify the content on WordPress.`, {
        phase: 'verification'
      });

      // Cleanup preview URL
      URL.revokeObjectURL(imageUrl);
      return post;

    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      consoleStore.addMessage('error', `❌ Failed to process ${location.name}: ${errorMessage}`);
      throw error;
    }
  };

  const handleLaunchMass = async () => {
    const consoleStore = useConsoleStore.getState();
    const locations = Array.from(selectedLocations.values());
    
    try {
      // CHECKPOINT: Pre-flight validation
      if (!activeSiteId) {
        consoleStore.addMessage('error', '❌ No WordPress site selected');
        return;
      }

      if (sections.length === 0) {
        consoleStore.addMessage('error', '❌ No content sections defined');
        return;
      }

      if (locations.length === 0) {
        consoleStore.addMessage('error', '❌ No locations selected');
        return;
      }

      // CHECKPOINT: Queue Initialization
      consoleStore.addMessage('info', `🚀 Starting batch processing for ${locations.length} locations...`);
      
      // Add locations to batch processor queue for potential resume later
      await batchProcessor.addLocations(locations);
      
      // Explicitly save the state so we can resume later if needed
      batchProcessor.saveStateWithSiteId(activeSiteId);
        setHasSavedCampaign(true);
      
      const successfulPosts = [];
      const failedLocations = [];

      for (let i = 0; i < locations.length; i++) {
        const location = locations[i];
        
        // CHECKPOINT: Location Processing
        consoleStore.addMessage('info', `\n📍 Processing location ${i + 1}/${locations.length}: ${location.name}`);
        
        try {
          const post = await handleLaunchSingle(location);
          if (post?.link) {
            successfulPosts.push({ location, link: post.link });
          }
          consoleStore.addMessage('success', `✅ Successfully processed ${location.name}`);

          // Small delay between locations
          if (i < locations.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        } catch (error: any) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          failedLocations.push({ location, error: errorMessage });
          consoleStore.addMessage('error', `❌ Failed to process ${location.name}: ${errorMessage}`);
          
          // CHECKPOINT: Error Recovery
          consoleStore.addMessage('info', '⏳ Waiting before next location...');
          await new Promise(resolve => setTimeout(resolve, 2000)); // Longer delay after error
          continue;
        }

        // CHECKPOINT: Inter-location Delay
        if (i < locations.length - 1) {
          consoleStore.addMessage('info', '⏳ Preparing for next location...');
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // CHECKPOINT: Queue Completion
      consoleStore.addMessage('info', '\n📊 Queue Processing Summary:');
      consoleStore.addMessage('success', `✅ Successfully processed: ${successfulPosts.length}/${locations.length} locations`);
      
      if (successfulPosts.length > 0) {
        consoleStore.addMessage('info', '🔗 Successfully created posts:');
        successfulPosts.forEach(({ location, link }) => {
          consoleStore.addMessage('success', `   • ${location.name}: ${link}`);
        });
      }

      if (failedLocations.length > 0) {
        consoleStore.addMessage('error', '❌ Failed locations:');
        failedLocations.forEach(({ location, error }) => {
          consoleStore.addMessage('error', `   • ${location.name}: ${error}`);
        });
      }

    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      consoleStore.addMessage('error', `❌ Queue processing failed: ${errorMessage}`);
      throw error;
    }
  };

  const handlePauseBatch = () => {
    batchProcessor.pause();
  };

  const handleResumeBatch = () => {
    batchProcessor.resume();
  };

  const handleStopBatch = () => {
    batchProcessor.stop();
  };

  const handleAddSection = () => {
    const newSection: Section = {
      id: Date.now().toString(),
      level: 'h2',
      title: '',
      content: '',
      type: 'text',
      minWordCount: 1500,
      children: [],
    };
    setSections([...sections, newSection]);
  };

  const handleUpdateSection = (id: string, updates: Partial<Section>) => {
    const updateSection = (sections: Section[]): Section[] => {
      return sections.map((section) => {
        if (section.id === id) {
          return { ...section, ...updates };
        }
        if (section.children && section.children.length > 0) {
          return {
            ...section,
            children: updateSection(section.children),
          };
        }
        return section;
      });
    };

    setSections(updateSection(sections));
  };

  const handleDeleteSection = (id: string) => {
    const deleteSection = (sections: Section[]): Section[] => {
      return sections
        .filter((section) => section.id !== id)
        .map((section) => {
          if (section.children && section.children.length > 0) {
            return {
              ...section,
              children: deleteSection(section.children),
            };
          }
          return section;
        });
    };

    setSections(deleteSection(sections));
  };

  const handleAddSubsection = (parentId: string) => {
    // Find the section
    const addSubsection = (sections: Section[]): Section[] => {
      return sections.map(section => {
        if (section.id === parentId) {
          return {
            ...section,
            children: [
              ...(section.children || []),
              {
                id: Date.now().toString(),
                title: '',
                level: 'h3',
                type: 'text',
                content: '',
                promptGuide: '',
                requirements: {
                  wordCount: 250
                },
                children: []
              } as Section
            ]
          };
        } else if (section.children && section.children.length > 0) {
          return {
            ...section,
            children: addSubsection(section.children)
          };
        }
        return section;
      });
    };
    
    setSections(addSubsection(sections));
  };

  // Add state for tracking section movement
  const [movingSection, setMovingSection] = useState<string | null>(null);

  /**
   * Moves a section up or down in the sections array
   * @param id Section ID to move
   * @param direction 'up' or 'down'
   */
  const handleMoveSection = (id: string, direction: 'up' | 'down') => {
    // Find the section index
    const sectionIndex = sections.findIndex(section => section.id === id);
    
    // Safety checks to prevent errors
    if (sectionIndex === -1 ||
       (direction === 'up' && sectionIndex === 0) ||
       (direction === 'down' && sectionIndex === sections.length - 1)) {
      return;
    }
    
    // Visual feedback - set moving state
    setMovingSection(id);
    
    // Create a new array and determine target position
    const newSections = [...sections];
    const targetIndex = direction === 'up' ? sectionIndex - 1 : sectionIndex + 1;
    
    // Swap the sections using array destructuring
    [newSections[sectionIndex], newSections[targetIndex]] = 
      [newSections[targetIndex], newSections[sectionIndex]];
    
    // Update state with the new order
    setSections(newSections);
    
    // Add visual feedback using the console
    useConsoleStore.getState().addMessage('info', 
      `Section "${sections[sectionIndex].title || 'Untitled'}" moved ${direction}`);
    
    // Reset moving state after a short delay (for visual feedback)
    setTimeout(() => {
      setMovingSection(null);
      
      // Add highlight effect to the moved section
      const sectionElement = document.getElementById(`section-${id}`);
      if (sectionElement) {
        sectionElement.classList.add('section-moved');
        setTimeout(() => sectionElement.classList.remove('section-moved'), 800);
      }
    }, 100);
  };

  const [activeTab, setActiveTab] = useState<'wordpress' | 'templates'>('wordpress');
  const [currentTemplate, setCurrentTemplate] = useState<ContentTemplate | null>(null);
  // Add template modal states
  const [showSaveTemplateModal, setShowSaveTemplateModal] = useState(false);
  const [templateName, setTemplateName] = useState('');
  const [templateDescription, setTemplateDescription] = useState('');
  // Add version modal states
  const [showSaveVersionModal, setShowSaveVersionModal] = useState(false);
  const [changeDescription, setChangeDescription] = useState('');
  const [changeType, setChangeType] = useState<'major' | 'minor' | 'patch'>('minor');

  // Function to create a new blank template
  const handleCreateNewTemplate = () => {
    // Create a new empty section
    const newSection: Section = {
      id: Date.now().toString(),
      level: 'h2',
      title: '',
      content: '',
      type: 'text',
      minWordCount: 500,
      children: [],
    };
    
    // Reset all template-related state
    setSections([newSection]);
    setTitle("New Template Title");
    setHeaderShortcode("");
    setFooterShortcode("");
    setCurrentTemplate(null);
    
    // Clear image generator prompt if ref exists
    if (imageGeneratorRef.current) {
      imageGeneratorRef.current.setPrompt("");
    }
    
    // Show message to user
    useConsoleStore.getState().addMessage('info', 'Created new blank template. Add content and customize as needed.');
  };

  // Add template handling functions
  const handleSaveAsTemplate = async () => {
    if (sections.length === 0) return;

    const newTemplate = {
      id: '',
      name: `Template ${new Date().toLocaleDateString()}`,
      description: 'Template created from current content',
      tags: [],
      title,
      titlePromptGuide: currentTemplate?.titlePromptGuide || '',
      sections: sections.map(section => ({
        ...section,
        // Add required properties for TemplateSection
        requirements: {
          wordCount: section.minWordCount || 500
        },
        promptGuide: section.promptGuide || '' // Ensure this is never undefined
      })),
      imagePromptGuide: currentTemplate?.imagePromptGuide || multiLocationTemplate.imagePromptGuide || '',
      metadata: {
        useCount: 0,
        wordCount: sections.reduce((total, section) => total + (section.minWordCount || 0), 0),
        sectionCount: sections.length
      },
      globalRequirements: {
        tone: currentTemplate?.globalRequirements?.tone || 'professional',
        style: currentTemplate?.globalRequirements?.style || 'informative',
        compliance: currentTemplate?.globalRequirements?.compliance || []
      }
    };

    try {
      const saved = await templateService.createTemplate(newTemplate);
      setCurrentTemplate(saved);
      // Add this line to trigger template list refresh
      if (typeof onTemplateSaved === 'function') {
        onTemplateSaved(saved);
      }
      useConsoleStore.getState().addMessage('success', 'Template saved successfully');
    } catch (error: any) {
      console.error('Template save error:', error);
      useConsoleStore.getState().addMessage('error', `Failed to save template: ${error.message || 'Unknown error'}`);
    }
  };

  // Update the saveAsDefaultTemplate function to use the modal inputs
  const saveAsDefaultTemplate = async () => {
    if (sections.length === 0) return;
    
    try {
      // Get the current image prompt from the image generator if available
      const imagePrompt = imageGeneratorRef.current?.getPrompt() || '';
      
      // First create a proper template in the template system
      const templateToSave = {
        name: templateName,
        description: templateDescription,
        tags: ['default', selectedLocations.size > 1 ? 'multi-location' : 'single-location'],
        title,
        titlePromptGuide: currentTemplate?.titlePromptGuide || '',
        sections: sections.map(section => ({
          ...section,
          // Transform to match template section structure
          requirements: {
            wordCount: section.minWordCount || 500
          }
        })),
        imagePromptGuide: imagePrompt || currentTemplate?.imagePromptGuide || multiLocationTemplate.imagePromptGuide || '',
        headerShortcode,
        footerShortcode,
        metadata: {
          useCount: 0,
          wordCount: sections.reduce((total, section) => total + (section.minWordCount || 0), 0),
          sectionCount: sections.length,
          locationCompatibility: {
            levels: ['country', 'state', 'city']  // Default to all levels
          }
        },
        globalRequirements: {
          tone: currentTemplate?.globalRequirements?.tone || 'professional',
          style: currentTemplate?.globalRequirements?.style || 'informative',
          compliance: currentTemplate?.globalRequirements?.compliance || []
        }
      };
      
      // Create the template
      const savedTemplate = await templateService.createTemplate(templateToSave);
      
      // Now set this as the default template for the appropriate type
      await templateService.setDefaultTemplate(savedTemplate.id, selectedLocations.size > 1 ? 'multi' : 'single');
      
      // Update UI state
      setCurrentTemplate(savedTemplate);
      
      // Also save to localStorage for redundancy (just like the Prompt Builder does)
      localStorage.setItem('saved_default_header_shortcode', headerShortcode);
      localStorage.setItem('saved_default_footer_shortcode', footerShortcode);
      localStorage.setItem('saved_default_title_format', title);
      localStorage.setItem('saved_default_image_prompt', imagePrompt);
      
      // Show success message with specific details to confirm to user
      useConsoleStore.getState().addMessage('success', 
        `✅ Template "${templateName}" saved with ${sections.length} sections, header/footer shortcodes, title format, and image prompt!`
      );
      
      // Reset modal state
      setShowSaveTemplateModal(false);
      
      console.log('Default template saved and registered:', savedTemplate);
    } catch (error: any) {
      console.error('Error saving default template:', error);
      useConsoleStore.getState().addMessage('error', 
        `Failed to save default template: ${error.message || 'Unknown error'}`
      );
    }
  };

  // Add a function to handle loading a template
  const handleLoadTemplate = async (template: ContentTemplate | any) => {
    if (!template) {
      useConsoleStore.getState().addMessage('error', 'Template is empty or invalid');
      return;
    }
    
    // Load title from template or from localStorage if available
    const savedTitleFormat = localStorage.getItem('saved_default_title_format');
    setTitle(savedTitleFormat || template.title || '');
    
    // Load shortcodes from template or from localStorage if available
    const savedHeaderShortcode = localStorage.getItem('saved_default_header_shortcode');
    if (savedHeaderShortcode) {
      setHeaderShortcode(savedHeaderShortcode);
    } else if (template.headerShortcode) {
      setHeaderShortcode(template.headerShortcode);
    }
    
    const savedFooterShortcode = localStorage.getItem('saved_default_footer_shortcode');
    if (savedFooterShortcode) {
      setFooterShortcode(savedFooterShortcode);
    } else if (template.footerShortcode) {
      setFooterShortcode(template.footerShortcode);
    }
    
    // Load the image prompt if available
    const savedImagePrompt = localStorage.getItem('saved_default_image_prompt');
    if (savedImagePrompt && imageGeneratorRef.current) {
      imageGeneratorRef.current.setPrompt(savedImagePrompt);
    } else if (template.imagePromptGuide && imageGeneratorRef.current) {
      imageGeneratorRef.current.setPrompt(template.imagePromptGuide);
    }
    
    // Convert TemplateSection objects to Section objects with the required structure
    if (Array.isArray(template.sections)) {
      const convertedSections = template.sections.map(templateSection => {
        // Create a proper Section object from each TemplateSection
        const section: Section = {
          id: templateSection.id,
          level: templateSection.level as 'h1' | 'h2' | 'h3',
          title: templateSection.title,
          // Initialize with empty content that will be filled by the editor
          content: templateSection.content || '',
          // Ensure type is one of the expected values
          type: templateSection.type as 'text' | 'bulletList' | 'orderedList',
          // Get word count requirement or default to 500
          minWordCount: (templateSection.requirements?.wordCount || templateSection.minWordCount || 500) as 250 | 500 | 800 | 1200 | 1500 | 2000 | 2500 | 3000 | 3500 | 4000 | 4500 | 5000 | 5500 | 6000 | 6500 | 7000 | 7500 | 8000,
          // Copy the prompt guide if available
          promptGuide: templateSection.promptGuide,
          // Initialize empty children array
          children: templateSection.children || [],
        };
        return section;
      });
      
      setSections(convertedSections);
    } else {
      setSections([]);
    }
    
    setCurrentTemplate(template);
    
    useConsoleStore.getState().addMessage('success', `📋 Loaded template: ${template.name || template.title || 'Unnamed template'}`);
  };

  // Add this query to get the template
  const { data: defaultPrompt = "Create a professional image of OilWell Cannabis products in [Location] that captures our premium brand identity and local character.", refetch: refetchPrompt } = useQuery({
    queryKey: ['defaultPrompt'],
    queryFn: async () => {
      const response = await fetch(`${API_BASE_URL}/api/default-prompt`);
      if (!response.ok) {
        throw new Error('Failed to fetch default prompt');
      }
      const data = await response.json();
      return data.prompt;
    }
  });

  const [isEditingPrompt, setIsEditingPrompt] = useState(false);
  const [editedPrompt, setEditedPrompt] = useState('');

  // Add this mutation hook near other hooks
  const savePromptMutation = useMutation({
    mutationFn: async (newPrompt: string) => {
      const response = await fetch(`${API_BASE_URL}/api/default-prompt`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt: newPrompt })
      });
      if (!response.ok) {
        throw new Error('Failed to update prompt');
      }
      return response.json();
    },
    onSuccess: async () => {
      // Invalidate and refetch to update the UI
      await queryClient.invalidateQueries({ queryKey: ['defaultPrompt'] });
      await refetchPrompt();
      useConsoleStore.getState().addMessage('success', 'Default prompt updated');
      setIsEditingPrompt(false);
    },
    onError: (error: any) => {
      useConsoleStore.getState().addMessage('error', `Failed to update default prompt: ${error.message}`);
    }
  });

  const handleSavePrompt = () => {
    savePromptMutation.mutate(editedPrompt);
  };

  const handleSectionSelect = (section: Section) => {
    setCurrentSection(section);
  };

  // Update the startup template loading useEffect with better async handling
  useEffect(() => {
    let isMounted = true; // Track if component is mounted
    
    const initializeWithTemplate = async () => {
      try {
        // Before anything else, try to migrate any legacy templates
        if (isMounted) await templateService.migrateLocalStorageTemplates();
        
        // Only load template if no sections are already loaded and component still mounted
        if (sections.length === 0 && isMounted) {
          try {
            // Try to load the user's default template if they have one
            const defaultTemplate = await templateService.getDefaultTemplate('multi');
            
            if (defaultTemplate && isMounted) {
              // Load the user's custom template
              handleLoadTemplate(defaultTemplate);
              useConsoleStore.getState().addMessage('success', `Loaded your custom template`);
            } else if (isMounted) {
              // Check if this user has ANY templates
              const userTemplates = await templateService.getAllTemplates();
              
              if (userTemplates.length > 0) {
                // Existing user with templates but no default - create blank template
                handleCreateNewTemplate();
                useConsoleStore.getState().addMessage('info', 'Created a new blank template');
              } else {
                // New user - create a minimal starter template
                handleCreateNewTemplate();
                useConsoleStore.getState().addMessage('info', 'Welcome! Created a blank template to get you started.');
              }
            }
          } catch (error) {
            console.error('Error loading template:', error);
            if (isMounted) {
              // On error, just create a blank template
              handleCreateNewTemplate();
              useConsoleStore.getState().addMessage('warning', 'Error loading templates, created a blank template instead');
            }
          }
        }
      } catch (error) {
        console.error('Error initializing template:', error);
        // If still mounted, just create a blank template
        if (isMounted) {
          handleCreateNewTemplate();
          useConsoleStore.getState().addMessage('warning', 'Error initializing, created a blank template');
        }
      }
    };
    
    // Start initialization
    initializeWithTemplate();
    
    // Cleanup function
    return () => {
      isMounted = false; // Prevent state updates after unmount
    };
  }, []);

  const [processingQueue, setProcessingQueue] = useState<Location[]>([]);
  const [isProcessingQueue, setIsProcessingQueue] = useState(false);
  const [currentProcessingIndex, setCurrentProcessingIndex] = useState(0);

  const validateQueueRequirements = () => {
    const consoleStore = useConsoleStore.getState();
    const { activeSiteId } = usePandemicStore();
    
    if (!imageGeneratorRef.current) {
      consoleStore.addMessage('error', '❌ Image generator not initialized');
      throw new Error('Image generator not available');
    }

    if (!activeSiteId) {
      consoleStore.addMessage('error', '❌ No WordPress site selected');
      throw new Error('WordPress site not selected');
    }

    if (sections.length === 0) {
      consoleStore.addMessage('error', '❌ No content sections defined');
      throw new Error('Content sections required');
    }
  };

  const processLocationQueue = async (locations: Location[]) => {
    const consoleStore = useConsoleStore.getState();
    const { activeSiteId } = usePandemicStore();
    
    try {
      // CHECKPOINT: Queue Initialization
      consoleStore.addMessage('info', `🚀 Initializing queue processing for ${locations.length} locations`);
      validateQueueRequirements();

      if (!activeSiteId) {
        consoleStore.addMessage('error', '❌ No WordPress site selected');
        throw new Error('WordPress site not selected');
      }

      // Store current prompt from Control Matrix for batch processing
      let controlMatrixPrompt = '';
      
      // Get a local reference to avoid circular dependency
      const imageGenerator = imageGeneratorRef.current;
      if (imageGenerator) {
        controlMatrixPrompt = imageGenerator.getPrompt();
        
        if (controlMatrixPrompt && controlMatrixPrompt.trim() !== '') {
          // Show an excerpt of the prompt (first 50 chars) to confirm it's capturing correctly
          const promptExcerpt = controlMatrixPrompt.length > 50 ? 
            controlMatrixPrompt.substring(0, 50) + '...' : controlMatrixPrompt;
            
          consoleStore.addMessage('info', `📋 BATCH MODE: Using custom prompt from Control Matrix`);
          consoleStore.addMessage('info', `📋 PROMPT: "${promptExcerpt}"`);
          consoleStore.addMessage('info', `📋 Will be used for all ${locations.length} locations`);
          addDebugLog(`Using Control Matrix prompt for batch: ${controlMatrixPrompt}`);
        } else {
          consoleStore.addMessage('info', `⚠️ BATCH MODE: No custom prompt detected in Control Matrix`);
          consoleStore.addMessage('info', `⚠️ Will use template defaults - add text to Control Matrix for custom prompts`);
        }
      } else {
        consoleStore.addMessage('warning', `⚠️ BATCH MODE: Image generator not initialized`);
        consoleStore.addMessage('warning', `⚠️ Will use template defaults - try refreshing the page`);
      }
      
      const successfulPosts: Array<{ location: Location; link: string }> = [];
      const failedLocations: Array<{ location: Location; error: string }> = [];

      for (let i = 0; i < locations.length; i++) {
        if (!isProcessingQueue) {
          consoleStore.addMessage('warning', '⚠️ Processing stopped by user');
          break;
        }

        const location = locations[i];
        setCurrentProcessingIndex(i);
        
        try {
          consoleStore.addMessage('info', `\n📍 Processing location ${i + 1}/${locations.length}: ${location.name}`);
          
          await processLocation(location.id);
          
          successfulPosts.push({ location, link: 'TODO' });
          consoleStore.addMessage('success', `✅ Successfully processed ${location.name}`);

          // Small delay between locations
          if (i < locations.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        } catch (error: any) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          failedLocations.push({ location, error: errorMessage });
          consoleStore.addMessage('error', `❌ Failed to process ${location.name}: ${errorMessage}`);
          
          // Continue with next location after error
          await new Promise(resolve => setTimeout(resolve, 2000));
          continue;
        }

        // CHECKPOINT: Inter-location Delay
        if (i < locations.length - 1) {
          consoleStore.addMessage('info', '⏳ Preparing for next location...');
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // CHECKPOINT: Queue Completion
      consoleStore.addMessage('info', '\n📊 Queue Processing Summary:');
      consoleStore.addMessage('success', `✅ Successfully processed: ${successfulPosts.length}/${locations.length} locations`);
      
      if (successfulPosts.length > 0) {
        consoleStore.addMessage('info', '🔗 Successfully created posts:');
        successfulPosts.forEach(({ location, link }) => {
          consoleStore.addMessage('success', `   • ${location.name}: ${link}`);
        });
      }

      if (failedLocations.length > 0) {
        consoleStore.addMessage('error', '❌ Failed locations:');
        failedLocations.forEach(({ location, error }) => {
          consoleStore.addMessage('error', `   • ${location.name}: ${error}`);
        });
      }

    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      consoleStore.addMessage('error', `❌ Queue processing failed: ${errorMessage}`);
      throw error;
    }
  };

  // Combined effect to handle image generator initialization and updates
  useEffect(() => {
    // Skip if no image generator
    if (!imageGeneratorRef.current) {
      addDebugLog('Image generator not initialized');
      return;
    }

    // Skip if no sections
    if (!sections || sections.length === 0) {
      addDebugLog('No sections available');
      return;
    }

    // Find section with image prompt guide
    const section = sections.find(s => s.imagePromptGuide);
    if (!section) {
      addDebugLog('No section with image prompt guide found');
      return;
    }

    // Do nothing - don't set any prompts
    
  }, [sections]); // Only depend on sections changes

  // Separate effect for location selection
  useEffect(() => {
    if (!selectedLocations?.size) {
      return;
    }

    const locationNames = Array.from(selectedLocations).map(l => l.name).join(', ');
    addDebugLog(`Selected locations changed: ${locationNames}`);

    // Do nothing - don't set any prompts
    
  }, [selectedLocations]); // Only depend on selectedLocations changing

  useEffect(() => {
    if (selectedLocations.size > 0 && imageGeneratorRef.current) {
      const allLocations = Array.from(selectedLocations.values());
      const leafLocation = allLocations.find(location => 
        !allLocations.some(otherLocation => 
          otherLocation.parentId === location.id
        )
      );

      // Do nothing - don't set any prompts
      
    }
  }, [selectedLocations]);

  // Get template settings context
  const templateSettingsContext = useContext(TemplateSettingsContext);

  const [campaign, setCampaign] = useState(null);
  const [uploadStatus, setUploadStatus] = useState<{ [key: string]: 'pending' | 'uploading' | 'success' | 'error' | 'skipped' }>({});
  const [uploadErrors, setUploadErrors] = useState<{ [key: string]: string }>({});
  const [tempId, setTempId] = useState(1);

  // Helper functions for model validation
  const isModelSelected = () => {
    return !!selectedModel && selectedModel !== '';
  };

  const validateModelSelection = () => {
    if (!isModelSelected()) {
      useConsoleStore.getState().addMessage('error', 
        '❌ OPERATION BLOCKED: No AI model selected. You MUST select a model first.');
      return false;
    }
    return true;
  };

  // Helper function for language detection model validation
  const validateLanguageDetectionModel = () => {
    const effectiveModel = languageDetectionModel || selectedModel;
    if (!effectiveModel || effectiveModel === '') {
      useConsoleStore.getState().addMessage('error', '❌ No language detection model selected. Please select a content model or dedicated language detection model.');
      return false;
    }
    return true;
  };

  // Load models on mount
  useEffect(() => {
    const loadModels = async () => {
      setIsLoadingModels(true);
      
      // Get models from system settings first
      const settings = getSettings();
      const initialModels = settings.models.options || {};
      setAvailableModels(initialModels);
      
      // DO NOT set any default model - user must explicitly choose
      
      // Try to detect and load LM Studio models
      try {
        const lmStudioUrl = await lmStudioDetectionService.detectLMStudio();
        if (lmStudioUrl) {
          const lmModels = await lmStudioDetectionService.getAvailableModels(lmStudioUrl);
          
          // Add LM Studio models with proper prefix
          const updatedModels = { ...initialModels };
          lmModels.forEach(model => {
            const modelId = `lmstudio:${model.id}`;
            updatedModels[modelId] = {
              id: modelId,
              name: `LM Studio: ${model.id}`,
              description: 'LM Studio local model'
            };
          });
          
          setAvailableModels(updatedModels);
        }
      } catch (error) {
        console.error('Error loading LM Studio models:', error);
        useConsoleStore.getState().addMessage('error', `Failed to load LM Studio models: ${error}`);
      }

      // Try to detect and load Ollama models
      try {
        const ollamaUrl = await ollamaDetectionService.detectOllama();
        if (ollamaUrl) {
          const ollamaModels = await ollamaDetectionService.getAvailableModels(ollamaUrl);
          
          // Add Ollama models with proper prefix
          setAvailableModels(prev => {
            const updatedModels = { ...prev };
            ollamaModels.forEach(model => {
              const modelId = `ollama:${model.name}`;
              updatedModels[modelId] = {
                id: modelId,
                name: `Ollama: ${model.name}`,
                description: `Ollama model - ${model.name}`
              };
            });
            return updatedModels;
          });
        }
      } catch (error) {
        console.error('Error loading Ollama models:', error);
        useConsoleStore.getState().addMessage('error', `Failed to load Ollama models: ${error}`);
      }
      
      // Try to load OpenRouter models if API key is configured
      if (openRouterService.isConfigured()) {
        try {
          const openRouterModels = await openRouterService.getFormattedModels();
          // Directly add OpenRouter models to the availableModels state
          setAvailableModels(prevModels => ({
            ...prevModels,
            ...openRouterModels
          }));
          useConsoleStore.getState().addMessage('success', '✅ OpenRouter models loaded successfully');
        } catch (error) {
          console.error('Error loading OpenRouter models:', error);
          useConsoleStore.getState().addMessage('error', `Failed to load OpenRouter models: ${error}`);
        }
      }
      
      // Try to load Anthropic models via server-side API
      try {
        // Request Anthropic models from server
        const response = await fetch(`${API_BASE_URL}/api/anthropic/models`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch Anthropic models: ${response.statusText}`);
        }
        
        const anthropicModels = await response.json();
        
        // Directly add Anthropic models to the availableModels state
        setAvailableModels(prevModels => ({
          ...prevModels,
          ...anthropicModels
        }));
        
        useConsoleStore.getState().addMessage('success', '✅ Anthropic models loaded successfully');
      } catch (error) {
        console.error('Error loading Anthropic models:', error);
        useConsoleStore.getState().addMessage('error', `Failed to load Anthropic models: ${error}`);
      }
      
      setIsLoadingModels(false);
    };
    
    loadModels();
  }, []);

  // Handle model change
  const handleModelChange = (modelId: string) => {
    if (!modelId || modelId === '') {
      useConsoleStore.getState().addMessage('error', '❌ ERROR: Invalid model selection');
      return;
    }
    
    // Update selected model only
    setSelectedModel(modelId);
    
    // Update template settings global model type
    templateSettingsContext.setGlobalModelType(modelId);
    
    // Apply the model to all existing sections to ensure consistency
    sections.forEach(section => {
      templateSettingsContext.setModelType(section.id, modelId);
    });
    
    // CRITICAL FIX: Update system settings to ensure all services use the same model
    const settings = getSettings();
    settings.models.current = modelId;
    saveSettings(settings);
    
    // Get model name for logging
    const modelName = availableModels[modelId]?.name || modelId;
    useConsoleStore.getState().addMessage('success', 
      `✅ AI model set to: ${modelName} (used for ALL operations)`
    );
  };

  // Handle language detection model change
  const handleLanguageDetectionModelChange = (modelId: string) => {
    // Simply update the language detection model
    // No need to update template settings or system settings
    setLanguageDetectionModel(modelId);
    
    if (modelId) {
      useConsoleStore.getState().addMessage('info', `✅ Language detection model set to: ${availableModels[modelId]?.name || modelId}`);
    } else {
      useConsoleStore.getState().addMessage('info', '✅ Language detection model cleared - using main model');
    }
  };

  // Function to refresh available models
  const refreshModels = async () => {
    setIsLoadingModels(true);
    useConsoleStore.getState().addMessage('info', `🔍 Refreshing available models...`);
    
    try {
      // Try to auto-configure LM Studio
      await lmStudioDetectionService.autoConfigureLMStudio();
      
      // Try to auto-configure Ollama
      await ollamaDetectionService.autoConfigureOllama();
      
      // Reload settings and models
      const settings = getSettings();
      const updatedModels = settings.models.options || {};
      
      // Try to detect and load LM Studio models
      const lmStudioUrl = await lmStudioDetectionService.detectLMStudio();
      if (lmStudioUrl) {
        const lmModels = await lmStudioDetectionService.getAvailableModels(lmStudioUrl);
        
        // Add LM Studio models with proper prefix
        lmModels.forEach(model => {
          const modelId = `lmstudio:${model.id}`;
          updatedModels[modelId] = {
            id: modelId,
            name: `LM Studio: ${model.id}`,
            description: 'LM Studio local model'
          };
        });
      }
      
      // Try to detect and load Ollama models
      const ollamaUrl = await ollamaDetectionService.detectOllama();
      if (ollamaUrl) {
        const ollamaModels = await ollamaDetectionService.getAvailableModels(ollamaUrl);
        
        // Add Ollama models with proper prefix
        ollamaModels.forEach(model => {
          const modelId = `ollama:${model.name}`;
          updatedModels[modelId] = {
            id: modelId,
            name: `Ollama: ${model.name}`,
            description: `Ollama model - ${model.name}`
          };
        });
      }
      
      // Try to load OpenRouter models if API key is configured
      if (openRouterService.isConfigured()) {
        try {
          const openRouterModels = await openRouterService.getFormattedModels();
          // Directly add OpenRouter models to the updatedModels object
          Object.assign(updatedModels, openRouterModels);
          useConsoleStore.getState().addMessage('success', '✅ OpenRouter models refreshed successfully');
        } catch (error) {
          console.error('Error refreshing OpenRouter models:', error);
          useConsoleStore.getState().addMessage('error', `Failed to refresh OpenRouter models: ${error}`);
        }
      }
      
      setAvailableModels(updatedModels);
      useConsoleStore.getState().addMessage('success', `✅ Models refreshed successfully`);
    } catch (error) {
      console.error('Error refreshing models:', error);
      useConsoleStore.getState().addMessage('error', `Failed to refresh models: ${error}`);
    }
    
    setIsLoadingModels(false);
  };

  // Add effect to monitor batch processor state
  useEffect(() => {
    // Update the hasSavedCampaign state anytime the app loads
    const checkSavedState = () => {
      const savedState = localStorage.getItem('batchProcessorState');
      setHasSavedCampaign(!!savedState);
    };
    
    // Initial check
    checkSavedState();
    
    // Setup periodic check every 2 seconds
    const interval = setInterval(checkSavedState, 2000);
    
    // Listen for browser closing/navigating away
    const handleBeforeUnload = () => {
      if (batchProcessor.isProcessing) {
        // Auto-save state when user leaves
        batchProcessor.saveStateWithSiteId(activeSiteId || '');
      }
    };
    
    window.addEventListener('storage', handleBeforeUnload);
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      clearInterval(interval);
      window.removeEventListener('storage', handleBeforeUnload);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  // Original effect for batch progress
  useEffect(() => {
    const handleProgress = (progress: any) => {
      setBatchProgress(progress);
    };

    batchProcessor.on('progress', handleProgress);
    return () => {
      batchProcessor.off('progress', handleProgress);
    };
  }, []);

  // Add a function to handle showing the save version modal
  const handleShowSaveVersionModal = () => {
    if (sections.length === 0) {
      useConsoleStore.getState().addMessage('error', 'Please add at least one section before saving a version');
      return;
    }
    
    if (!currentTemplate) {
      useConsoleStore.getState().addMessage('error', 'No template is currently loaded. Please save as a new template first.');
      return;
    }
    
    setChangeDescription('');
    setChangeType('minor');
    setShowSaveVersionModal(true);
  };
  
  // Add a function to save the current content as a new version of the loaded template
  const saveAsNewVersion = async () => {
    if (!currentTemplate || sections.length === 0) return;
    
    try {
      // Get the current image prompt from the image generator if available
      const imagePrompt = imageGeneratorRef.current?.getPrompt() || '';
      
      // Update the existing template with current content
      const updates = {
        title,
        sections,
        imagePromptGuide: imagePrompt,
        headerShortcode,
        footerShortcode,
        // Make sure globalRequirements is included with current values or defaults
        globalRequirements: {
          tone: currentTemplate?.globalRequirements?.tone || 'professional',
          style: currentTemplate?.globalRequirements?.style || 'informative',
          compliance: currentTemplate?.globalRequirements?.compliance || []
        }
      };
      
      // Save as a new version of the current template
      const updatedTemplate = await templateService.updateTemplate(
        currentTemplate.id,
        updates,
        changeDescription,
        changeType
      );
      
      // Update UI state
      setCurrentTemplate(updatedTemplate);
      
      // Also save to localStorage for redundancy
      localStorage.setItem('saved_default_header_shortcode', headerShortcode);
      localStorage.setItem('saved_default_footer_shortcode', footerShortcode);
      localStorage.setItem('saved_default_title_format', title);
      localStorage.setItem('saved_default_image_prompt', imagePrompt);
      
      // Show success message with version info
      useConsoleStore.getState().addMessage('success', 
        `✅ Template "${updatedTemplate.name}" updated to version ${updatedTemplate.version}!`
      );
      
      // Reset modal state
      setShowSaveVersionModal(false);
      
      console.log('Template version saved:', updatedTemplate);
      
      // Trigger template list refresh if callback provided
      if (typeof onTemplateSaved === 'function') {
        onTemplateSaved(updatedTemplate);
      }
    } catch (error: any) {
      console.error('Error saving template version:', error);
      useConsoleStore.getState().addMessage('error', 
        `Failed to save template version: ${error.message || 'Unknown error'}`
      );
    }
  };

  // Create a ref for external access
  const contentEditorRef = useRef<ContentEditorRefType>({
    headerShortcode: '',
    footerShortcode: ''
  });
  
  // Make the ref globally available
  useEffect(() => {
    window.contentEditorRef = contentEditorRef;
    return () => {
      window.contentEditorRef = undefined;
    };
  }, []);

  // State for WordPress shortcodes
  const [headerShortcode, setHeaderShortcode] = useState('');
  const [footerShortcode, setFooterShortcode] = useState('');

  // Update ref values when shortcodes change
  useEffect(() => {
    if (contentEditorRef.current) {
      contentEditorRef.current.headerShortcode = headerShortcode;
      contentEditorRef.current.footerShortcode = footerShortcode;
    }
  }, [headerShortcode, footerShortcode]);

  // Add new function for the batch processor launch that was working before
  const handleLaunchBatchProcessor = async () => {
    const consoleStore = useConsoleStore.getState();
    const locations = Array.from(selectedLocations.values());
    
    // Pre-flight validation
    if (!activeSiteId) {
      consoleStore.addMessage('error', '❌ No WordPress site selected');
      return;
    }

    if (sections.length === 0) {
      consoleStore.addMessage('error', '❌ No content sections defined');
      return;
    }

    if (locations.length === 0) {
      consoleStore.addMessage('error', '❌ No locations selected');
      return;
    }

    consoleStore.addMessage('info', `🚀 Starting batch processing for ${locations.length} locations using batch processor...`);

    try {
      // Get the image prompt from the control matrix
      let imagePrompt = '';
      if (imageGeneratorRef.current) {
        imagePrompt = imageGeneratorRef.current.getPrompt();
      }

      // CRITICAL FIX: Ensure all locations have their full hierarchy path before processing
      // Build location hierarchies for EVERY location to ensure proper context
      consoleStore.addMessage('info', '🔄 Building full location hierarchies for all selected locations...');
      const locationsWithHierarchy = [];
      
      for (const loc of locations) {
        try {
          // Get the complete hierarchy for this location from geonamesApi
          const hierarchy = await geonamesApi.getLocationHierarchy(loc.id);
          const fullPath = hierarchy.map(l => l.name).join(' > ');
          
          consoleStore.addMessage('info', `✅ Built hierarchy for ${loc.name}: ${fullPath}`);
          
          // Add location with properly set hierarchy path
          locationsWithHierarchy.push({
            ...loc,
            hierarchyPath: fullPath,
            fullPath: fullPath
          });
        } catch (error) {
          consoleStore.addMessage('error', `❌ Failed to build hierarchy for ${loc.name}: ${error.message}`);
          // Still add the location but without enhanced path
          locationsWithHierarchy.push(loc);
        }
      }

      // Now create batch locations with guaranteed hierarchy paths
      const batchProcessorLocations = locationsWithHierarchy.map(loc => {
        return {
          id: loc.id,
          name: loc.name,
          featureClass: loc.featureClass || '',
          feature_code: loc.feature_code || '',
          country_code: loc.countryCode || '',
          countryCode: loc.countryCode || '',
          asciiname: loc.name,
          coordinates: {
            latitude: 0,
            longitude: 0
          },
          // Use the guaranteed hierarchy paths - no fallbacks needed
          hierarchyPath: loc.hierarchyPath,
          fullPath: loc.fullPath,
          disambiguationInfo: '',
          type: loc.featureClass || 'city',
          has_children: false
        };
      });

      // First enhance all sections
      const enhancedSections = await Promise.all(sections.map(async section => {
        // Skip enhancement for empty sections
        if (!section.content.trim()) {
          return section;
        }

        // For each location, we need to first enhance the content
        const locationContext = {
          id: 'batch-mode',
          name: 'Multiple Locations',
          hierarchyPath: 'Multiple Locations',
          fullPath: 'Multiple Locations',  // Add fullPath to ensure proper placeholder replacement
          type: 'city'
        };

        // Get settings for this section to determine which service to use
        const sectionSettings = templateSettingsContext.getSettingsForSection(section.id);
        
        // Select the appropriate service based on settings
        const enhancementService = sectionSettings.generationStyle === 'direct' ? 
                                  directOpenAIService : neuroEnhancerService;

        // Create a function to validate and convert wordCount to an acceptable value
        const getValidWordCount = (count: number): 250 | 500 | 800 | 1200 | 1500 | 2000 | 2500 | 3000 | 3500 | 4000 | 4500 | 5000 | 5500 | 6000 | 6500 | 7000 | 7500 | 8000 => {
          const validCounts = [250, 500, 800, 1200, 1500, 2000, 2500, 3000, 3500, 4000, 4500, 5000, 5500, 6000, 6500, 7000, 7500, 8000];
          const closest = validCounts.reduce((prev, curr) => 
            Math.abs(curr - count) < Math.abs(prev - count) ? curr : prev
          );
          return closest as 250 | 500 | 800 | 1200 | 1500 | 2000 | 2500 | 3000 | 3500 | 4000 | 4500 | 5000 | 5500 | 6000 | 6500 | 7000 | 7500 | 8000;
        };
        
        const targetWordCount = getValidWordCount(sectionSettings.wordCount);
        
        const enhanced = await enhancementService.enhance({
          content: section.content,
          targetWordCount,
          model: sectionSettings.modelType, // Pass the model from section settings
          locationContext: locationContext,
          contextualData: {
            currentSection: {
              id: section.id,
              title: section.title,
              level: section.level,
              type: section.type,
              promptGuide: section.promptGuide,
              requirements: { wordCount: getValidWordCount(targetWordCount) }
            },
            siblingContexts: sections
              .filter(s => s.id !== section.id)
              .map(s => {
                // Get settings for this sibling section
                const siblingSettings = templateSettingsContext.getSettingsForSection(s.id);
                return {
                  title: s.title,
                  level: s.level,
                  type: s.type,
                  content: '', // Add empty content to satisfy type requirements
                  promptGuide: s.promptGuide || '',
                  requirements: { wordCount: getValidWordCount(siblingSettings.wordCount) }
                };
              })
          }
        });

        return {
          ...section,
          content: enhanced.enhancedContent
        };
      }));

      // Start the batch processor with all needed configurations
      await batchProcessor.addLocations(batchProcessorLocations);
      batchProcessor.start(async (location: any, _) => {
        // For each location in the batch, perform translation
        // Build proper full hierarchical path using the same method as language detection
        let locationWithPath;
        try {
          const hierarchy = await geonamesApi.getLocationHierarchy(location.id);
          const fullPath = hierarchy.map(l => l.name).join(' > ');
          
          locationWithPath = {
            ...location,
            hierarchyPath: fullPath,
            fullPath: fullPath,
            has_children: false
          };
          
          console.log(`🔍 Batch processing ${location.name} with full path: ${fullPath}`);
          useConsoleStore.getState().addMessage('success', `✅ Built full hierarchy path for batch processing: ${fullPath}`);
        } catch (error) {
          console.error(`Failed to build hierarchy for ${location.name}:`, error);
          useConsoleStore.getState().addMessage('error', `Failed to build hierarchy for ${location.name}: ${error.message || 'Unknown error'}`);
          return; // Skip this location if hierarchy building fails
        }
        
        // Translate content for this specific location
        // FIXED: Use same logic as single mode - process ALL sections together
        const modelForLanguageDetection = languageDetectionModel || selectedModel;

        const translatedSections = await contentTranslationService.translateContent(
          enhancedSections,  // Send ALL sections together (same as single mode)
          locationWithPath,
          modelForLanguageDetection,  // For language detection ONLY
          selectedModel  // For actual translation - ALWAYS use main model (same as single mode)
        );
        
        return batchProcessor.processLocation(location, {
          siteId: activeSiteId,
          imagePrompt: imagePrompt,
          headerShortcode: headerShortcode,
          footerShortcode: footerShortcode,
          translations: translatedSections
        });
      });
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      consoleStore.addMessage('error', `❌ Batch processing error: ${errorMessage}`);
    }
  };

  const renderLanguageDetectionModelOptions = () => {
    // Group models by provider
    const modelsByProvider: Record<string, any[]> = {};
    
    Object.values(availableModels).forEach((model: any) => {
      // Skip if not a valid model object
      if (!model || typeof model !== 'object') return;
      
      // Skip group headers - we'll use optgroups instead
      if (model.isGroupHeader) return;
      
      // Apply filter if one exists
      if (languageDetectionModelFilter && 
          !(model.name.toLowerCase().includes(languageDetectionModelFilter) || 
            model.id.toLowerCase().includes(languageDetectionModelFilter))) {
        return; // Skip this model if it doesn't match the filter
      }
      
      // Determine provider
      let provider = 'other';
      
      if (model.id.startsWith('openai:')) provider = 'openai';
      else if (model.id.startsWith('openrouter:')) provider = 'openrouter';
      else if (model.id.startsWith('lmstudio:')) provider = 'lmstudio';
      else if (model.id.startsWith('anthropic:')) provider = 'anthropic';
      
      // Create the provider group if it doesn't exist
      if (!modelsByProvider[provider]) {
        modelsByProvider[provider] = [];
      }
      
      // Add model to its provider group
      modelsByProvider[provider].push(model);
    });
    
    // Define the order of providers and their display names
    const providerConfig = [
      { id: 'openai', label: 'OpenAI Models' },
      { id: 'openrouter', label: 'OpenRouter Models' },
      { id: 'anthropic', label: 'Anthropic Models' },
      { id: 'lmstudio', label: 'LM Studio Models' },
      { id: 'other', label: 'Other Models' }
    ];
    
    // Check if we have any models after filtering
    const hasModelsAfterFiltering = Object.values(modelsByProvider).some(models => models.length > 0);
    
    // Render options with proper optgroups by provider
    return (
      <>
        <option value="">-- DEFAULT (USE MAIN MODEL) --</option>
        
        {hasModelsAfterFiltering ? (
          providerConfig.map(provider => {
            const models = modelsByProvider[provider.id] || [];
            if (models.length === 0) return null;
            
            return (
              <optgroup key={provider.id} label={provider.label}>
                {models.map((model: any) => (
                  <option 
                    key={model.id} 
                    value={model.id}
                  >
                    {model.name}
                  </option>
                ))}
              </optgroup>
            );
          })
        ) : (
          languageDetectionModelFilter ? (
            <option disabled>No models match filter "{languageDetectionModelFilter}"</option>
          ) : null
        )}
      </>
    );
  };

  const renderModelOptions = () => {
    // Group models by provider
    const modelsByProvider: Record<string, any[]> = {};
    
    Object.values(availableModels).forEach((model: any) => {
      // Skip if not a valid model object
      if (!model || typeof model !== 'object') return;
      
      // Skip group headers - we'll use optgroups instead
      if (model.isGroupHeader) return;
      
      // Apply filter if one exists
      if (modelFilter && 
          !(model.name.toLowerCase().includes(modelFilter) || 
            model.id.toLowerCase().includes(modelFilter))) {
        return; // Skip this model if it doesn't match the filter
      }
      
      // Determine provider
      let provider = 'other';
      
      if (model.id.startsWith('openai:')) provider = 'openai';
      else if (model.id.startsWith('openrouter:')) provider = 'openrouter';
      else if (model.id.startsWith('lmstudio:')) provider = 'lmstudio';
      else if (model.id.startsWith('anthropic:')) provider = 'anthropic';
      
      // Create the provider group if it doesn't exist
      if (!modelsByProvider[provider]) {
        modelsByProvider[provider] = [];
      }
      
      // Add model to its provider group
      modelsByProvider[provider].push(model);
    });
    
    // Define the order of providers and their display names
    const providerConfig = [
      { id: 'openai', label: 'OpenAI Models' },
      { id: 'openrouter', label: 'OpenRouter Models' },
      { id: 'anthropic', label: 'Anthropic Models' },
      { id: 'lmstudio', label: 'LM Studio Models' },
      { id: 'other', label: 'Other Models' }
    ];
    
    // Check if we have any models after filtering
    const hasModelsAfterFiltering = Object.values(modelsByProvider).some(models => models.length > 0);
    
    // Render options with proper optgroups by provider
    return (
      <>
        <option value="">-- CHOOSE AI MODEL --</option>
        
        {hasModelsAfterFiltering ? (
          providerConfig.map(provider => {
            const models = modelsByProvider[provider.id] || [];
            if (models.length === 0) return null;
            
            return (
              <optgroup key={provider.id} label={provider.label}>
                {models.map((model: any) => (
                  <option 
                    key={model.id} 
                    value={model.id}
                  >
                    {model.name}
                  </option>
                ))}
              </optgroup>
            );
          })
        ) : (
          modelFilter ? (
            <option disabled>No models match filter "{modelFilter}"</option>
          ) : null
        )}
      </>
    );
  };

  // Add a function to handle showing the save template modal
  const handleShowSaveAsDefaultModal = () => {
    if (sections.length === 0) {
      useConsoleStore.getState().addMessage('error', 'Please add at least one section before saving a template');
      return;
    }
    setTemplateName(`Default ${selectedLocations.size > 1 ? 'Multi-Location' : 'Single Location'} Template`);
    setTemplateDescription(`My customized template for ${selectedLocations.size > 1 ? 'multiple' : 'single'} locations`);
    setShowSaveTemplateModal(true);
  };

  return (
    <div className="h-full flex flex-col space-y-4 p-4 overflow-y-auto custom-scrollbar">
      {/* Campaign Mode Indicator */}
      {(batchProgress.total > 0 || batchProgress.status !== 'idle') && (
        <div className="px-3 py-2 bg-plasma-500/10 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-plasma-300">
                Mass Campaign Mode
              </h3>
              <p className="text-xs text-plasma-300/70">
                {`${batchProgress.completed + batchProgress.failed} of ${batchProgress.total} locations processed`}
              </p>
            </div>
            <div className="text-xs text-plasma-300/70">
              <p>✓ Sequential processing</p>
              <p>✓ Batch translations</p>
              <p>✓ Progress tracking</p>
            </div>
          </div>
          {/* New: Content Generation Preview */}
          <div className="mt-3 p-3 bg-bolt-900/50 rounded border border-plasma-500/20">
            <h4 className="text-xs font-medium mb-2">🧠 Automatic Content Generation</h4>
            <div className="space-y-1 text-xs">
              <p className="text-plasma-300">1. Load template with placeholders ✓</p>
              <p className="text-plasma-300">2. For each location:</p>
              <p className="text-plasma-300/70 ml-4">→ Replace {[Location]} tags</p>
              <p className="text-plasma-300/70 ml-4">→ Generate unique content via NeuroEnhancer</p>
              <p className="text-plasma-300/70 ml-4">→ Create location-specific images</p>
              <p className="text-plasma-300/70 ml-4">→ Translate to local languages</p>
              <p className="text-plasma-300/70 ml-4">→ Publish to WordPress</p>
            </div>
          </div>
        </div>
      )}

      {/* CYBERPUNK COMMAND CENTER - Non-fixed version */}
      <div className="w-full bg-bolt-900/95 backdrop-blur-md border border-plasma-500/30 rounded-md mb-4">
        {/* Decorative energy line */}
        <div className="h-0.5 bg-gradient-to-r from-plasma-500/0 via-plasma-500 to-plasma-500/0"></div>
        
        {/* Command center controls - Made responsive */}
        <div className="h-12 flex items-center justify-between px-2 sm:px-5">
          {/* Left side: Command status with minimal design */}
          <div className="flex items-center">
            <div className={`h-3 w-3 rounded-full ${selectedLocations.size > 0 ? 'bg-energy-500 animate-pulse shadow-md shadow-energy-500/30' : 'bg-gray-600'} mr-3`}></div>
            
            {selectedLocations.size === 0 ? (
              <span className="text-xs font-mono text-red-400">NO TARGET</span>
            ) : selectedLocations.size === 1 ? (
              <span className="text-xs font-mono text-energy-400">{Array.from(selectedLocations.values())[0].name}</span>
            ) : (
              <span className="text-xs font-mono text-energy-400 flex items-center gap-1">
                <span className="hidden sm:inline">MULTI:</span> 
                <span className="text-energy-300 ml-1.5">{selectedLocations.size}</span>
                <span className="mx-2 text-plasma-200 hidden sm:inline">COMPLETE:</span>
                <span className="text-energy-300 hidden sm:inline">{Object.values(uploadStatus).filter(status => status === 'success').length}</span>
              </span>
            )}
          </div>
          
          {/* Right side: Mission control with simplified buttons */}
          <div className="flex items-center gap-2 sm:gap-3">
            {/* Single Location Launch Button */}
            <Button
              variant="secondary"
              onClick={() => {
                if (selectedLocations.size === 1) {
                  const location = Array.from(selectedLocations.values())[0];
                  handleLaunchSingle(location);
                } else {
                  useConsoleStore.getState().addMessage('error', '⚠️ ERROR: Single target launch requires exactly one location');
                }
              }}
              disabled={selectedLocations.size !== 1 || sections.length === 0}
              className="h-8 text-xs px-2 sm:px-3"
            >
              SINGLE
            </Button>
            
            {/* Mass Launch Button with Energy Effects */}
            <Button
              variant="energy"
              onClick={handleLaunchMass}
              disabled={selectedLocations.size <= 1 || sections.length === 0}
              className="h-8 text-xs px-2 sm:px-3"
            >
              LAUNCH {selectedLocations.size}
            </Button>
          </div>
        </div>
      </div>

      {/* Top row: WordPress/Templates collapsible sections - Made responsive */}
      <div className="mb-4 space-y-4">
        <CollapsibleSection 
          title="WordPress Sites" 
          defaultExpanded={false}
        >
          <WordPressSiteManager />
        </CollapsibleSection>
        
        <CollapsibleSection 
          title="Templates" 
          defaultExpanded={false}
        >
          <TemplateManager 
            onTemplateSelect={handleLoadTemplate}
            currentTemplate={currentTemplate}
            onTemplateSaved={(template) => {
              setCurrentTemplate(template);
            }}
          />
        </CollapsibleSection>
      </div>

      {/* Title Input - Made responsive */}
      <div className="title-container bg-bolt-800/30 rounded-lg border border-plasma-500/20 p-4 mb-4">
        <input
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter title with [Location] placeholder..."
          className="w-full text-xl md:text-2xl font-medium text-plasma-100 bg-transparent border-0 focus:outline-none focus:ring-0 placeholder-plasma-500/40 animate-glow px-2 py-2"
        />
        
        {/* Add template information display */}
        {currentTemplate && (
          <div className="mt-2 flex flex-wrap items-center gap-2">
            <div className="px-2 py-1 text-xs bg-plasma-500/10 text-plasma-300 rounded-md flex items-center gap-1">
              <span>Template:</span>
              <span className="font-medium">{currentTemplate.name}</span>
              <span>v{currentTemplate.version}</span>
            </div>
            <div className="text-xs text-plasma-300/70">
              {currentTemplate.metadata?.wordCount} words · {currentTemplate.metadata?.sectionCount} sections
            </div>
          </div>
        )}
      </div>

      {/* Content Structure - Made responsive */}
      <div className="flex-1 space-y-4">
        {/* Enhanced Content Structure Header with Cyberpunk-style UI */}
        <div className="mb-3 relative">
          {/* Title and quick add */}
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-medium text-plasma-100 flex items-center">
              <span className="h-5 w-1 bg-energy-500 mr-2 rounded-sm shadow-glow-sm"></span>
              Content Structure
            </h3>
            <div className="flex gap-2">
              {/* Expand/Collapse All button */}
              <Button
                variant="secondary"
                onClick={toggleAllSections}
                className="h-8 px-3 py-0 text-xs border-plasma-500/30 hover:border-plasma-500/70 transition-colors flex items-center"
                title={allSectionsExpanded ? "Collapse all sections" : "Expand all sections"}
              >
                {allSectionsExpanded ? (
                  <>
                    <ChevronsUp className="h-4 w-4 mr-1" />
                    <span>Collapse All</span>
                  </>
                ) : (
                  <>
                    <ChevronsDown className="h-4 w-4 mr-1" />
                    <span>Expand All</span>
                  </>
                )}
              </Button>
              <Button
                variant="energy"
                onClick={handleAddSection}
                icon={Plus}
                className="h-8 px-3 py-0 text-xs border-energy-500/30 hover:border-energy-500/70 shadow-glow-sm"
                title="Add a new content section"
              >
                <span>Add Section</span>
              </Button>
            </div>
          </div>
          
          {/* Cyberpunk control panel */}
          <div className="bg-bolt-900/60 backdrop-blur-sm border border-plasma-500/20 rounded-lg overflow-hidden">
            {/* Tab navigation */}
            <div className="flex border-b border-plasma-500/20">
              <div 
                className={`flex-1 px-4 py-2 text-xs font-mono cursor-pointer relative transition-colors ${!selectedModel ? 'text-red-400' : 'text-energy-300'}`}
                onClick={() => document.getElementById('model-select-input')?.focus()}
              >
                <div className={`absolute bottom-0 left-0 right-0 h-0.5 ${selectedModel ? 'bg-energy-500' : 'bg-red-500'}`}></div>
                <div className="flex items-center">
                  <div className={`h-2 w-2 rounded-full ${selectedModel ? 'bg-energy-500 animate-pulse shadow-md shadow-energy-500/30' : 'bg-gray-600'} mr-3`}></div>
                  {selectedModel ? (
                    <span className="truncate">
                      MODEL: {availableModels[selectedModel]?.name.split(':').pop() || 'SELECTED'}
                    </span>
                  ) : (
                    <span>MODEL REQUIRED</span>
                  )}
                </div>
              </div>
              
              <div 
                className="flex-1 px-4 py-2 text-xs font-mono cursor-pointer relative transition-colors text-plasma-300 hover:text-plasma-100"
                onClick={() => document.getElementById('header-shortcode-input')?.focus()}
              >
                <div className="flex items-center">
                  <div className={`h-2 w-2 rounded-full ${headerShortcode || footerShortcode ? 'bg-plasma-400' : 'bg-bolt-700'}`}></div>
                  <span>SHORTCODES</span>
                </div>
              </div>
              
              <div 
                className={`flex-1 px-4 py-2 text-xs font-mono cursor-pointer relative transition-colors ${currentTemplate ? 'text-plasma-100' : 'text-plasma-300 hover:text-plasma-100'}`}
              >
                <div className="flex items-center">
                  <div className={`h-2 w-2 rounded-full ${currentTemplate ? 'bg-plasma-400' : 'bg-bolt-700'}`}></div>
                  <span className="truncate">
                    {currentTemplate ? `TEMPLATE: ${currentTemplate.name.substring(0, 12)}${currentTemplate.name.length > 12 ? '...' : ''}` : 'TEMPLATES'}
                  </span>
                </div>
              </div>
            </div>
            
            {/* Control panel content */}
            <div className="grid grid-cols-1 md:grid-cols-6 gap-3 p-3">
              {/* MODEL CONTROL - 2 columns on MD+ */}
              <div className="md:col-span-2 space-y-2">
                <div className="relative">
                  {/* Decorative corners */}
                  <div className="absolute -top-1 -left-1 h-2 w-2 border-t border-l border-energy-500/50"></div>
                  <div className="absolute -top-1 -right-1 h-2 w-2 border-t border-r border-energy-500/50"></div>
                  
                  {/* Model filter with search icon */}
                  <div className="relative">
                    <input
                      id="model-filter-input"
                      type="text"
                      placeholder="Filter models..."
                      value={modelFilter}
                      onChange={(e) => setModelFilter(e.target.value.toLowerCase())}
                      className="w-full h-8 text-xs border-plasma-500/20 bg-bolt-800/70 border rounded-t pl-8 pr-2 text-plasma-100 focus:border-energy-500/50 transition-colors"
                    />
                    <div className="absolute left-2.5 top-2.5 text-plasma-500/70">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </div>
                  </div>
                  
                  {/* Enhanced select with custom dropdown styling */}
                  <div className="flex">
                    <select
                      id="model-select-input"
                      value={selectedModel || ''}
                      onChange={(e) => handleModelChange(e.target.value)}
                      className="w-full h-9 text-xs border-plasma-500/20 bg-bolt-800/70 border border-r-0 rounded-bl pl-2 pr-2 text-plasma-100 appearance-none focus:border-energy-500/50 transition-colors"
                      disabled={isLoadingModels}
                      required
                      style={{
                        backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%239089fc' stroke-width='2'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M19 9l-7 7-7-7' /%3E%3C/svg%3E")`,
                        backgroundPosition: 'right 0.5rem center',
                        backgroundRepeat: 'no-repeat',
                        backgroundSize: '1rem',
                      }}
                    >
                      {renderModelOptions()}
                    </select>
                    
                    {/* Refresh button with enhanced styling */}
                    <button
                      onClick={refreshModels}
                      disabled={isLoadingModels}
                      className="h-9 w-9 flex items-center justify-center bg-bolt-800/70 border border-plasma-500/20 rounded-br text-plasma-300 hover:text-energy-300 hover:border-energy-500/50 transition-colors"
                      title="Refresh model list"
                    >
                      {isLoadingModels ? 
                        <Loader className="h-4 w-4 animate-spin text-energy-400" /> : 
                        <RefreshCw className="h-4 w-4" />
                      }
                    </button>
                  </div>
                </div>
              </div>
              
              {/* SHORTCODES - 2 columns on MD+ */}
              <div className="md:col-span-2 space-y-2">
                <div className="relative">
                  {/* Decorative corners */}
                  <div className="absolute -top-1 -left-1 h-2 w-2 border-t border-l border-plasma-500/30"></div>
                  <div className="absolute -top-1 -right-1 h-2 w-2 border-t border-r border-plasma-500/30"></div>
                  
                  {/* Header shortcode with icon */}
                  <div className="flex items-center mb-2">
                    <div className="h-8 w-8 flex items-center justify-center bg-bolt-800/70 border border-r-0 border-plasma-500/20 rounded-l">
                      <span className="text-xs font-mono text-plasma-400">H</span>
                    </div>
                    <input
                      id="header-shortcode-input"
                      type="text"
                      value={headerShortcode}
                      onChange={(e) => setHeaderShortcode(e.target.value)}
                      placeholder="Header shortcode"
                      className="flex-1 h-8 text-xs bg-bolt-800/70 border border-plasma-500/20 rounded-r px-2 text-plasma-100 focus:border-plasma-500/50 transition-colors"
                    />
                  </div>
                  
                  {/* Footer shortcode with icon */}
                  <div className="flex items-center">
                    <div className="h-8 w-8 flex items-center justify-center bg-bolt-800/70 border border-r-0 border-plasma-500/20 rounded-l">
                      <span className="text-xs font-mono text-plasma-400">F</span>
                    </div>
                    <input
                      id="footer-shortcode-input"
                      type="text"
                      value={footerShortcode}
                      onChange={(e) => setFooterShortcode(e.target.value)}
                      placeholder="Footer shortcode"
                      className="flex-1 h-8 text-xs bg-bolt-800/70 border border-plasma-500/20 rounded-r px-2 text-plasma-100 focus:border-plasma-500/50 transition-colors"
                    />
                  </div>
                </div>
              </div>
              
              {/* TEMPLATE ACTIONS - 2 columns on MD+ */}
              <div className="md:col-span-2 space-y-2">
                <div className="relative">
                  {/* Decorative corners */}
                  <div className="absolute -top-1 -left-1 h-2 w-2 border-t border-l border-plasma-500/30"></div>
                  <div className="absolute -top-1 -right-1 h-2 w-2 border-t border-r border-plasma-500/30"></div>
                  
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      variant="secondary"
                      onClick={handleCreateNewTemplate}
                      className="h-8 text-xs px-2 border-plasma-500/30 hover:border-plasma-500/60 transition-colors"
                      title="Create a new blank template"
                    >
                      <span className="flex items-center justify-center text-xs">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        New Template
                      </span>
                    </Button>
                    
                    <Button
                      variant={sections.length === 0 ? "secondary" : "primary"}
                      onClick={handleShowSaveAsDefaultModal}
                      disabled={sections.length === 0}
                      className={`h-8 text-xs px-2 ${sections.length === 0 ? 'opacity-60' : 'shadow-glow-sm'}`}
                      title="Save as default template"
                    >
                      <span className="flex items-center justify-center text-xs">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h5a2 2 0 012 2v7a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h5v5.586l-1.293-1.293z" />
                        </svg>
                        Save Default
                      </span>
                    </Button>
                    
                    {currentTemplate && (
                      <Button
                        variant="secondary"
                        onClick={handleShowSaveVersionModal}
                        disabled={sections.length === 0}
                        className={`h-8 text-xs px-2 col-span-2 ${sections.length === 0 ? 'opacity-60' : ''}`}
                        title="Save as new version"
                      >
                        <span className="flex items-center justify-center text-xs">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z" />
                            <path fillRule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clipRule="evenodd" />
                          </svg>
                          Save as New Version
                        </span>
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Language Detection Model Selection - Optional secondary model */}
          <div className="bg-bolt-900/60 backdrop-blur-sm border border-plasma-500/20 rounded-lg overflow-hidden mt-3">
            <div className="flex items-center px-3 py-2 border-b border-plasma-500/20">
              <div className="h-2 w-2 rounded-full bg-plasma-500/50 mr-2"></div>
              <span className="text-xs font-mono text-plasma-300">LANGUAGE DETECTION MODEL (OPTIONAL)</span>
              <span className="ml-2 text-xs text-plasma-500/70">— Use a lighter model for language detection</span>
            </div>
            
            <div className="p-3">
              <div className="relative">
                {/* Language detection model filter */}
                <div className="relative mb-1">
                  <input
                    id="language-detection-filter-input"
                    type="text"
                    placeholder="Filter models..."
                    value={languageDetectionModelFilter}
                    onChange={(e) => setLanguageDetectionModelFilter(e.target.value.toLowerCase())}
                    className="w-full h-8 text-xs border-plasma-500/20 bg-bolt-800/70 border rounded-t pl-8 pr-2 text-plasma-100 focus:border-plasma-500/50 transition-colors"
                  />
                  <div className="absolute left-2.5 top-2.5 text-plasma-500/70">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                </div>
                
                {/* Language detection model select */}
                <select
                  id="language-detection-select-input"
                  value={languageDetectionModel || ''}
                  onChange={(e) => handleLanguageDetectionModelChange(e.target.value)}
                  className="w-full h-9 text-xs border-plasma-500/20 bg-bolt-800/70 border rounded-b pl-2 pr-8 text-plasma-100 appearance-none focus:border-plasma-500/50 transition-colors"
                  disabled={isLoadingModels}
                  style={{
                    backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%239089fc' stroke-width='2'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M19 9l-7 7-7-7' /%3E%3C/svg%3E")`,
                    backgroundPosition: 'right 0.5rem center',
                    backgroundRepeat: 'no-repeat',
                    backgroundSize: '1rem',
                  }}
                >
                  {renderLanguageDetectionModelOptions()}
                </select>
                
                {languageDetectionModel && (
                  <div className="mt-2 text-xs text-plasma-400">
                    Using: {availableModels[languageDetectionModel]?.name || languageDetectionModel}
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Add cyberpunk glow styles */}
          <style jsx="true" global="true">{`
            .shadow-glow-sm {
              box-shadow: 0 0 5px rgba(130, 87, 230, 0.5);
            }
            
            .animate-pulse {
              animation: pulse-glow 2s infinite;
            }
            
            @keyframes pulse-glow {
              0% {
                box-shadow: 0 0 0 0 rgba(130, 87, 230, 0.7);
              }
              70% {
                box-shadow: 0 0 0 6px rgba(130, 87, 230, 0);
              }
              100% {
                box-shadow: 0 0 0 0 rgba(130, 87, 230, 0);
              }
            }
          `}</style>
        </div>

        {/* Content Structure - Made responsive with better spacing */}
        <div className="bg-bolt-800/30 rounded-lg border border-plasma-500/20 p-3 md:p-4 overflow-y-auto custom-scrollbar">
          {sections.map((section, index) => (
            <SectionEditor
              key={section.id}
              section={section}
              level={0}
              onUpdate={handleUpdateSection}
              onDelete={handleDeleteSection}
              onAddSubsection={handleAddSubsection}
              onSelect={handleSectionSelect}
              isSelected={currentSection?.id === section.id}
              index={index}
              onMoveUp={() => handleMoveSection(section.id, 'up')}
              onMoveDown={() => handleMoveSection(section.id, 'down')}
              isMoving={movingSection === section.id}
              forceExpanded={allSectionsExpanded}
            />
          ))}
        </div>
        
        {/* Image Generator - Reintegrated into the content structure */}
        <div className="bg-bolt-800/30 rounded-lg border border-plasma-500/20 p-3 md:p-4 mt-4">
          <div className="mb-3 relative">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-plasma-100 flex items-center">
                <span className="h-5 w-1 bg-energy-500 mr-2 rounded-sm shadow-glow-sm"></span>
                Image Generator
              </h3>
            </div>
          </div>
          <div className="flex-1 overflow-y-auto">
            <ImageGenerator
              ref={imageGeneratorRef}
              contentTitle={title}
              onImageSelect={(url, blob) => {
                setFeaturedImageBlob(blob);
                setPreviousImages(prev => [...prev, { url, blob }]);
              }}
              previousImages={previousImages}
              sections={sections}
            />
          </div>
        </div>
      </div>

      {/* Batch progress indicator (for mobile) */}
      {(batchProgress.total > 0 && (
        batchProgress.status === 'running' || 
        batchProgress.status === 'paused'
      )) && (
        <div className="fixed bottom-20 right-4 max-w-md z-50">
          <BatchProgress
            {...batchProgress}
            onPause={handlePauseBatch}
            onResume={batchProgress.status === 'paused' ? handleResumeBatch : handleResumeCampaign}
            onStop={handleStopBatch}
          />
        </div>
      )}

      {hasSavedCampaign && batchProgress.total === 0 && batchProgress.status === 'idle' && (
        <div className="fixed bottom-20 right-4 z-40">
          <div className="flex space-x-2">
            <Button
              variant="energy"
              size="sm"
              onClick={handleResumeCampaign}
              className="flex items-center gap-1"
            >
              <RefreshCw size={14} />
              <span>Resume Campaign</span>
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => {
                if (window && window.confirm('Clear saved campaign?')) {
                  localStorage.removeItem('batchProcessorState');
                  window.location.reload(); // Refresh to show changes
                }
              }}
              className="flex items-center gap-1"
            >
              <span>Clear</span>
            </Button>
          </div>
        </div>
      )}

      {/* Add prompt editing UI */}
      {isEditingPrompt && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-100">
          <div className="bg-bolt-800 p-6 rounded-lg w-full max-w-2xl">
            <h3 className="text-lg font-medium mb-4">Edit Default Prompt</h3>
            <textarea
              value={editedPrompt}
              onChange={(e) => setEditedPrompt(e.target.value)}
              className="w-full bg-bolt-900 text-plasma-100 p-2 rounded mb-4"
              placeholder="Enter default prompt..."
            />
            <div className="flex justify-end gap-2">
              <Button
                variant="secondary"
                onClick={() => setIsEditingPrompt(false)}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleSavePrompt}
              >
                Save
              </Button>
            </div>
          </div>
        </div>
      )}
      
      {/* Add padding at the bottom to prevent content from being hidden behind the footer */}
      <div className="h-20 w-full"></div>

      {/* Save Default Template Modal */}
      {showSaveTemplateModal && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center p-4 z-100">
          <div className="bg-bolt-800 border border-plasma-500/30 p-6 rounded-lg w-full max-w-lg">
            <h3 className="text-lg font-medium mb-4 text-plasma-100">Save Default Template</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-plasma-200 mb-1">Template Name</label>
                <input
                  type="text"
                  value={templateName}
                  onChange={(e) => setTemplateName(e.target.value)}
                  className="w-full bg-bolt-900 text-plasma-100 p-2 rounded border border-bolt-600 focus:border-plasma-500 focus:outline-none"
                  placeholder="My Default Template"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-plasma-200 mb-1">Description</label>
                <textarea
                  value={templateDescription}
                  onChange={(e) => setTemplateDescription(e.target.value)}
                  className="w-full bg-bolt-900 text-plasma-100 p-2 rounded border border-bolt-600 focus:border-plasma-500 focus:outline-none h-24"
                  placeholder="Describe what this template is for..."
                />
              </div>
              <div className="flex justify-end gap-2 mt-4">
                <Button
                  variant="secondary"
                  onClick={() => setShowSaveTemplateModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={saveAsDefaultTemplate}
                  disabled={templateName.trim() === ''}
                >
                  Save Template
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Save New Version Modal */}
      {showSaveVersionModal && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center p-4 z-100">
          <div className="bg-bolt-800 border border-plasma-500/30 p-6 rounded-lg w-full max-w-lg">
            <h3 className="text-lg font-medium mb-4 text-plasma-100">Save New Version</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-plasma-200 mb-1">Change Description</label>
                <textarea
                  value={changeDescription}
                  onChange={(e) => setChangeDescription(e.target.value)}
                  className="w-full bg-bolt-900 text-plasma-100 p-2 rounded border border-bolt-600 focus:border-plasma-500 focus:outline-none h-24"
                  placeholder="Describe what changed in this version..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-plasma-200 mb-1">Version Type</label>
                <div className="flex gap-3">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="versionType"
                      checked={changeType === 'patch'}
                      onChange={() => setChangeType('patch')}
                      className="mr-2"
                    />
                    <span className="text-sm text-plasma-200">Patch (0.0.1)</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="versionType"
                      checked={changeType === 'minor'}
                      onChange={() => setChangeType('minor')}
                      className="mr-2"
                    />
                    <span className="text-sm text-plasma-200">Minor (0.1.0)</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="versionType"
                      checked={changeType === 'major'}
                      onChange={() => setChangeType('major')}
                      className="mr-2"
                    />
                    <span className="text-sm text-plasma-200">Major (1.0.0)</span>
                  </label>
                </div>
              </div>
              <div className="flex justify-end gap-2 mt-4">
                <Button
                  variant="secondary"
                  onClick={() => setShowSaveVersionModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={saveAsNewVersion}
                  disabled={changeDescription.trim() === ''}
                >
                  Save Version
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
