import { useConsoleStore } from '../stores/consoleStore';
import { neuroEnhancerService } from './neuroEnhancer';
import { directOpenAIService } from './directOpenAIService'; // Fixed import path
import { getSettings } from '../config/systemSettings';
import { Section } from '../types';
import { Location } from './locations';
import { translateText } from '../services/translation-service';

interface LocationWithPath extends Location {
  hierarchyPath: string;
  feature_class?: string;
  featureClass?: string;
}

interface TranslatedContent {
  language: string;
  sections: Section[];
  isMainLanguage: boolean;
}

class ContentTranslationService {
  async getLocationLanguages(location: Location, model?: string): Promise<string[]> {
    const consoleStore = useConsoleStore.getState();
    
    try {
      // Get system settings
      const settings = getSettings();
      
      // Strict validation - no fallback
      if (!model || model === '') {
        throw new Error('❌ OPERATION BLOCKED: No AI model selected. You MUST select a model first.');
      }
      
      // NO fallback - use the provided model
      const currentModel = model;
      
      // Make sure models properties exist
      const lmStudioModels = settings.lmStudio.models || {};
      const modelOptions = settings.models.options || {};
      
      // Use the same model detection logic as directOpenAIService for consistency
      const isLmStudioModel = settings.lmStudio.enabled && 
                            Object.keys(lmStudioModels).includes(currentModel);
      
      // OpenAI models should use the directOpenAIService
      // LM Studio models should be handled by neuroEnhancer
      const isDirectModel = !isLmStudioModel && (
        Object.keys(modelOptions).includes(currentModel) || 
        currentModel.includes('gpt') || 
        currentModel.includes('openai')
      );
      
      const enhancementService = isDirectModel ? directOpenAIService : neuroEnhancerService;
      
      // Get model display name for logging
      let modelName = currentModel;
      try {
        // Safely get model name
        if (isLmStudioModel && lmStudioModels[currentModel]?.name) {
          modelName = lmStudioModels[currentModel].name;
        } else if (modelOptions[currentModel]?.name) {
          modelName = modelOptions[currentModel].name;
        }
      } catch (e) {
        // Ignore errors when looking up model name
      }
      
      // Log which service is being used
      consoleStore.addMessage('info', `Using ${isDirectModel ? 'Direct OpenAI' : 'Neural Enhancer'} for language detection with model: ${modelName}`);

      // Bulletproof language detection prompt with guaranteed English inclusion
      const prompt = `You are a language detection expert with deep knowledge of global linguistics and geography.

TASK: Determine the native language(s) for this location and return a standardized JSON array.

LOCATION: ${location.name}
FULL PATH: ${location.hierarchyPath || location.fullPath || location.name}

CRITICAL RULES:
1. ALWAYS include the primary native language first
2. ALWAYS include "English" as the second language (regardless of actual local usage)
3. Return ONLY full language names, never codes (use "Chinese" not "zh", "Spanish" not "es")
4. Maximum 2 languages unless location is genuinely multilingual (3+ official languages)

EXCEPTION: If the primary native language IS English, return only ["English"]

STANDARDIZATION RULES:
- Use "English" not "American English", "British English", etc.
- Use "Chinese" for Mandarin/Cantonese (specify variant only if critical)
- Use "Spanish" not "Castilian"
- Use "Portuguese" not "Brazilian Portuguese"
- Use "Arabic" for Modern Standard Arabic
- Use "French" not regional variants
- Use "German" not "High German"
- For indigenous languages, use the most widely accepted English name

EXAMPLES:
- Beijing, China → ["Chinese", "English"]
- Tokyo, Japan → ["Japanese", "English"]
- Madrid, Spain → ["Spanish", "English"]
- São Paulo, Brazil → ["Portuguese", "English"]
- Cairo, Egypt → ["Arabic", "English"]
- London, UK → ["English"]
- New York, USA → ["English"]
- Montreal, Canada → ["French", "English"]
- Singapore → ["English", "Chinese", "Malay"]
- Zurich, Switzerland → ["German", "English"]

Return ONLY the JSON array with standardized language names.`;

      // Use the appropriate service for language detection
      const response = await enhancementService.detectLanguage({
        location: location,
        content: prompt,
        model: currentModel
      });

      console.log('Raw language response:', response.enhancedContent); // Debug log

      try {
        // Extract JSON array using regex
        const jsonMatch = response.enhancedContent.match(/\[.*?\]/);
        if (!jsonMatch) {
          consoleStore.addMessage('warning', `⚠️ No JSON array found in response for ${location.name}, attempting validation`);
          // Try to validate and recover from the raw response
          return await this.validateLanguageResponse(response.enhancedContent, location, currentModel);
        }

        const jsonArray = jsonMatch[0];
        console.log('Extracted JSON array:', jsonArray); // Debug log

        const languages = JSON.parse(jsonArray);
        console.log('Parsed languages:', languages); // Debug log

        // Check for language codes and validate
        const hasLanguageCodes = languages.some((lang: string) =>
          typeof lang === 'string' && lang.length <= 3 && /^[a-z]{2,3}$/i.test(lang.trim())
        );

        if (hasLanguageCodes) {
          consoleStore.addMessage('warning', `⚠️ Language codes detected for ${location.name}, converting to full names`);
          return await this.validateLanguageResponse(response.enhancedContent, location, currentModel);
        }

        // Standard validation and capitalization
        const validatedLanguages = languages.map((lang: string) => {
          // Ensure capitalization and trim whitespace
          return lang.trim().charAt(0).toUpperCase() + lang.trim().slice(1);
        });

        if (Array.isArray(validatedLanguages) && validatedLanguages.length > 0) {
          consoleStore.addMessage('success', `📊 Detected languages for ${location.name}: ${validatedLanguages.join(', ')}`);
          return validatedLanguages;
        }

        consoleStore.addMessage('warning', `⚠️ No specific languages detected for ${location.name}, using validation`);
        return await this.validateLanguageResponse(response.enhancedContent, location, currentModel);
      } catch (error) {
        console.error('Failed to parse languages:', error, 'Raw response:', response.enhancedContent);
        consoleStore.addMessage('warning', `⚠️ Error parsing languages for ${location.name}, attempting recovery`);
        return await this.validateLanguageResponse(response.enhancedContent, location, currentModel);
      }
    } catch (error) {
      console.error('Failed to analyze languages:', error);

      // Graceful degradation for token limit errors
      if (error instanceof Error && error.message && error.message.includes('Token limit exceeded')) {
        consoleStore.addMessage('warning', `⚠️ Content too large for language analysis of ${location.name}, attempting recovery`);
        return await this.recoverFromLanguageError(error.message, location, currentModel);
      }

      // Try intelligent recovery for other errors
      if (error instanceof Error) {
        consoleStore.addMessage('warning', `⚠️ Language detection failed for ${location.name}, attempting recovery`);
        return await this.recoverFromLanguageError(error.message, location, currentModel);
      }

      // Ultimate fallback
      consoleStore.addMessage('error', `❌ All language detection methods failed for ${location.name}, using English`);
      return ['English'];
    }
  }

  /**
   * Validates and standardizes language detection response using prompt-driven approach
   */
  private async validateLanguageResponse(rawResponse: string, location: Location, model: string): Promise<string[]> {
    const consoleStore = useConsoleStore.getState();

    try {
      const validationPrompt = `You are a language validation expert.

TASK: Validate and standardize this language detection result.

ORIGINAL RESPONSE: ${rawResponse}
LOCATION: ${location.name}

VALIDATION RULES:
1. Extract only valid language names (reject codes like "en", "es")
2. Standardize to internationally recognized names
3. Remove duplicates and invalid entries
4. Ensure logical consistency for the location
5. Maximum 3 languages unless location is truly multilingual

If the response contains language codes, convert them:
- "en" → "English"
- "es" → "Spanish"
- "fr" → "French"
- "de" → "German"
- "it" → "Italian"
- "pt" → "Portuguese"
- "ru" → "Russian"
- "zh" → "Chinese"
- "ja" → "Japanese"
- "ko" → "Korean"
- "ar" → "Arabic"

If the response is invalid or unclear, return ["English"] as fallback.

Return ONLY a clean JSON array of standardized language names.`;

      // Get system settings for model routing
      const settings = getSettings();
      const isDirectModel = !settings.lmStudio.enabled ||
                           !Object.keys(settings.lmStudio.models || {}).includes(model);
      const enhancementService = isDirectModel ? directOpenAIService : neuroEnhancerService;

      const response = await enhancementService.detectLanguage({
        location: location,
        content: validationPrompt,
        model: model
      });

      // Parse and return the validated result
      const jsonMatch = response.enhancedContent.match(/\[.*?\]/);
      if (jsonMatch) {
        const languages = JSON.parse(jsonMatch[0]);
        if (Array.isArray(languages) && languages.length > 0) {
          return languages.map((lang: string) =>
            lang.trim().charAt(0).toUpperCase() + lang.trim().slice(1)
          );
        }
      }
    } catch (error) {
      consoleStore.addMessage('warning', `⚠️ Validation failed for ${location.name}, using fallback`);
    }

    return ['English'];
  }

  /**
   * Intelligent error recovery using prompt-driven approach
   */
  private async recoverFromLanguageError(error: string, location: Location, model: string): Promise<string[]> {
    const consoleStore = useConsoleStore.getState();

    try {
      const recoveryPrompt = `You are a language expert helping recover from a detection error.

ERROR: ${error}
LOCATION: ${location.name}
LOCATION PATH: ${location.hierarchyPath || location.fullPath || location.name}

TASK: Based on your geographic and linguistic knowledge, determine the most likely languages for this location.

GUIDELINES:
- Use your training knowledge of world geography and languages
- Consider official languages, population demographics, and common usage
- Default to conservative estimates (1-2 languages max unless clearly multilingual)
- Always include English if it's commonly used for business/tourism

Return ONLY a JSON array of language names like ["Spanish"] or ["Hindi", "English"].`;

      // Get system settings for model routing
      const settings = getSettings();
      const isDirectModel = !settings.lmStudio.enabled ||
                           !Object.keys(settings.lmStudio.models || {}).includes(model);
      const enhancementService = isDirectModel ? directOpenAIService : neuroEnhancerService;

      const response = await enhancementService.detectLanguage({
        location: location,
        content: recoveryPrompt,
        model: model
      });

      const jsonMatch = response.enhancedContent.match(/\[.*?\]/);
      if (jsonMatch) {
        const languages = JSON.parse(jsonMatch[0]);
        if (Array.isArray(languages) && languages.length > 0) {
          consoleStore.addMessage('success', `🔄 Recovered languages for ${location.name}: ${languages.join(', ')}`);
          return languages.map((lang: string) =>
            lang.trim().charAt(0).toUpperCase() + lang.trim().slice(1)
          );
        }
      }
    } catch (recoveryError) {
      consoleStore.addMessage('warning', `⚠️ Recovery also failed for ${location.name}, using ultimate fallback`);
    }

    return ['English'];
  }

  async translateContent(sections: Section[], location: LocationWithPath, languageDetectionModel?: string, translationModel?: string): Promise<TranslatedContent[]> {
    const consoleStore = useConsoleStore.getState();
    
    try {
      // Extra logging to debug where the process is getting stuck
      consoleStore.addMessage('info', `🔍 Starting translation for ${location.name}`);
      console.log(`Starting translation for ${location.name} with ${sections.length} sections`);
      
      // Use languageDetectionModel for detecting languages
      const targetLanguages = await this.getLocationLanguages(location, languageDetectionModel);
      console.log(`Got target languages: ${targetLanguages.join(', ')}`);
      
      const translations: TranslatedContent[] = [];
      
      // Process primary language first
      const primaryLang = targetLanguages[0]; // Now a full language name like "Spanish"
      consoleStore.addMessage('info', `🌐 Processing primary language: ${primaryLang}`);

      // Always include English version
      const englishContent: TranslatedContent = {
        language: 'English', // Changed from 'en'
        sections: sections.map(section => ({
          ...section,
          language: 'English', // Changed from 'en'
          children: section.children?.map(child => ({
            ...child,
            language: 'English' // Changed from 'en'
          })) || []
        })),
        isMainLanguage: primaryLang === 'English' // Changed from 'en'
      };

      // If English is primary, add it first
      if (primaryLang === 'English') { // Changed from 'en'
        consoleStore.addMessage('success', `✅ English is primary language, no translation needed`);
        translations.push(englishContent);
        return translations;
      }

      // Translate to primary language if not English
      consoleStore.addMessage('info', `🔄 Translating content to ${primaryLang}...`);
      
      try {
        // Process sections sequentially instead of in parallel
        const primaryContent = [];
        for (let i = 0; i < sections.length; i++) {
          const section = sections[i];
          consoleStore.addMessage('info', `🔄 Translating section ${i+1}/${sections.length}: ${section.title}`);
          console.log(`Translating section ${i+1}/${sections.length}: ${section.title}`);
          
          try {
            // Translate title - USE TRANSLATION MODEL, NOT LANGUAGE DETECTION MODEL
            const translatedTitle = await translateText(section.title, 'English', primaryLang, translationModel);
            console.log(`Title translated: ${translatedTitle}`);
            
            // Translate content - USE TRANSLATION MODEL, NOT LANGUAGE DETECTION MODEL
            const translatedContent = await translateText(section.content, 'English', primaryLang, translationModel);
            console.log(`Content translated successfully for section ${i+1}`);
            
            // Process children sequentially
            const translatedChildren = [];
            for (let j = 0; j < (section.children || []).length; j++) {
              const child = section.children[j];
              try {
                console.log(`Translating child ${j+1} for section ${i+1}`);
                
                // Translate child title - USE TRANSLATION MODEL
                const childTitle = await translateText(child.title, 'English', primaryLang, translationModel);
                
                // Translate child content - USE TRANSLATION MODEL
                const childContent = await translateText(child.content, 'English', primaryLang, translationModel);
                
                translatedChildren.push({
                  ...child,
                  title: childTitle,
                  content: childContent,
                  language: primaryLang
                });
              } catch (childError) {
                console.error(`Error translating child ${j+1} for section ${i+1}:`, childError);
                
                // Return original child with language tag if translation fails
                translatedChildren.push({
                  ...child,
                  language: primaryLang
                });
              }
            }
            
            primaryContent.push({
              ...section,
              title: translatedTitle,
              content: translatedContent,
              language: primaryLang,
              children: translatedChildren
            });
          } catch (sectionError) {
            console.error(`Error translating section ${i+1}:`, sectionError);
            consoleStore.addMessage('warning', `⚠️ Error translating section ${i+1}, skipping translation for this section`);

            // FIXED: Don't add phantom content - skip failed sections
            // This prevents English content from being tagged with foreign language names
            // The English version will still be available in the englishContent object
          }
        }

        // Add primary language content
        translations.push({
          language: primaryLang,
          sections: primaryContent,
          isMainLanguage: true
        });

      } catch (translationError: unknown) {
        console.error('Error during primary language translation:', translationError);
        const errorMessage = translationError instanceof Error ? translationError.message : 'Unknown error';
        consoleStore.addMessage('error', `❌ Translation failed: ${errorMessage}, using English content only`);
        // If entire translation process fails, only return English
        return [englishContent];
      }

      // Add English version second
      translations.push(englishContent);
      
      consoleStore.addMessage('success', `✅ Translation completed for ${location.name}`);
      console.log(`Translation completed for ${location.name} with ${translations.length} language versions`);
      return translations;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      consoleStore.addMessage('error', `❌ Translation failed: ${errorMessage}`);
      console.error('Translation error:', error);
      
      // Return English content as fallback
      const fallbackContent: TranslatedContent = {
        language: 'English', // Changed from 'en'
        sections: sections.map(section => ({
          ...section,
          language: 'English', // Changed from 'en'
          children: section.children?.map(child => ({
            ...child,
            language: 'English' // Changed from 'en'
          })) || []
        })),
        isMainLanguage: true
      };
      
      return [fallbackContent];
    }
  }
}

export const contentTranslationService = new ContentTranslationService();
