import { useConsoleStore } from '../stores/consoleStore';
import { getSettings } from '../config/systemSettings';
import { DEFAULT_PROMPTS, MODEL_PRESETS } from '../config/defaultPrompts';
import { openRouterService } from './openRouterService';

// Use same interfaces as neuroEnhancer.ts
interface LocationContext {
  id: string;
  name: string;
  hierarchyPath: string;
  fullPath?: string;
  type: string;
}

interface SectionContext {
  title: string;
  content?: string;
  level: 'h1' | 'h2' | 'h3';
  type?: 'text';
  promptGuide: string;
  requirements: {
    wordCount: number;
  };
}

interface ContextualData {
  currentSection: SectionContext & {
    id: string;
  };
  siblingContexts: SectionContext[];
  parentContext?: SectionContext;
}

interface EnhanceOptions {
  content?: string;
  targetWordCount: 250 | 500 | 800 | 1200 | 1500 | 2000 | 2500 | 3000 | 3500 | 4000 | 4500 | 5000 | 5500 | 6000 | 6500 | 7000 | 7500 | 8000;
  locationContext: LocationContext;
  contextualData?: ContextualData;
  customPrompt?: string; // Add optional custom prompt parameter
  systemPrompt?: string; // Add optional system prompt parameter
  model?: string; // Add model parameter to interface
}

// Process prompt template with variables - using [Location] format consistently
function processPromptTemplate(template: string, data: any): string {
  let processed = template;
  
  // Basic replacements
  const replacements: Record<string, string> = {
    '{content}': data.content || '',
    '{targetWordCount}': data.targetWordCount?.toString() || ''
  };
  
  // Location context replacements - using [Location] format exclusively
  if (data.locationContext) {
    const locationName = data.locationContext.name || '';
    // USE FULLPATH INSTEAD OF HIERARCHYPATH FOR BETTER DISAMBIGUATION
    const locationPath = data.locationContext.fullPath || data.locationContext.hierarchyPath || '';
    
    // ENHANCED: Include hierarchy path in [Location] token when available
    // This ensures location disambiguation in all templates
    if (locationPath && locationPath !== locationName) {
      replacements['[Location]'] = `${locationName} (${locationPath})`;
    } else {
      replacements['[Location]'] = locationName;
    }
    
    // NEW: Add [LocationClean] token that ALWAYS uses just the friendly name
    // This gives clean content output (e.g., "Kenya" not "Kenya (Earth > Africa > Kenya)")
    // while preserving full disambiguation context for AI via system prompt
    replacements['[LocationClean]'] = locationName;
    
    // ALWAYS add hierarchy context to system prompt as well - NO FALLBACKS
    // This ensures AI gets full context in system prompt too
    if (locationPath && locationPath !== locationName && data.setLocationContextInfo) {
      data.setLocationContextInfo(`\n\nLocation Context: The location "${locationName}" is in: ${locationPath}`);
    }
    
    // Always set [LocationPath] for templates that explicitly use it
    replacements['[LocationPath]'] = locationPath;
    replacements['[LocationType]'] = data.locationContext.type || '';
  }
  
  // Section context replacements
  if (data.contextualData?.currentSection) {
    const section = data.contextualData.currentSection;
    replacements['{sectionTitle}'] = section.title || '';
    replacements['{sectionLevel}'] = section.level || '';
  }
  
  // Apply all replacements
  Object.entries(replacements).forEach(([key, value]) => {
    processed = processed.replace(new RegExp(key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), value);
  });
  
  return processed;
}

/**
 * Determines if a model should use the OpenRouter API
 */
function isOpenRouterModel(modelId: string): boolean {
  return modelId.startsWith('openrouter:');
}

/**
 * Determines if a model should use the LM Studio API
 */
function isLMStudioModel(modelId: string, settings: any): boolean {
  // Models with lmstudio: prefix always use LM Studio
  if (modelId.startsWith('lmstudio:')) {
    return true;
  }

  // If LM Studio is not enabled, don't use it
  if (!settings.lmStudio.enabled) {
    return false;
  }

  // Check if model is in the LM Studio models list
  const isRegisteredLMStudioModel = Object.keys(settings.lmStudio.models || {}).includes(modelId);

  // Check if model contains known LM Studio model identifiers
  const isKnownLMStudioModelType = 
    modelId.toLowerCase().includes('gemma') || 
    modelId.toLowerCase().includes('llama') || 
    modelId.toLowerCase().includes('mistral');

  return isRegisteredLMStudioModel || isKnownLMStudioModelType;
}

/**
 * Determines if a model should use the OpenAI API
 */
function isOpenAIModel(modelId: string): boolean {
  // All models that start with "gpt-" use OpenAI
  return modelId.startsWith('gpt-');
}

// Main service export
export const directOpenAIService = {
  async enhance(options: EnhanceOptions): Promise<{ enhancedContent: string }> {
    const {
      content,
      targetWordCount,
      locationContext,
      contextualData,
      customPrompt,
      systemPrompt,
      model
    } = options;
    
    const consoleStore = useConsoleStore.getState();
    const settings = getSettings();
    
    try {
      // Get the model ID - MUST be provided in options, no fallbacks
      if (!model || model === '') {
        throw new Error('❌ OPERATION BLOCKED: No AI model selected. You MUST select a model first.');
      }
      
      // Log the model being used
      consoleStore.addMessage('info', `🔄 Using model: ${model}`);
      
      // Determine if this is an LM Studio model
      const isLmStudio = model.startsWith('lmstudio:');
      const isOllama = model.startsWith('ollama:');
      const isOpenRouter = model.startsWith('openrouter:');
      const isAnthropicModel = model.startsWith('anthropic:');
      
      // Determine which API to use
      let apiEndpoint = '/api/direct-openai';
      if (isLmStudio) {
        apiEndpoint = '/api/lm-studio';
      } else if (isOllama) {
        apiEndpoint = '/api/ollama';
      } else if (isOpenRouter) {
        apiEndpoint = '/api/openrouter';
      }
      
      // Get model information - but NEVER block if we have a model
      let modelInfo: any = { id: model, name: model }; // Default values
      
      // Try to get additional info if available
      if (isLmStudio && settings.lmStudio.models[model]) {
        modelInfo = { ...modelInfo, ...settings.lmStudio.models[model] };
      } else if (isOllama && settings.ollama.models[model]) {
        modelInfo = { ...modelInfo, ...settings.ollama.models[model] };
      } else if (settings.models.options[model]) {
        modelInfo = settings.models.options[model];
      }
      
      const modelName = modelInfo?.name || model;
      
      consoleStore.addMessage('info', `🔄 Starting enhancement for [${locationContext.name}]...`);
      
      // Log detailed API call settings with ALL model-specific settings
      consoleStore.addMessage('info', `📝 API Call Settings for [${locationContext.name}]:`);
      consoleStore.addMessage('info', `   → Using Model: ${modelName}`);
      consoleStore.addMessage('info', `   → Provider: ${isLmStudio ? 'LM Studio (Local)' : isOllama ? 'Ollama (Local)' : isOpenRouter ? 'OpenRouter' : isAnthropicModel ? 'Anthropic' : 'OpenAI'}`);
      consoleStore.addMessage('info', `   → Model Description: ${modelInfo?.description || 'Custom model'}`);
      consoleStore.addMessage('info', `   → Default Temperature: ${modelInfo?.defaultTemp}`);
      consoleStore.addMessage('info', `   → Applied Temperature: ${settings.generationParams.temperature}`);
      consoleStore.addMessage('info', `   → Top P: ${settings.generationParams.topP}`);
      consoleStore.addMessage('info', `   → Frequency Penalty: ${settings.generationParams.frequencyPenalty}`);
      consoleStore.addMessage('info', `   → Presence Penalty: ${settings.generationParams.presencePenalty}`);
      
      // Build system prompt
      let systemPromptMessage = systemPrompt || 
                                (MODEL_PRESETS[model as keyof typeof MODEL_PRESETS]?.systemPrompt) || 
                                DEFAULT_PROMPTS.system;
      
      // CRITICAL FIX: Prioritize getting the prompt from contextualData first
      // This ensures we get the full Prompt Instructions from the UI
      let promptSource = '';
      
      // First try to get the full prompt instructions from the section
      if (contextualData?.currentSection?.promptGuide) {
        promptSource = contextualData.currentSection.promptGuide;
        consoleStore.addMessage('info', `📝 Using Prompt Instructions from section (${promptSource.length} chars)`);
      } 
      // If there's a custom prompt, use that as a fallback
      else if (customPrompt) {
        promptSource = customPrompt;
        consoleStore.addMessage('info', `📝 Using Custom Prompt (${promptSource.length} chars)`);
      }
      // Last resort: use the content itself if available
      else if (content) {
        promptSource = content;
        consoleStore.addMessage('info', `📝 Using Content as prompt (${promptSource.length} chars)`);
      }
      
      // Verify we have something to work with
      if (!promptSource) {
        throw new Error('No prompt instructions or content provided for enhancement');
      }
      
            // Process the promptSource with the template engine to handle variables
      // Use a separate variable to collect any additional context info
      let locationContextInfo = '';
      const processedPrompt = processPromptTemplate(promptSource, {
        targetWordCount,
        locationContext,
        contextualData,
        // Use this property to collect location context info
        setLocationContextInfo: (info: string) => {
          locationContextInfo = info;
        }
      });
      
      // Add location context to system prompt if available
      if (locationContextInfo) {
        systemPromptMessage += locationContextInfo;
        
        // Add verification in console showing exact context being used
        const pathUsed = locationContext.fullPath ? 'fullPath' : 'hierarchyPath';
        const actualPath = locationContext.fullPath || locationContext.hierarchyPath || 'NONE';
        consoleStore.addMessage('info', `📍 LOCATION CONTEXT: Using "${locationContext.name}" with ${pathUsed}: ${actualPath}`);
      }
      
      // Use the processed prompt as the user prompt
      const userPrompt = processedPrompt;
      consoleStore.addMessage('info', `Using full prompt for [${locationContext.name}], ${userPrompt.length} characters`);
      
      // Update logging
      consoleStore.addMessage('info', `   → Content Length: ${(content || '').length} chars`);
      consoleStore.addMessage('info', `   → Prompt Source Length: ${promptSource.length} chars`);
      consoleStore.addMessage('info', `   → Processed Prompt Length: ${userPrompt.length} chars`);
      consoleStore.addMessage('info', `   → Target Words: ${targetWordCount}`);
      
      // Prepare common request body
      const requestBody: any = {
        model: model,
        messages: [
          // Absolute minimal system prompt
          { role: 'system', content: systemPromptMessage },
          // User's exact prompt with only location variables processed
          { role: 'user', content: userPrompt }
        ],
        temperature: settings.generationParams.temperature,
        top_p: settings.generationParams.topP,
        frequency_penalty: settings.generationParams.frequencyPenalty,
        presence_penalty: settings.generationParams.presencePenalty
      };
      
      // Add provider-specific parameters
      if (isLmStudio) {
        // Add LM Studio specific parameters
        requestBody.serverUrl = settings.lmStudio.serverUrl;
        // Only add these if they exist
        if (modelInfo.topK) requestBody.top_k = modelInfo.topK;
        if (modelInfo.repeatPenalty) requestBody.repeat_penalty = modelInfo.repeatPenalty;
      } else if (isOllama) {
        // Add Ollama specific parameters
        requestBody.serverUrl = settings.ollama.serverUrl;
      } else if (isOpenRouter) {
        // Use the OpenRouter API directly from the service
        try {
          const openRouterResponse = await openRouterService.completion({
            model: model,
            messages: requestBody.messages,
            temperature: settings.generationParams.temperature,
            max_tokens: settings.generationParams.maxTokens,
            top_p: settings.generationParams.topP,
            frequency_penalty: settings.generationParams.frequencyPenalty,
            presence_penalty: settings.generationParams.presencePenalty
          });
          
          // Parse response in the same format as our other APIs
          if (openRouterResponse.choices && openRouterResponse.choices.length > 0) {
            const enhancedContent = openRouterResponse.choices[0].message.content.trim();
            
            // Validate content (optional)
            const validation = validateContent(enhancedContent, locationContext.name, targetWordCount);
            if (!validation.isValid) {
              consoleStore.addMessage('warning', `⚠️ Content may need improvement: ${validation.issues.join(', ')}`);
            }
            
            consoleStore.addMessage('success', `✅ Content enhanced successfully with ${modelName}`);
            return { enhancedContent };
          } else {
            throw new Error('Invalid response format from OpenRouter API');
          }
        } catch (error: any) {
          consoleStore.addMessage('error', `❌ OpenRouter API error: ${error.message}`);
          throw error;
        }
      } else if (isAnthropicModel) {
        // Route to Anthropic API
        consoleStore.addMessage('info', `☁️ Using Anthropic API for ${model}`);
        try {
          // Use our internal Anthropic endpoint
          const apiEndpoint = '/api/anthropic';
          const anthropicRequestBody = {
            model: model.replace('anthropic:', ''),
            content: userPrompt,
            systemPrompt: systemPromptMessage,
          };
          
          const response = await fetch(apiEndpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(anthropicRequestBody)
          });
          
          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(`Anthropic API error: ${errorData?.error?.message || response.statusText || 'Unknown error'}`);
          }
          
          const result = await response.json();
          const enhancedContent = result.content || result.enhancedContent;
          
          if (enhancedContent) {
            consoleStore.addMessage('success', `✅ Content enhanced successfully with ${modelName}`);
            return { enhancedContent };
          } else {
            throw new Error('Invalid response format from Anthropic API');
          }
        } catch (error: any) {
          consoleStore.addMessage('error', `❌ Anthropic API error: ${error.message}`);
          throw error;
        }
      } else {
        // Add OpenAI specific parameters
        requestBody.response_format = { type: "text" };
      }
      
      // Make API request through appropriate server proxy
      const startTime = Date.now();
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API error: ${errorData?.error?.message || response.statusText || 'Unknown error'}`);
      }

      const data = await response.json();
      const processingTime = Date.now() - startTime;
      
      // Log completion with metadata
      consoleStore.addMessage('success', `✅ Enhancement completed in ${(processingTime/1000).toFixed(2)}s`);
      
      if (data.usage) {
        consoleStore.addMessage('info', `   → Tokens Used: ${data.usage.total_tokens} (prompt: ${data.usage.prompt_tokens}, completion: ${data.usage.completion_tokens})`);
      }
      
      // Parse response based on server proxy format
      let enhancedContent = '';
      
      // Handle different response formats from proxy
      if (data.content) {
        // Server proxy format
        enhancedContent = data.content.trim();
      } else if (data.choices && data.choices.length > 0 && data.choices[0].message) {
        // Direct API format
        enhancedContent = data.choices[0].message.content.trim();
      } else {
        consoleStore.addMessage('error', '❌ Invalid response format from API');
        throw new Error('Invalid response format from API');
      }
      
      // Content quality check
      if (enhancedContent.length < targetWordCount * 3) { // Roughly checking if content is too short (avg 5 chars per word)
        consoleStore.addMessage('warning', `⚠️ Generated content may be shorter than requested (${enhancedContent.length} chars)`);
      }
      
      // Validate generated content
      const validationResult = validateContent(enhancedContent, locationContext.name, targetWordCount);
      
      // If content doesn't meet requirements, add a note
      if (!validationResult.isValid) {
        consoleStore.addMessage('warning', `⚠️ Content validation issues: ${validationResult.issues.join(', ')}`);
      }
      
      consoleStore.addMessage('success', `✅ Enhancement complete for [${locationContext.name}]`);
      return { enhancedContent };
    } catch (error: any) {
      consoleStore.addMessage('error', `❌ Enhancement failed: ${error.message}`);
      throw error;
    }
  },

  /**
   * Detect the language of a location using AI
   */
  async detectLanguage({ 
    location, 
    content, 
    model 
  }: { 
    location: any; 
    content: string;
    model?: string;
  }): Promise<{ enhancedContent: string }> {
    const consoleStore = useConsoleStore.getState();
    const settings = getSettings();
    let enhancedContent = '';
    
    // Extract location info outside try block for use in catch
    const locationName = location.name || '';
    const locationPath = location.fullPath || location.hierarchyPath || '';
    const locationDisplay = locationPath && locationPath !== locationName ? 
      `${locationName} (${locationPath})` : locationName;
    
    try {
      // Get the model ID - MUST be provided in options, no fallbacks
      if (!model || model === '') {
        throw new Error('❌ OPERATION BLOCKED: No AI model selected. You MUST select a model first.');
      }
      
      // Determine if this is an LM Studio model
      const isLmStudio = model.startsWith('lmstudio:');
      const isOllama = model.startsWith('ollama:');
      const isOpenRouter = model.startsWith('openrouter:');
      
      // Determine which API to use
      let apiEndpoint = '/api/direct-openai';
      if (isLmStudio) {
        apiEndpoint = '/api/lm-studio';
      } else if (isOllama) {
        apiEndpoint = '/api/ollama';
      } else if (isOpenRouter) {
        apiEndpoint = '/api/openrouter';
      }
      
      // Get model display name for logging
      let modelName = model;
      if (isLmStudio && settings.lmStudio.models[model]?.name) {
        modelName = settings.lmStudio.models[model].name;
      } else if (isOllama && settings.ollama.models[model]?.name) {
        modelName = settings.ollama.models[model].name;
      } else if (settings.models.options[model]?.name) {
        modelName = settings.models.options[model].name;
      }
      
      consoleStore.addMessage('info', `🔍 Analyzing language for [${locationDisplay}] with ${modelName}...`);
      
      // Build bulletproof system prompt for language detection
      const systemPromptMessage =
        "You are a language detection AI. CRITICAL RULES: 1) ALWAYS include the primary native language first, 2) ALWAYS include 'English' as second language (regardless of local usage), 3) EXCEPTION: If primary language IS English, return only ['English'], 4) Use full names never codes, 5) Use hierarchical path for disambiguation (e.g., 'Georgia' in 'United States/Georgia' = English-speaking state vs 'Europe/Georgia' = Georgian-speaking country). Return ONLY JSON array like ['Chinese','English'] or ['English'].";
      
      // We've already extracted the location display information above
      
      // Add location hierarchy context to content if not already included
      let enhancedContent;
      if (locationPath && locationPath !== locationName) {
        enhancedContent = `## CRITICAL LOCATION INFORMATION ##\nFULL LOCATION PATH: ${locationPath}\nLOCATION NAME: ${locationName}\n\nThis is specifically about ${locationName} at path ${locationPath}.\n\n${content}`;
      } else {
        enhancedContent = content;
      }
      
      // Prepare common request body
      const requestBody: any = {
        model: model,
        messages: [
          { role: 'system', content: systemPromptMessage },
          { role: 'user', content: enhancedContent }
        ],
        temperature: 0.2, // Use lower temperature for factual language detection
        top_p: 0.95,
        frequency_penalty: 0,
        presence_penalty: 0
      };
      
      // If it's an OpenRouter model, use the OpenRouter service directly
      if (isOpenRouter) {
        // Check if OpenRouter is configured
        if (!openRouterService.isConfigured()) {
          throw new Error('OpenRouter API key not configured. Please add your API key to the .env file.');
        }
        
        try {
          const openRouterResponse = await openRouterService.completion({
            model: model,
            messages: requestBody.messages,
            temperature: 0.2,
            max_tokens: 500, // Reasonable default for language detection
            top_p: 0.95,
            frequency_penalty: 0,
            presence_penalty: 0
          });
          
          if (openRouterResponse.choices && openRouterResponse.choices.length > 0) {
            const enhancedContent = openRouterResponse.choices[0].message.content.trim();
            consoleStore.addMessage('success', `✅ Language analysis complete for [${locationDisplay}]`);
            return { enhancedContent };
          } else {
            throw new Error('Invalid response format from OpenRouter API');
          }
        } catch (error: any) {
          consoleStore.addMessage('error', `❌ OpenRouter API error: ${error.message}`);
          throw error;
        }
      }
      
      // Make API request
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API error: ${errorData?.error?.message || response.statusText || 'Unknown error'}`);
      }

      const data = await response.json();
      
      // Parse response based on server proxy format
      let responseContent = '';
      
      // Handle different response formats from proxy
      if (data.content) {
        // Server proxy format
        responseContent = data.content.trim();
      } else if (data.choices && data.choices.length > 0 && data.choices[0].message) {
        // Direct API format
        responseContent = data.choices[0].message.content.trim();
      } else {
        consoleStore.addMessage('error', '❌ Invalid response format from API');
        throw new Error('Invalid response format from API');
      }
      
      consoleStore.addMessage('success', `✅ Language analysis complete for [${location.name}]`);
      return { enhancedContent: responseContent };
    } catch (error: any) {
      consoleStore.addMessage('error', `❌ Language analysis failed for [${locationDisplay}]: ${error.message}`);
      throw error;
    }
  }
};

/**
 * Validates the generated content against quality requirements
 */
function validateContent(content: string, locationName: string, targetWordCount: number): { 
  isValid: boolean; 
  issues: string[];
} {
  const issues: string[] = [];
  
  // Check actual word count against target
  const wordCount = content.split(/\s+/).length;
  const wordCountDifference = Math.abs(wordCount - targetWordCount);
  const wordCountPercentageDiff = (wordCountDifference / targetWordCount) * 100;
  
  if (wordCountPercentageDiff > 15) {
    issues.push(`Word count (${wordCount}) differs significantly from target (${targetWordCount})`);
  }
  
  // Check first-person voice usage (we, our, us)
  const firstPersonPronouns = ['we', 'our', 'us'];
  let firstPersonCount = 0;
  
  firstPersonPronouns.forEach(pronoun => {
    const regex = new RegExp(`\\b${pronoun}\\b`, 'gi');
    const matches = content.match(regex);
    if (matches) {
      firstPersonCount += matches.length;
    }
  });
  
  if (firstPersonCount < 5) {
    issues.push('Limited use of first-person perspective (we, our, us)');
  }
  
  // Check for section headings in longer content
  if (targetWordCount >= 800 && !content.includes('#')) {
    issues.push('Missing section headings in long-form content');
  }
  
  return {
    isValid: issues.length === 0,
    issues
  };
}
